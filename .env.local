# NextAuth Configuration
# NEXTAUTH_URL=http://localhost:3001  # Local development (commented out for ngrok)
NEXTAUTH_URL=https://3745-98-42-139-155.ngrok-free.app
NEXTAUTH_SECRET=RAsFUaG4n12jSZousPXzEr9/AvLayGptTNOsi1yyzk8=

# Supabase Configuration
# Get these from: https://supabase.com/dashboard/project/YOUR_PROJECT/settings/api
NEXT_PUBLIC_SUPABASE_URL=https://aeufphhufxbtjekzzorj.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFldWZwaGh1ZnhidGpla3p6b3JqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwNzYwODYsImV4cCI6MjA2NDY1MjA4Nn0.gnkzEpSLZgOhn0wv5mCy5zKtmpp4-zpswDMuAsxTWp4
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFldWZwaGh1ZnhidGpla3p6b3JqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTA3NjA4NiwiZXhwIjoyMDY0NjUyMDg2fQ.uv97SsbdlaxTFMnjmHMEHIurFOMeHqsxsRBQ7MJdKUQ


# Google OAuth
# Get these from: https://console.cloud.google.com/apis/credentials
GOOGLE_CLIENT_ID=889614042425-1svrhoh73b8uga7kl2hsvu4g9kpcdnpu.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Slack OAuth (Optional)
SLACK_CLIENT_ID=92999792886.9025899880800
SLACK_CLIENT_SECRET=877ca49a5fc1da828d492696ccebda63

# Encryption Key for storing credentials
ENCRYPTION_KEY=34437be7a631b6137d89e283e2990626
