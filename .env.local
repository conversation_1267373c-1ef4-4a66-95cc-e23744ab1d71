# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=RAsFUaG4n12jSZousPXzEr9/AvLayGptTNOsi1yyzk8=

# Supabase Configuration
# Get these from: https://supabase.com/dashboard/project/YOUR_PROJECT/settings/api
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Google OAuth
# Get these from: https://console.cloud.google.com/apis/credentials
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Slack OAuth (Optional)
SLACK_CLIENT_ID=
SLACK_CLIENT_SECRET=

# Encryption Key for storing credentials
ENCRYPTION_KEY=34437be7a631b6137d89e283e2990626
