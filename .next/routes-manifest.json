{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/api/tools/customers/[id]", "regex": "^/api/tools/customers/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/tools/customers/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/tools/calendar-to-sheets", "regex": "^/tools/calendar\\-to\\-sheets(?:/)?$", "routeKeys": {}, "namedRegex": "^/tools/calendar\\-to\\-sheets(?:/)?$"}, {"page": "/tools/customer-manager", "regex": "^/tools/customer\\-manager(?:/)?$", "routeKeys": {}, "namedRegex": "^/tools/customer\\-manager(?:/)?$"}, {"page": "/tools/gmail-to-sheets", "regex": "^/tools/gmail\\-to\\-sheets(?:/)?$", "routeKeys": {}, "namedRegex": "^/tools/gmail\\-to\\-sheets(?:/)?$"}, {"page": "/tools/slack-to-sheets", "regex": "^/tools/slack\\-to\\-sheets(?:/)?$", "routeKeys": {}, "namedRegex": "^/tools/slack\\-to\\-sheets(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}