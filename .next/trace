[{"name": "hot-reloader", "duration": 59, "timestamp": 21483560190, "id": 3, "tags": {"version": "15.3.3"}, "startTime": 1749076620098, "traceId": "0c160f797823bf29"}, {"name": "setup-dev-bundler", "duration": 593825, "timestamp": 21483354839, "id": 2, "parentId": 1, "tags": {}, "startTime": 1749076619893, "traceId": "0c160f797823bf29"}, {"name": "run-instrumentation-hook", "duration": 17, "timestamp": 21483980538, "id": 4, "parentId": 1, "tags": {}, "startTime": 1749076620519, "traceId": "0c160f797823bf29"}, {"name": "start-dev-server", "duration": 973301, "timestamp": 21483027439, "id": 1, "tags": {"cpus": "16", "platform": "darwin", "memory.freeMem": "75071881216", "memory.totalMem": "137438953472", "memory.heapSizeLimit": "68769808384", "memory.rss": "309395456", "memory.heapTotal": "101859328", "memory.heapUsed": "68348264"}, "startTime": 1749076619566, "traceId": "0c160f797823bf29"}, {"name": "compile-path", "duration": 2091446, "timestamp": 21504669050, "id": 7, "tags": {"trigger": "/"}, "startTime": 1749076641207, "traceId": "0c160f797823bf29"}, {"name": "ensure-page", "duration": 2092827, "timestamp": 21504668275, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1749076641206, "traceId": "0c160f797823bf29"}]