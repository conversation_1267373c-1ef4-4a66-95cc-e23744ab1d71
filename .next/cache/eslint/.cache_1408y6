[{"/Users/<USER>/Documents/GitHub/aris/src/app/api/auth/[...nextauth]/route.ts": "1", "/Users/<USER>/Documents/GitHub/aris/src/app/api/auth/slack/route.ts": "2", "/Users/<USER>/Documents/GitHub/aris/src/app/api/credentials/route.ts": "3", "/Users/<USER>/Documents/GitHub/aris/src/app/api/health/route.ts": "4", "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/calendar/create-spreadsheet/route.ts": "5", "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/calendar/events/route.ts": "6", "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/calendar/update-spreadsheet/route.ts": "7", "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/customers/[id]/route.ts": "8", "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/customers/route.ts": "9", "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/slack/channels/route.ts": "10", "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/slack/connect-channel/route.ts": "11", "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/slack/connections/route.ts": "12", "/Users/<USER>/Documents/GitHub/aris/src/app/layout.tsx": "13", "/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx": "14", "/Users/<USER>/Documents/GitHub/aris/src/app/providers.tsx": "15", "/Users/<USER>/Documents/GitHub/aris/src/app/settings/page.tsx": "16", "/Users/<USER>/Documents/GitHub/aris/src/app/tools/calendar-to-sheets/page.tsx": "17", "/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx": "18", "/Users/<USER>/Documents/GitHub/aris/src/app/tools/gmail-to-sheets/page.tsx": "19", "/Users/<USER>/Documents/GitHub/aris/src/app/tools/slack-to-sheets/page.tsx": "20", "/Users/<USER>/Documents/GitHub/aris/src/lib/database.ts": "21", "/Users/<USER>/Documents/GitHub/aris/src/lib/encryption.ts": "22"}, {"size": 2166, "mtime": 1749089548453, "results": "23", "hashOfConfig": "24"}, {"size": 3216, "mtime": 1749081696200, "results": "25", "hashOfConfig": "24"}, {"size": 3324, "mtime": 1749076377374, "results": "26", "hashOfConfig": "24"}, {"size": 817, "mtime": 1749076921186, "results": "27", "hashOfConfig": "24"}, {"size": 4850, "mtime": 1749076432416, "results": "28", "hashOfConfig": "24"}, {"size": 3076, "mtime": 1749076417049, "results": "29", "hashOfConfig": "24"}, {"size": 4373, "mtime": 1749076447612, "results": "30", "hashOfConfig": "24"}, {"size": 3795, "mtime": 1749081470413, "results": "31", "hashOfConfig": "24"}, {"size": 2301, "mtime": 1749081438740, "results": "32", "hashOfConfig": "24"}, {"size": 2708, "mtime": 1749081708746, "results": "33", "hashOfConfig": "24"}, {"size": 6239, "mtime": 1749081738388, "results": "34", "hashOfConfig": "24"}, {"size": 1406, "mtime": 1749081717513, "results": "35", "hashOfConfig": "24"}, {"size": 798, "mtime": 1749076334960, "results": "36", "hashOfConfig": "24"}, {"size": 3026, "mtime": 1749089848125, "results": "37", "hashOfConfig": "24"}, {"size": 217, "mtime": 1749076344508, "results": "38", "hashOfConfig": "24"}, {"size": 8602, "mtime": 1749081749286, "results": "39", "hashOfConfig": "24"}, {"size": 10181, "mtime": 1749076404922, "results": "40", "hashOfConfig": "24"}, {"size": 13587, "mtime": 1749081601045, "results": "41", "hashOfConfig": "24"}, {"size": 5869, "mtime": 1749076529428, "results": "42", "hashOfConfig": "24"}, {"size": 10735, "mtime": 1749081787160, "results": "43", "hashOfConfig": "24"}, {"size": 3263, "mtime": 1749081414243, "results": "44", "hashOfConfig": "24"}, {"size": 1726, "mtime": 1749076245836, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1dhs2hn", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 13, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/GitHub/aris/src/app/api/auth/[...nextauth]/route.ts", ["112"], [], "/Users/<USER>/Documents/GitHub/aris/src/app/api/auth/slack/route.ts", [], [], "/Users/<USER>/Documents/GitHub/aris/src/app/api/credentials/route.ts", [], [], "/Users/<USER>/Documents/GitHub/aris/src/app/api/health/route.ts", [], [], "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/calendar/create-spreadsheet/route.ts", [], [], "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/calendar/events/route.ts", ["113"], [], "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/calendar/update-spreadsheet/route.ts", ["114"], [], "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/customers/[id]/route.ts", [], [], "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/customers/route.ts", ["115"], [], "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/slack/channels/route.ts", ["116", "117", "118"], [], "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/slack/connect-channel/route.ts", [], [], "/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/slack/connections/route.ts", ["119"], [], "/Users/<USER>/Documents/GitHub/aris/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx", [], [], "/Users/<USER>/Documents/GitHub/aris/src/app/providers.tsx", [], [], "/Users/<USER>/Documents/GitHub/aris/src/app/settings/page.tsx", ["120", "121", "122", "123"], [], "/Users/<USER>/Documents/GitHub/aris/src/app/tools/calendar-to-sheets/page.tsx", ["124"], [], "/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx", [], [], "/Users/<USER>/Documents/GitHub/aris/src/app/tools/gmail-to-sheets/page.tsx", ["125", "126", "127", "128", "129", "130", "131", "132", "133", "134", "135", "136", "137"], [], "/Users/<USER>/Documents/GitHub/aris/src/app/tools/slack-to-sheets/page.tsx", [], [], "/Users/<USER>/Documents/GitHub/aris/src/lib/database.ts", ["138", "139"], [], "/Users/<USER>/Documents/GitHub/aris/src/lib/encryption.ts", ["140", "141", "142"], [], {"ruleId": "143", "severity": 2, "message": "144", "line": 21, "column": 35, "nodeType": null, "messageId": "145", "endLine": 21, "endColumn": 42}, {"ruleId": "143", "severity": 2, "message": "146", "line": 7, "column": 27, "nodeType": null, "messageId": "145", "endLine": 7, "endColumn": 34}, {"ruleId": "147", "severity": 2, "message": "148", "line": 72, "column": 42, "nodeType": "149", "messageId": "150", "endLine": 72, "endColumn": 45, "suggestions": "151"}, {"ruleId": "143", "severity": 2, "message": "146", "line": 5, "column": 27, "nodeType": null, "messageId": "145", "endLine": 5, "endColumn": 34}, {"ruleId": "143", "severity": 2, "message": "146", "line": 6, "column": 27, "nodeType": null, "messageId": "145", "endLine": 6, "endColumn": 34}, {"ruleId": "147", "severity": 2, "message": "148", "line": 44, "column": 25, "nodeType": "149", "messageId": "150", "endLine": 44, "endColumn": 28, "suggestions": "152"}, {"ruleId": "147", "severity": 2, "message": "148", "line": 45, "column": 22, "nodeType": "149", "messageId": "150", "endLine": 45, "endColumn": 25, "suggestions": "153"}, {"ruleId": "143", "severity": 2, "message": "146", "line": 5, "column": 27, "nodeType": null, "messageId": "145", "endLine": 5, "endColumn": 34}, {"ruleId": "147", "severity": 2, "message": "148", "line": 29, "column": 49, "nodeType": "149", "messageId": "150", "endLine": 29, "endColumn": 52, "suggestions": "154"}, {"ruleId": "147", "severity": 2, "message": "148", "line": 30, "column": 51, "nodeType": "149", "messageId": "150", "endLine": 30, "endColumn": 54, "suggestions": "155"}, {"ruleId": "147", "severity": 2, "message": "148", "line": 35, "column": 49, "nodeType": "149", "messageId": "150", "endLine": 35, "endColumn": 52, "suggestions": "156"}, {"ruleId": "147", "severity": 2, "message": "148", "line": 36, "column": 51, "nodeType": "149", "messageId": "150", "endLine": 36, "endColumn": 54, "suggestions": "157"}, {"ruleId": "143", "severity": 2, "message": "158", "line": 5, "column": 43, "nodeType": null, "messageId": "145", "endLine": 5, "endColumn": 51}, {"ruleId": "143", "severity": 2, "message": "159", "line": 3, "column": 20, "nodeType": null, "messageId": "145", "endLine": 3, "endColumn": 29}, {"ruleId": "143", "severity": 2, "message": "160", "line": 5, "column": 16, "nodeType": null, "messageId": "145", "endLine": 5, "endColumn": 31}, {"ruleId": "143", "severity": 2, "message": "161", "line": 17, "column": 17, "nodeType": null, "messageId": "145", "endLine": 17, "endColumn": 24}, {"ruleId": "162", "severity": 2, "message": "163", "line": 105, "column": 45, "nodeType": "164", "messageId": "165", "suggestions": "166"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 105, "column": 51, "nodeType": "164", "messageId": "165", "suggestions": "167"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 105, "column": 54, "nodeType": "164", "messageId": "165", "suggestions": "168"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 105, "column": 63, "nodeType": "164", "messageId": "165", "suggestions": "169"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 105, "column": 66, "nodeType": "164", "messageId": "165", "suggestions": "170"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 105, "column": 81, "nodeType": "164", "messageId": "165", "suggestions": "171"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 148, "column": 25, "nodeType": "164", "messageId": "165", "suggestions": "172"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 148, "column": 32, "nodeType": "164", "messageId": "165", "suggestions": "173"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 149, "column": 48, "nodeType": "164", "messageId": "165", "suggestions": "174"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 149, "column": 67, "nodeType": "164", "messageId": "165", "suggestions": "175"}, {"ruleId": "147", "severity": 2, "message": "148", "line": 22, "column": 18, "nodeType": "149", "messageId": "150", "endLine": 22, "endColumn": 21, "suggestions": "176"}, {"ruleId": "147", "severity": 2, "message": "148", "line": 34, "column": 12, "nodeType": "149", "messageId": "150", "endLine": 34, "endColumn": 15, "suggestions": "177"}, {"ruleId": "147", "severity": 2, "message": "148", "line": 22, "column": 49, "nodeType": "149", "messageId": "150", "endLine": 22, "endColumn": 52, "suggestions": "178"}, {"ruleId": "147", "severity": 2, "message": "148", "line": 33, "column": 60, "nodeType": "149", "messageId": "150", "endLine": 33, "endColumn": 63, "suggestions": "179"}, {"ruleId": "147", "severity": 2, "message": "148", "line": 48, "column": 50, "nodeType": "149", "messageId": "150", "endLine": 48, "endColumn": 53, "suggestions": "180"}, "@typescript-eslint/no-unused-vars", "'profile' is defined but never used.", "unusedVar", "'request' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["181", "182"], ["183", "184"], ["185", "186"], ["187", "188"], ["189", "190"], ["191", "192"], ["193", "194"], "'Settings' is defined but never used.", "'useEffect' is defined but never used.", "'FileSpreadsheet' is defined but never used.", "'session' is assigned a value but never used.", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["195", "196", "197", "198"], ["199", "200", "201", "202"], ["203", "204", "205", "206"], ["207", "208", "209", "210"], ["211", "212", "213", "214"], ["215", "216", "217", "218"], ["219", "220", "221", "222"], ["223", "224", "225", "226"], ["227", "228", "229", "230"], ["231", "232", "233", "234"], ["235", "236"], ["237", "238"], ["239", "240"], ["241", "242"], ["243", "244"], {"messageId": "245", "fix": "246", "desc": "247"}, {"messageId": "248", "fix": "249", "desc": "250"}, {"messageId": "245", "fix": "251", "desc": "247"}, {"messageId": "248", "fix": "252", "desc": "250"}, {"messageId": "245", "fix": "253", "desc": "247"}, {"messageId": "248", "fix": "254", "desc": "250"}, {"messageId": "245", "fix": "255", "desc": "247"}, {"messageId": "248", "fix": "256", "desc": "250"}, {"messageId": "245", "fix": "257", "desc": "247"}, {"messageId": "248", "fix": "258", "desc": "250"}, {"messageId": "245", "fix": "259", "desc": "247"}, {"messageId": "248", "fix": "260", "desc": "250"}, {"messageId": "245", "fix": "261", "desc": "247"}, {"messageId": "248", "fix": "262", "desc": "250"}, {"messageId": "263", "data": "264", "fix": "265", "desc": "266"}, {"messageId": "263", "data": "267", "fix": "268", "desc": "269"}, {"messageId": "263", "data": "270", "fix": "271", "desc": "272"}, {"messageId": "263", "data": "273", "fix": "274", "desc": "275"}, {"messageId": "263", "data": "276", "fix": "277", "desc": "266"}, {"messageId": "263", "data": "278", "fix": "279", "desc": "269"}, {"messageId": "263", "data": "280", "fix": "281", "desc": "272"}, {"messageId": "263", "data": "282", "fix": "283", "desc": "275"}, {"messageId": "263", "data": "284", "fix": "285", "desc": "266"}, {"messageId": "263", "data": "286", "fix": "287", "desc": "269"}, {"messageId": "263", "data": "288", "fix": "289", "desc": "272"}, {"messageId": "263", "data": "290", "fix": "291", "desc": "275"}, {"messageId": "263", "data": "292", "fix": "293", "desc": "266"}, {"messageId": "263", "data": "294", "fix": "295", "desc": "269"}, {"messageId": "263", "data": "296", "fix": "297", "desc": "272"}, {"messageId": "263", "data": "298", "fix": "299", "desc": "275"}, {"messageId": "263", "data": "300", "fix": "301", "desc": "266"}, {"messageId": "263", "data": "302", "fix": "303", "desc": "269"}, {"messageId": "263", "data": "304", "fix": "305", "desc": "272"}, {"messageId": "263", "data": "306", "fix": "307", "desc": "275"}, {"messageId": "263", "data": "308", "fix": "309", "desc": "266"}, {"messageId": "263", "data": "310", "fix": "311", "desc": "269"}, {"messageId": "263", "data": "312", "fix": "313", "desc": "272"}, {"messageId": "263", "data": "314", "fix": "315", "desc": "275"}, {"messageId": "263", "data": "316", "fix": "317", "desc": "266"}, {"messageId": "263", "data": "318", "fix": "319", "desc": "269"}, {"messageId": "263", "data": "320", "fix": "321", "desc": "272"}, {"messageId": "263", "data": "322", "fix": "323", "desc": "275"}, {"messageId": "263", "data": "324", "fix": "325", "desc": "266"}, {"messageId": "263", "data": "326", "fix": "327", "desc": "269"}, {"messageId": "263", "data": "328", "fix": "329", "desc": "272"}, {"messageId": "263", "data": "330", "fix": "331", "desc": "275"}, {"messageId": "263", "data": "332", "fix": "333", "desc": "266"}, {"messageId": "263", "data": "334", "fix": "335", "desc": "269"}, {"messageId": "263", "data": "336", "fix": "337", "desc": "272"}, {"messageId": "263", "data": "338", "fix": "339", "desc": "275"}, {"messageId": "263", "data": "340", "fix": "341", "desc": "266"}, {"messageId": "263", "data": "342", "fix": "343", "desc": "269"}, {"messageId": "263", "data": "344", "fix": "345", "desc": "272"}, {"messageId": "263", "data": "346", "fix": "347", "desc": "275"}, {"messageId": "245", "fix": "348", "desc": "247"}, {"messageId": "248", "fix": "349", "desc": "250"}, {"messageId": "245", "fix": "350", "desc": "247"}, {"messageId": "248", "fix": "351", "desc": "250"}, {"messageId": "245", "fix": "352", "desc": "247"}, {"messageId": "248", "fix": "353", "desc": "250"}, {"messageId": "245", "fix": "354", "desc": "247"}, {"messageId": "248", "fix": "355", "desc": "250"}, {"messageId": "245", "fix": "356", "desc": "247"}, {"messageId": "248", "fix": "357", "desc": "250"}, "suggestUnknown", {"range": "358", "text": "359"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "360", "text": "361"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "362", "text": "359"}, {"range": "363", "text": "361"}, {"range": "364", "text": "359"}, {"range": "365", "text": "361"}, {"range": "366", "text": "359"}, {"range": "367", "text": "361"}, {"range": "368", "text": "359"}, {"range": "369", "text": "361"}, {"range": "370", "text": "359"}, {"range": "371", "text": "361"}, {"range": "372", "text": "359"}, {"range": "373", "text": "361"}, "replaceWithAlt", {"alt": "374"}, {"range": "375", "text": "376"}, "Replace with `&quot;`.", {"alt": "377"}, {"range": "378", "text": "379"}, "Replace with `&ldquo;`.", {"alt": "380"}, {"range": "381", "text": "382"}, "Replace with `&#34;`.", {"alt": "383"}, {"range": "384", "text": "385"}, "Replace with `&rdquo;`.", {"alt": "374"}, {"range": "386", "text": "387"}, {"alt": "377"}, {"range": "388", "text": "389"}, {"alt": "380"}, {"range": "390", "text": "391"}, {"alt": "383"}, {"range": "392", "text": "393"}, {"alt": "374"}, {"range": "394", "text": "395"}, {"alt": "377"}, {"range": "396", "text": "397"}, {"alt": "380"}, {"range": "398", "text": "399"}, {"alt": "383"}, {"range": "400", "text": "401"}, {"alt": "374"}, {"range": "402", "text": "403"}, {"alt": "377"}, {"range": "404", "text": "405"}, {"alt": "380"}, {"range": "406", "text": "407"}, {"alt": "383"}, {"range": "408", "text": "409"}, {"alt": "374"}, {"range": "410", "text": "411"}, {"alt": "377"}, {"range": "412", "text": "413"}, {"alt": "380"}, {"range": "414", "text": "415"}, {"alt": "383"}, {"range": "416", "text": "417"}, {"alt": "374"}, {"range": "418", "text": "419"}, {"alt": "377"}, {"range": "420", "text": "421"}, {"alt": "380"}, {"range": "422", "text": "423"}, {"alt": "383"}, {"range": "424", "text": "425"}, {"alt": "374"}, {"range": "426", "text": "427"}, {"alt": "377"}, {"range": "428", "text": "429"}, {"alt": "380"}, {"range": "430", "text": "431"}, {"alt": "383"}, {"range": "432", "text": "433"}, {"alt": "374"}, {"range": "434", "text": "435"}, {"alt": "377"}, {"range": "436", "text": "437"}, {"alt": "380"}, {"range": "438", "text": "439"}, {"alt": "383"}, {"range": "440", "text": "441"}, {"alt": "374"}, {"range": "442", "text": "443"}, {"alt": "377"}, {"range": "444", "text": "445"}, {"alt": "380"}, {"range": "446", "text": "447"}, {"alt": "383"}, {"range": "448", "text": "449"}, {"alt": "374"}, {"range": "450", "text": "451"}, {"alt": "377"}, {"range": "452", "text": "453"}, {"alt": "380"}, {"range": "454", "text": "455"}, {"alt": "383"}, {"range": "456", "text": "457"}, {"range": "458", "text": "359"}, {"range": "459", "text": "361"}, {"range": "460", "text": "359"}, {"range": "461", "text": "361"}, {"range": "462", "text": "359"}, {"range": "463", "text": "361"}, {"range": "464", "text": "359"}, {"range": "465", "text": "361"}, {"range": "466", "text": "359"}, {"range": "467", "text": "361"}, [2430, 2433], "unknown", [2430, 2433], "never", [1534, 1537], [1534, 1537], [1607, 1610], [1607, 1610], [833, 836], [833, 836], [925, 928], [925, 928], [1117, 1120], [1117, 1120], [1208, 1211], [1208, 1211], "&quot;", [3618, 3717], "\n            Use Gmail search operators like &quot;from:\", \"subject:\", \"has:attachment\", etc.\n          ", "&ldquo;", [3618, 3717], "\n            Use Gmail search operators like &ldquo;from:\", \"subject:\", \"has:attachment\", etc.\n          ", "&#34;", [3618, 3717], "\n            Use Gmail search operators like &#34;from:\", \"subject:\", \"has:attachment\", etc.\n          ", "&rdquo;", [3618, 3717], "\n            Use Gmail search operators like &rdquo;from:\", \"subject:\", \"has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:&quot;, \"subject:\", \"has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:&ldquo;, \"subject:\", \"has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:&#34;, \"subject:\", \"has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:&rdquo;, \"subject:\", \"has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", &quot;subject:\", \"has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", &ldquo;subject:\", \"has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", &#34;subject:\", \"has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", &rdquo;subject:\", \"has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", \"subject:&quot;, \"has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", \"subject:&ldquo;, \"has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", \"subject:&#34;, \"has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", \"subject:&rdquo;, \"has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", \"subject:\", &quot;has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", \"subject:\", &ldquo;has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", \"subject:\", &#34;has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", \"subject:\", &rdquo;has:attachment\", etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", \"subject:\", \"has:attachment&quot;, etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", \"subject:\", \"has:attachment&ldquo;, etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", \"subject:\", \"has:attachment&#34;, etc.\n          ", [3618, 3717], "\n            Use Gmail search operators like \"from:\", \"subject:\", \"has:attachment&rdquo;, etc.\n          ", [5596, 5637], "• Click &quot;Search\" to fetch matching emails", [5596, 5637], "• Click &ldquo;Search\" to fetch matching emails", [5596, 5637], "• Click &#34;Search\" to fetch matching emails", [5596, 5637], "• Click &rdquo;Search\" to fetch matching emails", [5596, 5637], "• Click \"Search&quot; to fetch matching emails", [5596, 5637], "• Click \"Search&ldquo; to fetch matching emails", [5596, 5637], "• Click \"Search&#34; to fetch matching emails", [5596, 5637], "• Click \"Search&rdquo; to fetch matching emails", [5659, 5720], "• Review the results and click &quot;Download to Sheets\" to export", [5659, 5720], "• Review the results and click &ldquo;Download to Sheets\" to export", [5659, 5720], "• Review the results and click &#34;Download to Sheets\" to export", [5659, 5720], "• Review the results and click &rdquo;Download to Sheets\" to export", [5659, 5720], "• Review the results and click \"Download to Sheets&quot; to export", [5659, 5720], "• Review the results and click \"Download to Sheets&ldquo; to export", [5659, 5720], "• Review the results and click \"Download to Sheets&#34; to export", [5659, 5720], "• Review the results and click \"Download to Sheets&rdquo; to export", [552, 555], [552, 555], [762, 765], [762, 765], [468, 471], [468, 471], [833, 836], [833, 836], [1305, 1308], [1305, 1308]]