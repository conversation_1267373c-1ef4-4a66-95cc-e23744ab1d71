(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[662],{3177:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>u});var t=a(5155),l=a(2115),c=a(2108),n=a(7550),r=a(9946);let i=(0,r.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),d=(0,r.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),o=(0,r.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var m=a(5525),x=a(6874),h=a.n(x);function u(){var e,s;let{data:a}=(0,c.useSession)(),[r,x]=(0,l.useState)([]),[u,p]=(0,l.useState)(!1),v=async()=>{p(!0);try{var e,s,a,t,l,c;let n=await fetch("/api/credentials"),r=await n.json(),i=[{service:"Google",connected:(null==(e=r.credentials)?void 0:e.some(e=>"google"===e.service))||!1,lastUpdated:null==(a=r.credentials)||null==(s=a.find(e=>"google"===e.service))?void 0:s.updated_at,scopes:["Calendar","Gmail","Sheets"]},{service:"Slack",connected:(null==(t=r.credentials)?void 0:t.some(e=>"slack"===e.service))||!1,lastUpdated:null==(c=r.credentials)||null==(l=c.find(e=>"slack"===e.service))?void 0:l.updated_at,scopes:["Channels","Messages","Users"]}];x(i)}catch(e){console.error("Error fetching service status:",e)}finally{p(!1)}},g=async e=>{if(confirm("Are you sure you want to disconnect ".concat(e,"? This will stop all related integrations."))){p(!0);try{(await fetch("/api/credentials?service=".concat(e.toLowerCase()),{method:"DELETE"})).ok&&(await v(),alert("".concat(e," disconnected successfully")))}catch(e){console.error("Error disconnecting service:",e)}finally{p(!1)}}},y=()=>{window.location.href="/api/auth/slack"};return(0,l.useEffect)(()=>{let e=new URLSearchParams(window.location.search),s=e.get("success"),a=e.get("error");if("slack_connected"===s)alert("Slack connected successfully!"),window.history.replaceState({},document.title,window.location.pathname),v();else if(a){let e="Failed to connect to Slack";"slack_auth_failed"===a&&(e="Slack authorization failed"),"slack_token_failed"===a&&(e="Failed to get Slack access token"),"slack_connection_failed"===a&&(e="Slack connection failed"),alert(e),window.history.replaceState({},document.title,window.location.pathname)}},[]),(0,l.useEffect)(()=>{a&&v()},[a]),(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center h-16",children:[(0,t.jsx)(h(),{href:"/",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,t.jsx)(n.A,{className:"w-5 h-5"})}),(0,t.jsx)(i,{className:"w-8 h-8 text-blue-600 mr-3"}),(0,t.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Settings"})]})})}),(0,t.jsxs)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Account Information"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Name"}),(0,t.jsx)("p",{className:"text-gray-900",children:null==a||null==(e=a.user)?void 0:e.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Email"}),(0,t.jsx)("p",{className:"text-gray-900",children:null==a||null==(s=a.user)?void 0:s.email})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Connected Services"}),(0,t.jsx)("button",{onClick:v,disabled:u,className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:u?"Refreshing...":"Refresh"})]}),(0,t.jsx)("div",{className:"space-y-4",children:r.map(e=>{var s;return(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)("div",{className:"flex items-center mr-4",children:[e.connected?(0,t.jsx)(d,{className:"w-5 h-5 text-green-500 mr-2"}):(0,t.jsx)(o,{className:"w-5 h-5 text-red-500 mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.service})]}),(0,t.jsx)("div",{children:(0,t.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(e.connected?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.connected?"Connected":"Disconnected"})})]}),(0,t.jsx)("div",{className:"flex space-x-2",children:e.connected?(0,t.jsx)("button",{onClick:()=>g(e.service),disabled:u,className:"px-3 py-1 text-sm text-red-600 border border-red-300 rounded hover:bg-red-50 transition-colors",children:"Disconnect"}):"Slack"===e.service&&(0,t.jsx)("button",{onClick:y,className:"px-3 py-1 text-sm text-blue-600 border border-blue-300 rounded hover:bg-blue-50 transition-colors",children:"Connect"})})]}),e.connected&&(0,t.jsxs)("div",{className:"mt-3 text-sm text-gray-600",children:[(0,t.jsxs)("p",{children:["Last updated: ",e.lastUpdated?new Date(e.lastUpdated).toLocaleString():"Unknown"]}),(0,t.jsxs)("p",{children:["Permissions: ",null==(s=e.scopes)?void 0:s.join(", ")]})]})]},e.service)})})]}),(0,t.jsx)("div",{className:"mt-8 bg-blue-50 rounded-lg p-6",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(m.A,{className:"w-6 h-6 text-blue-600 mr-3 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-blue-900 mb-2",children:"Security & Privacy"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,t.jsx)("li",{children:"• All credentials are encrypted using AES-256 encryption"}),(0,t.jsx)("li",{children:"• API tokens are stored securely and never exposed in logs"}),(0,t.jsx)("li",{children:"• You can disconnect services at any time"}),(0,t.jsx)("li",{children:"• Data is only accessed when you explicitly trigger an action"}),(0,t.jsx)("li",{children:"• No data is shared with third parties"})]})]})]})})]})]})}},3386:(e,s,a)=>{Promise.resolve().then(a.bind(a,3177))},5525:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[108,906,441,684,358],()=>s(3386)),_N_E=e.O()}]);