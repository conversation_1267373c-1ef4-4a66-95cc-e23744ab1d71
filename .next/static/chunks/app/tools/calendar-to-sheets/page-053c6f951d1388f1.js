(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[404],{4261:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},4616:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5203:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>m});var t=a(5155),r=a(2115),l=a(2108),n=a(7550);let d=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var c=a(4616),i=a(4261),o=a(6874),h=a.n(o);function m(){let{data:e}=(0,l.useSession)(),[s,a]=(0,r.useState)([]),[o,m]=(0,r.useState)([]),[x,p]=(0,r.useState)(!1),[u,y]=(0,r.useState)(!1),[g,b]=(0,r.useState)({name:"",sheetName:"Calendar Events"}),N=async()=>{p(!0);try{let e=await fetch("/api/tools/calendar/events"),s=await e.json();s.events&&a(s.events)}catch(e){console.error("Error fetching calendar events:",e)}finally{p(!1)}},j=async()=>{if(g.name){p(!0);try{let e=await fetch("/api/tools/calendar/create-spreadsheet",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(g)}),s=await e.json();s.success&&(m([...o,s.spreadsheet]),b({name:"",sheetName:"Calendar Events"}),y(!1))}catch(e){console.error("Error creating spreadsheet:",e)}finally{p(!1)}}},v=async e=>{p(!0);try{let a=await fetch("/api/tools/calendar/update-spreadsheet",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({spreadsheetId:e,events:s})});(await a.json()).success&&alert("Spreadsheet updated successfully!")}catch(e){console.error("Error updating spreadsheet:",e)}finally{p(!1)}};return(0,r.useEffect)(()=>{e&&N()},[e]),(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h(),{href:"/",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,t.jsx)(n.A,{className:"w-5 h-5"})}),(0,t.jsx)(d,{className:"w-8 h-8 text-blue-600 mr-3"}),(0,t.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Calendar to Sheets"})]}),(0,t.jsxs)("button",{onClick:()=>y(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Create Spreadsheet"]})]})})}),(0,t.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Recent Calendar Events"}),(0,t.jsx)("button",{onClick:N,disabled:x,className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:x?"Loading...":"Refresh"})]}),(0,t.jsx)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:0===s.length?(0,t.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No calendar events found. Make sure your Google Calendar is connected."}):s.map(e=>(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:e.summary}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[new Date(e.start.dateTime).toLocaleString()," -",new Date(e.end.dateTime).toLocaleString()]}),e.description&&(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:e.description})]},e.id))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"Connected Spreadsheets"}),(0,t.jsx)("div",{className:"space-y-4",children:0===o.length?(0,t.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No spreadsheets created yet. Create one to start syncing calendar events."}):o.map(e=>(0,t.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Sheet: ",e.sheetName]}),(0,t.jsxs)("div",{className:"flex items-center mt-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 rounded-full mr-2 ".concat(e.isActive?"bg-green-400":"bg-gray-300")}),(0,t.jsx)("span",{className:"text-xs ".concat(e.isActive?"text-green-600":"text-gray-500"),children:e.isActive?"Active":"Inactive"})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{onClick:()=>v(e.id),disabled:x,className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors",children:"Update"}),(0,t.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-gray-600",children:(0,t.jsx)(i.A,{className:"w-4 h-4"})})]})]})},e.id))})]})]}),u&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Create New Spreadsheet"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Spreadsheet Name"}),(0,t.jsx)("input",{type:"text",value:g.name,onChange:e=>b({...g,name:e.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"My Calendar Events"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sheet Name"}),(0,t.jsx)("input",{type:"text",value:g.sheetName,onChange:e=>b({...g,sheetName:e.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Calendar Events"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,t.jsx)("button",{onClick:()=>y(!1),className:"px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,t.jsx)("button",{onClick:j,disabled:x||!g.name,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:x?"Creating...":"Create"})]})]})})]})]})}},6042:(e,s,a)=>{Promise.resolve().then(a.bind(a,5203))}},e=>{var s=s=>e(e.s=s);e.O(0,[108,906,441,684,358],()=>s(6042)),_N_E=e.O()}]);