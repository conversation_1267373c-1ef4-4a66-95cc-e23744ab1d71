(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[469],{2960:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>g});var s=t(5155),r=t(2115),c=t(2108),l=t(7550),i=t(9946);let n=(0,i.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var o=t(4616);let d=(0,i.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),m=(0,i.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),x=(0,i.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var u=t(6874),h=t.n(u);function g(){let{data:e}=(0,c.useSession)(),[a,t]=(0,r.useState)([]),[i,u]=(0,r.useState)(!1),[g,y]=(0,r.useState)(!1),[v,p]=(0,r.useState)(null),[b,f]=(0,r.useState)(""),[j,N]=(0,r.useState)({name:"",fee_agreement:"",aka:"",is_active:!0}),w=async()=>{u(!0);try{let e=await fetch("/api/tools/customers"),a=await e.json();a.customers&&t(a.customers)}catch(e){console.error("Error fetching customers:",e)}finally{u(!1)}},k=async()=>{if(j.name.trim()){u(!0);try{let e=v?"/api/tools/customers/".concat(v.id):"/api/tools/customers",a=v?"PUT":"POST",t=await fetch(e,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(j)});(await t.json()).success&&(await w(),A())}catch(e){console.error("Error saving customer:",e)}finally{u(!1)}}},_=async e=>{if(confirm("Are you sure you want to delete this customer?")){u(!0);try{let a=await fetch("/api/tools/customers/".concat(e),{method:"DELETE"});(await a.json()).success&&await w()}catch(e){console.error("Error deleting customer:",e)}finally{u(!1)}}},C=e=>{p(e),N({name:e.name,fee_agreement:e.fee_agreement||"",aka:e.aka||"",is_active:e.is_active}),y(!0)},A=()=>{N({name:"",fee_agreement:"",aka:"",is_active:!0}),p(null),y(!1)},S=async(e,a)=>{u(!0);try{let t=await fetch("/api/tools/customers/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_active:!a})});(await t.json()).success&&await w()}catch(e){console.error("Error updating customer status:",e)}finally{u(!1)}},M=a.filter(e=>e.name.toLowerCase().includes(b.toLowerCase())).sort((e,a)=>e.is_active!==a.is_active?e.is_active?-1:1:e.name.localeCompare(a.name));return(0,r.useEffect)(()=>{e&&w()},[e]),(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(h(),{href:"/",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,s.jsx)(l.A,{className:"w-5 h-5"})}),(0,s.jsx)(n,{className:"w-8 h-8 text-blue-600 mr-3"}),(0,s.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Customer Manager"})]}),(0,s.jsxs)("button",{onClick:()=>y(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,s.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Add Customer"]})]})})}),(0,s.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d,{className:"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Search by name...",value:b,onChange:e=>f(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,s.jsxs)("h2",{className:"text-lg font-medium text-gray-900",children:["Customers (",M.length,")"]})}),i?(0,s.jsx)("div",{className:"p-8 text-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"})}):0===M.length?(0,s.jsx)("div",{className:"p-8 text-center text-gray-500",children:b?"No customers found matching your search.":"No customers yet. Add your first customer name to get started."}):(0,s.jsx)("div",{className:"divide-y divide-gray-200",children:M.map(e=>(0,s.jsx)("div",{className:"p-6 hover:bg-gray-50 ".concat(e.is_active?"":"opacity-60 bg-gray-50"),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("h3",{className:"text-lg font-medium ".concat(e.is_active?"text-gray-900":"text-gray-500"),children:e.name}),(0,s.jsxs)("div",{className:"ml-3 flex items-center",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full mr-2 ".concat(e.is_active?"bg-green-400":"bg-gray-400")}),(0,s.jsx)("span",{className:"text-xs ".concat(e.is_active?"text-green-600":"text-gray-500"),children:e.is_active?"Active":"Inactive"})]})]}),e.aka&&(0,s.jsxs)("p",{className:"mt-1 text-sm ".concat(e.is_active?"text-gray-600":"text-gray-500"),children:["AKA: ",e.aka]}),e.fee_agreement&&(0,s.jsxs)("p",{className:"mt-1 text-sm ".concat(e.is_active?"text-gray-600":"text-gray-500"),children:["Fee Agreement: ",e.fee_agreement]}),(0,s.jsxs)("p",{className:"mt-2 text-xs text-gray-400",children:["Added ",new Date(e.created_at).toLocaleDateString()]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>S(e.id,e.is_active),className:"px-3 py-1 text-xs font-medium rounded-full transition-colors ".concat(e.is_active?"bg-red-100 text-red-700 hover:bg-red-200":"bg-green-100 text-green-700 hover:bg-green-200"),title:e.is_active?"Mark as Inactive":"Mark as Active",children:e.is_active?"Inactive":"Active"}),(0,s.jsx)("button",{onClick:()=>C(e),className:"p-2 text-gray-400 hover:text-blue-600",children:(0,s.jsx)(m,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>_(e.id),className:"p-2 text-gray-400 hover:text-red-600",children:(0,s.jsx)(x,{className:"w-4 h-4"})})]})]})},e.id))})]}),g&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:v?"Edit Customer":"Add New Customer"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name *"}),(0,s.jsx)("input",{type:"text",value:j.name,onChange:e=>N({...j,name:e.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"John Doe",autoFocus:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"AKA (separated by comma)"}),(0,s.jsx)("input",{type:"text",value:j.aka,onChange:e=>N({...j,aka:e.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., Johnny, J. Doe, John Smith"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fee Agreement"}),(0,s.jsx)("input",{type:"text",value:j.fee_agreement,onChange:e=>N({...j,fee_agreement:e.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., $500/hour, Fixed $5000, Contingency 30%"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:j.is_active,onChange:e=>N({...j,is_active:e.target.checked}),className:"mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Active Client"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Inactive clients will be grayed out and sorted below active clients"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,s.jsx)("button",{onClick:A,className:"px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,s.jsx)("button",{onClick:k,disabled:i||!j.name.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:i?"Saving...":v?"Update":"Add"})]})]})})]})]})}},4616:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},9601:(e,a,t)=>{Promise.resolve().then(t.bind(t,2960))}},e=>{var a=a=>e(e.s=a);e.O(0,[108,906,441,684,358],()=>a(9601)),_N_E=e.O()}]);