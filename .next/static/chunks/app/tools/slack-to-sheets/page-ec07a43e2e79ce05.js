(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[956],{4261:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},4616:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},7418:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>m});var t=a(5155),n=a(2115),l=a(2108),c=a(7550);let r=(0,a(9946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var i=a(4616),d=a(4261),o=a(6874),h=a.n(o);function m(){let{data:e}=(0,l.useSession)(),[s,a]=(0,n.useState)([]),[o,m]=(0,n.useState)([]),[x,g]=(0,n.useState)(!1),[u,y]=(0,n.useState)(!1),[p,b]=(0,n.useState)(""),j=async()=>{g(!0);try{let e=await fetch("/api/tools/slack/channels"),s=await e.json();s.channels&&a(s.channels)}catch(e){console.error("Error fetching channels:",e)}finally{g(!1)}},f=async()=>{try{let e=await fetch("/api/tools/slack/connections"),s=await e.json();s.connections&&m(s.connections)}catch(e){console.error("Error fetching connections:",e)}},N=async()=>{if(p){g(!0);try{let e=await fetch("/api/tools/slack/connect-channel",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({channelId:p})});(await e.json()).success&&(await f(),b(""),y(!1))}catch(e){console.error("Error connecting channel:",e)}finally{g(!1)}}},v=async()=>{window.location.href="/api/auth/slack"},w=async()=>{try{let e=await fetch("/api/credentials?service=slack"),s=await e.json();return s.credentials&&s.credentials.length>0}catch(e){return console.error("Error checking Slack connection:",e),!1}};return(0,n.useEffect)(()=>{(async()=>{e&&await w()&&(j(),f())})()},[e]),(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h(),{href:"/",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,t.jsx)(c.A,{className:"w-5 h-5"})}),(0,t.jsx)(r,{className:"w-8 h-8 text-blue-600 mr-3"}),(0,t.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Slack to Sheets"})]}),(0,t.jsxs)("button",{onClick:()=>y(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,t.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Connect Channel"]})]})})}),(0,t.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[0!==s.length||x?(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"Available Channels"}),(0,t.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:s.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"font-medium text-gray-900",children:["#",e.name]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.is_member?"Member":"Not a member"})]}),(0,t.jsx)("div",{className:"flex items-center",children:o.find(s=>s.channel_id===e.id)?(0,t.jsx)("span",{className:"text-green-600 text-sm font-medium",children:"Connected"}):(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:"Not connected"})})]},e.id))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"Connected Channels"}),0===o.length?(0,t.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No channels connected yet. Connect a channel to start syncing messages."}):(0,t.jsx)("div",{className:"space-y-4",children:o.map(e=>(0,t.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"font-medium text-gray-900",children:["#",e.channel_name]}),(0,t.jsxs)("div",{className:"flex items-center mt-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 rounded-full mr-2 ".concat(e.is_active?"bg-green-400":"bg-gray-300")}),(0,t.jsx)("span",{className:"text-xs ".concat(e.is_active?"text-green-600":"text-gray-500"),children:e.is_active?"Active":"Inactive"})]})]}),(0,t.jsx)("a",{href:e.spreadsheet_url,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-gray-600",children:(0,t.jsx)(d.A,{className:"w-4 h-4"})})]})},e.id))})]})]}):(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center",children:[(0,t.jsx)(r,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-xl font-medium text-gray-900 mb-2",children:"Connect to Slack"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Connect your Slack workspace to start syncing channel messages to spreadsheets."}),(0,t.jsx)("button",{onClick:v,className:"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors",children:"Connect Slack Workspace"})]}),u&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Connect Channel to Spreadsheet"}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Channel"}),(0,t.jsxs)("select",{value:p,onChange:e=>b(e.target.value),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"",children:"Choose a channel..."}),s.filter(e=>!o.find(s=>s.channel_id===e.id)).map(e=>(0,t.jsxs)("option",{value:e.id,children:["#",e.name]},e.id))]})]})}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,t.jsx)("button",{onClick:()=>y(!1),className:"px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,t.jsx)("button",{onClick:N,disabled:x||!p,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:x?"Connecting...":"Connect"})]})]})}),(0,t.jsxs)("div",{className:"mt-8 bg-blue-50 rounded-lg p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-blue-900 mb-2",children:"How to use Slack to Sheets"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,t.jsx)("li",{children:"• Connect your Slack workspace to access channels"}),(0,t.jsx)("li",{children:"• Select a channel to connect to a new spreadsheet"}),(0,t.jsx)("li",{children:"• Messages from the channel will be automatically synced"}),(0,t.jsx)("li",{children:"• View and manage your data in the connected spreadsheet"})]})]})]})]})}},9442:(e,s,a)=>{Promise.resolve().then(a.bind(a,7418))}},e=>{var s=s=>e(e.s=s);e.O(0,[108,906,441,684,358],()=>s(9442)),_N_E=e.O()}]);