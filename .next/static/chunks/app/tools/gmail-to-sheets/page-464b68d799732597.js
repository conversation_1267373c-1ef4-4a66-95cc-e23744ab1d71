(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[406],{4968:(e,s,a)=>{Promise.resolve().then(a.bind(a,6925))},6925:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>h});var t=a(5155),l=a(2115),r=a(2108),i=a(7550),c=a(9946);let d=(0,c.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),n=(0,c.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var o=a(6874),m=a.n(o);function h(){let{data:e}=(0,r.useSession)(),[s,a]=(0,l.useState)([]),[c,o]=(0,l.useState)(!1),[h,x]=(0,l.useState)(""),u=async()=>{o(!0);try{let e=await fetch("/api/tools/gmail/emails?q=".concat(encodeURIComponent(h))),s=await e.json();s.emails&&a(s.emails)}catch(e){console.error("Error fetching emails:",e)}finally{o(!1)}},g=async()=>{if(0!==s.length){o(!0);try{let e=await fetch("/api/tools/gmail/download-to-sheets",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({emails:s})});(await e.json()).success&&alert("Emails downloaded to spreadsheet successfully!")}catch(e){console.error("Error downloading emails:",e)}finally{o(!1)}}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(m(),{href:"/",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,t.jsx)(i.A,{className:"w-5 h-5"})}),(0,t.jsx)(d,{className:"w-8 h-8 text-blue-600 mr-3"}),(0,t.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Gmail to Sheets"})]}),(0,t.jsxs)("button",{onClick:g,disabled:c||0===s.length,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center disabled:opacity-50",children:[(0,t.jsx)(n,{className:"w-4 h-4 mr-2"}),"Download to Sheets"]})]})})}),(0,t.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Search Gmail"}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsx)("input",{type:"text",value:h,onChange:e=>x(e.target.value),placeholder:"Enter search query (e.g., from:<EMAIL>, subject:invoice)",className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,t.jsx)("button",{onClick:u,disabled:c,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:c?"Searching...":"Search"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:'Use Gmail search operators like "from:", "subject:", "has:attachment", etc.'})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,t.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,t.jsxs)("h2",{className:"text-lg font-medium text-gray-900",children:["Email Results (",s.length,")"]})}),c?(0,t.jsx)("div",{className:"p-8 text-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"})}):0===s.length?(0,t.jsx)("div",{className:"p-8 text-center text-gray-500",children:h?"No emails found matching your search.":"Enter a search query to find emails."}):(0,t.jsx)("div",{className:"divide-y divide-gray-200 max-h-96 overflow-y-auto",children:s.map(e=>(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"flex items-start justify-between",children:(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:e.subject}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["From: ",e.from]}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:e.date}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:e.snippet})]})})},e.id))})]}),(0,t.jsxs)("div",{className:"mt-8 bg-blue-50 rounded-lg p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-blue-900 mb-2",children:"How to use Gmail to Sheets"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,t.jsx)("li",{children:"• Use Gmail search operators to find specific emails"}),(0,t.jsx)("li",{children:'• Click "Search" to fetch matching emails'}),(0,t.jsx)("li",{children:'• Review the results and click "Download to Sheets" to export'}),(0,t.jsx)("li",{children:"• A new spreadsheet will be created with email data"})]})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[108,906,441,684,358],()=>s(4968)),_N_E=e.O()}]);