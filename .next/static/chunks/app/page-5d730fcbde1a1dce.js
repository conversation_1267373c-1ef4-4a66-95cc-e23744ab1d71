(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2824:(e,t,s)=>{Promise.resolve().then(s.bind(s,3792))},3792:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(5155),l=s(2108),a=s(5525);function n(){var e,t;let{data:s,status:n}=(0,l.useSession)();return"loading"===n?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):s?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(a.A,{className:"w-8 h-8 text-blue-600 mr-3"}),(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"API Integration Hub"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-700",children:["Welcome, ",null==(e=s.user)?void 0:e.name]}),(0,r.jsx)("button",{onClick:()=>(0,l.signOut)(),className:"text-sm text-gray-500 hover:text-gray-700",children:"Sign out"})]})]})})}),(0,r.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Welcome to your Integration Hub!"}),(0,r.jsxs)("p",{className:"text-gray-600 mb-8",children:["You are successfully logged in as ",null==(t=s.user)?void 0:t.email]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Next Steps:"}),(0,r.jsxs)("ul",{className:"text-left space-y-2 text-gray-600",children:[(0,r.jsx)("li",{children:"• Go to Settings to connect your Slack workspace"}),(0,r.jsx)("li",{children:"• Set up your Google integrations"}),(0,r.jsx)("li",{children:"• Explore the integration tools"})]})]})]})})]}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,r.jsx)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(a.A,{className:"w-16 h-16 text-blue-600 mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"API Integration Hub"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"Securely connect and manage your Google, Slack, and other API integrations"}),(0,r.jsx)("button",{onClick:()=>(0,l.signIn)("google"),className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium",children:"Sign in with Google"})]})})})}},5525:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var r=s(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:l=24,strokeWidth:a=2,absoluteStrokeWidth:n,className:d="",children:x,iconNode:m,...u}=e;return(0,r.createElement)("svg",{ref:t,...o,width:l,height:l,stroke:s,strokeWidth:n?24*Number(a)/Number(l):a,className:i("lucide",d),...!x&&!c(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(x)?x:[x]])}),x=(e,t)=>{let s=(0,r.forwardRef)((s,a)=>{let{className:c,...o}=s;return(0,r.createElement)(d,{ref:a,iconNode:t,className:i("lucide-".concat(l(n(e))),"lucide-".concat(e),c),...o})});return s.displayName=n(e),s}}},e=>{var t=t=>e(e.s=t);e.O(0,[108,441,684,358],()=>t(2824)),_N_E=e.O()}]);