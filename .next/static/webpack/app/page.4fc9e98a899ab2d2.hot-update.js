"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    var _session_user;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [tools, setTools] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        {\n            id: 'calendar-to-sheets',\n            name: 'Calendar to Sheets',\n            description: 'Create and update spreadsheets from calendar events',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 29,\n                columnNumber: 13\n            }, this),\n            path: '/tools/calendar-to-sheets',\n            isActive: false\n        },\n        {\n            id: 'gmail-to-sheets',\n            name: 'Gmail to Sheets',\n            description: 'Download and update email data to spreadsheets',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 37,\n                columnNumber: 13\n            }, this),\n            path: '/tools/gmail-to-sheets',\n            isActive: false\n        },\n        {\n            id: 'slack-to-sheets',\n            name: 'Slack to Sheets',\n            description: 'Connect Slack channels to spreadsheets',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 45,\n                columnNumber: 13\n            }, this),\n            path: '/tools/slack-to-sheets',\n            isActive: false\n        },\n        {\n            id: 'customer-manager',\n            name: 'Customer Manager',\n            description: 'Manage and edit customer information',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 53,\n                columnNumber: 13\n            }, this),\n            path: '/tools/customer-manager',\n            isActive: false\n        }\n    ]);\n    // Features to build state\n    const [featureList, setFeatureList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        {\n            text: \"Fix Google Status Connection\",\n            completed: false\n        },\n        {\n            text: \"Add Slack OAuth Integration\",\n            completed: false\n        },\n        {\n            text: \"Implement Gmail Search Filters\",\n            completed: false\n        },\n        {\n            text: \"Add Export to CSV Feature\",\n            completed: false\n        }\n    ]);\n    const [showFeatureForm, setShowFeatureForm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [newFeatureText, setNewFeatureText] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    // Feature management functions\n    const addFeature = ()=>{\n        if (newFeatureText.trim()) {\n            setFeatureList([\n                ...featureList,\n                {\n                    text: newFeatureText.trim(),\n                    completed: false\n                }\n            ]);\n            setNewFeatureText('');\n            setShowFeatureForm(false);\n        }\n    };\n    const toggleFeature = (index)=>{\n        const updated = featureList.map((feature, i)=>i === index ? {\n                ...feature,\n                completed: !feature.completed\n            } : feature);\n        setFeatureList(updated);\n    };\n    const removeFeature = (index)=>{\n        setFeatureList(featureList.filter((_, i)=>i !== index));\n    };\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-16 h-16 text-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"API Integration Hub\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-8\",\n                            children: \"Securely connect and manage your Google, Slack, and other API integrations\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signIn)('google'),\n                            className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                            children: \"Sign in with Google\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"API Integration Hub\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: [\n                                            \"Welcome, \",\n                                            (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/settings\",\n                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signOut)(),\n                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Integration Tools\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Manage your API integrations and automate data workflows\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: tools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: tool.path,\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg mr-3\",\n                                                children: tool.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900\",\n                                                        children: tool.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full mr-2 \".concat(tool.isActive ? 'bg-green-400' : 'bg-gray-300')\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs \".concat(tool.isActive ? 'text-green-600' : 'text-gray-500'),\n                                                                children: tool.isActive ? 'Active' : 'Inactive'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: tool.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, tool.id, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"Connected Services\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-blue-600\",\n                                        children: \"2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Google, Slack\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"Active Integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-green-600\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Running workflows\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"Last Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Just now\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"User login\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 rounded-lg shadow-sm border border-yellow-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: \"Features To Build\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowFeatureForm(true),\n                                                className: \"text-yellow-600 hover:text-yellow-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-32 overflow-y-auto\",\n                                        children: [\n                                            featureList.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: feature.completed,\n                                                                    onChange: ()=>toggleFeature(index),\n                                                                    className: \"mr-2 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm \".concat(feature.completed ? 'line-through text-gray-500' : 'text-gray-700'),\n                                                                    children: feature.text\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeFeature(index),\n                                                            className: \"opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-700 ml-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            featureList.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 italic\",\n                                                children: \"No features added yet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    showFeatureForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                    children: \"Add New Feature\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Feature Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newFeatureText,\n                                                onChange: (e)=>setNewFeatureText(e.target.value),\n                                                onKeyPress: (e)=>e.key === 'Enter' && addFeature(),\n                                                className: \"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500\",\n                                                placeholder: \"e.g., Add dark mode support\",\n                                                autoFocus: true\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowFeatureForm(false);\n                                                setNewFeatureText('');\n                                            },\n                                            className: \"px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addFeature,\n                                            disabled: !newFeatureText.trim(),\n                                            className: \"px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors disabled:opacity-50\",\n                                            children: \"Add Feature\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"lGNRTwjlNQntmVNSKXQlbt/Qyzc=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});