"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,LogOut,Mail,MessageSquare,Plus,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    var _session_user;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [tools, setTools] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        {\n            id: 'calendar-to-sheets',\n            name: 'Calendar to Sheets',\n            description: 'Create and update spreadsheets from calendar events',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 29,\n                columnNumber: 13\n            }, this),\n            path: '/tools/calendar-to-sheets',\n            isActive: false\n        },\n        {\n            id: 'gmail-to-sheets',\n            name: 'Gmail to Sheets',\n            description: 'Download and update email data to spreadsheets',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 37,\n                columnNumber: 13\n            }, this),\n            path: '/tools/gmail-to-sheets',\n            isActive: false\n        },\n        {\n            id: 'slack-to-sheets',\n            name: 'Slack to Sheets',\n            description: 'Connect Slack channels to spreadsheets',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 45,\n                columnNumber: 13\n            }, this),\n            path: '/tools/slack-to-sheets',\n            isActive: false\n        },\n        {\n            id: 'customer-manager',\n            name: 'Customer Manager',\n            description: 'Manage and edit customer information',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 53,\n                columnNumber: 13\n            }, this),\n            path: '/tools/customer-manager',\n            isActive: false\n        }\n    ]);\n    // Features to build state\n    const [featureList, setFeatureList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        {\n            text: \"Fix Google Status Connection\",\n            completed: false\n        },\n        {\n            text: \"Add Slack OAuth Integration\",\n            completed: false\n        },\n        {\n            text: \"Implement Gmail Search Filters\",\n            completed: false\n        },\n        {\n            text: \"Add Export to CSV Feature\",\n            completed: false\n        }\n    ]);\n    const [showFeatureForm, setShowFeatureForm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [newFeatureText, setNewFeatureText] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    // Feature management functions\n    const addFeature = ()=>{\n        if (newFeatureText.trim()) {\n            setFeatureList([\n                ...featureList,\n                {\n                    text: newFeatureText.trim(),\n                    completed: false\n                }\n            ]);\n            setNewFeatureText('');\n            setShowFeatureForm(false);\n        }\n    };\n    const toggleFeature = (index)=>{\n        const updated = featureList.map((feature, i)=>i === index ? {\n                ...feature,\n                completed: !feature.completed\n            } : feature);\n        setFeatureList(updated);\n    };\n    const removeFeature = (index)=>{\n        setFeatureList(featureList.filter((_, i)=>i !== index));\n    };\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-16 h-16 text-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"API Integration Hub\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-8\",\n                            children: \"Securely connect and manage your Google, Slack, and other API integrations\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signIn)('google'),\n                            className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                            children: \"Sign in with Google\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"API Integration Hub\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: [\n                                            \"Welcome, \",\n                                            (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/settings\",\n                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signOut)(),\n                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"Integration Tools\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Manage your API integrations and automate data workflows\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: tools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: tool.path,\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 rounded-lg mr-3\",\n                                                children: tool.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900\",\n                                                        children: tool.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full mr-2 \".concat(tool.isActive ? 'bg-green-400' : 'bg-gray-300')\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs \".concat(tool.isActive ? 'text-green-600' : 'text-gray-500'),\n                                                                children: tool.isActive ? 'Active' : 'Inactive'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: tool.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, tool.id, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"Connected Services\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-blue-600\",\n                                        children: \"2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Google, Slack\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"Active Integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-green-600\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Running workflows\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"Last Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Just now\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"User login\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 rounded-lg shadow-sm border border-yellow-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: \"Features To Build\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowFeatureForm(true),\n                                                className: \"text-yellow-600 hover:text-yellow-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-32 overflow-y-auto\",\n                                        children: [\n                                            featureList.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: feature.completed,\n                                                                    onChange: ()=>toggleFeature(index),\n                                                                    className: \"mr-2 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm \".concat(feature.completed ? 'line-through text-gray-500' : 'text-gray-700'),\n                                                                    children: feature.text\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeFeature(index),\n                                                            className: \"opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-700 ml-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_LogOut_Mail_MessageSquare_Plus_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            featureList.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 italic\",\n                                                children: \"No features added yet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"lGNRTwjlNQntmVNSKXQlbt/Qyzc=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});