"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tools/customer-manager/page",{

/***/ "(app-pages-browser)/./src/app/tools/customer-manager/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/tools/customer-manager/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CustomerManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Plus,Search,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Plus,Search,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Plus,Search,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Plus,Search,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Plus,Search,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Plus,Search,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CustomerManager() {\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCustomer, setEditingCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        fee_agreement: '',\n        aka: '',\n        is_active: true\n    });\n    const fetchCustomers = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch('/api/tools/customers');\n            const data = await response.json();\n            if (data.customers) {\n                setCustomers(data.customers);\n            }\n        } catch (error) {\n            console.error('Error fetching customers:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveCustomer = async ()=>{\n        if (!formData.name.trim()) return;\n        setLoading(true);\n        try {\n            const url = editingCustomer ? \"/api/tools/customers/\".concat(editingCustomer.id) : '/api/tools/customers';\n            const method = editingCustomer ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (data.success) {\n                await fetchCustomers();\n                resetForm();\n            }\n        } catch (error) {\n            console.error('Error saving customer:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const deleteCustomer = async (customerId)=>{\n        if (!confirm('Are you sure you want to delete this customer?')) return;\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/tools/customers/\".concat(customerId), {\n                method: 'DELETE'\n            });\n            const data = await response.json();\n            if (data.success) {\n                await fetchCustomers();\n            }\n        } catch (error) {\n            console.error('Error deleting customer:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const editCustomer = (customer)=>{\n        setEditingCustomer(customer);\n        setFormData({\n            name: customer.name,\n            fee_agreement: customer.fee_agreement || '',\n            aka: customer.aka || '',\n            is_active: customer.is_active\n        });\n        setShowForm(true);\n    };\n    const resetForm = ()=>{\n        setFormData({\n            name: '',\n            fee_agreement: '',\n            aka: '',\n            is_active: true\n        });\n        setEditingCustomer(null);\n        setShowForm(false);\n    };\n    const toggleCustomerStatus = async (customerId, currentStatus)=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/tools/customers/\".concat(customerId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    is_active: !currentStatus\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                await fetchCustomers();\n            }\n        } catch (error) {\n            console.error('Error updating customer status:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredCustomers = customers.filter((customer)=>customer.name.toLowerCase().includes(searchTerm.toLowerCase())).sort((a, b)=>{\n        // First sort by active status (active customers first)\n        if (a.is_active !== b.is_active) {\n            return a.is_active ? -1 : 1;\n        }\n        // Then sort alphabetically by name within each group\n        return a.name.localeCompare(b.name);\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CustomerManager.useEffect\": ()=>{\n            if (session) {\n                fetchCustomers();\n            }\n        }\n    }[\"CustomerManager.useEffect\"], [\n        session\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/\",\n                                        className: \"mr-4 p-2 text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Customer Manager\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowForm(true),\n                                className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Customer\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search by name...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    children: [\n                                        \"Customers (\",\n                                        filteredCustomers.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, this),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this) : filteredCustomers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center text-gray-500\",\n                                children: searchTerm ? 'No customers found matching your search.' : 'No customers yet. Add your first customer name to get started.'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"divide-y divide-gray-200\",\n                                children: filteredCustomers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 hover:bg-gray-50 \".concat(!customer.is_active ? 'opacity-60 bg-gray-50' : ''),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-medium \".concat(customer.is_active ? 'text-gray-900' : 'text-gray-500'),\n                                                                    children: customer.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-3 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 rounded-full mr-2 \".concat(customer.is_active ? 'bg-green-400' : 'bg-gray-400')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs \".concat(customer.is_active ? 'text-green-600' : 'text-gray-500'),\n                                                                            children: customer.is_active ? 'Active' : 'Inactive'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                                            lineNumber: 224,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        customer.aka && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-sm \".concat(customer.is_active ? 'text-gray-600' : 'text-gray-500'),\n                                                            children: [\n                                                                \"AKA: \",\n                                                                customer.aka\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        customer.fee_agreement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-sm \".concat(customer.is_active ? 'text-gray-600' : 'text-gray-500'),\n                                                            children: [\n                                                                \"Fee Agreement: \",\n                                                                customer.fee_agreement\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-2 text-xs text-gray-400\",\n                                                            children: [\n                                                                \"Added \",\n                                                                new Date(customer.created_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>toggleCustomerStatus(customer.id, customer.is_active),\n                                                            className: \"p-2 \".concat(customer.is_active ? 'text-green-600 hover:text-green-700' : 'text-gray-400 hover:text-green-600'),\n                                                            title: customer.is_active ? 'Mark as Inactive' : 'Mark as Active',\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>editCustomer(customer),\n                                                            className: \"p-2 text-gray-400 hover:text-blue-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deleteCustomer(customer.id),\n                                                            className: \"p-2 text-gray-400 hover:text-red-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, customer.id, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this),\n                    showForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                    children: editingCustomer ? 'Edit Customer' : 'Add New Customer'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.name,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            name: e.target.value\n                                                        }),\n                                                    className: \"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    placeholder: \"John Doe\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Fee Agreement\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.fee_agreement,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            fee_agreement: e.target.value\n                                                        }),\n                                                    className: \"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    placeholder: \"e.g., $500/hour, Fixed $5000, Contingency 30%\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: resetForm,\n                                            className: \"px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: saveCustomer,\n                                            disabled: loading || !formData.name.trim(),\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                                            children: loading ? 'Saving...' : editingCustomer ? 'Update' : 'Add'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomerManager, \"XdQWsn3MESxQiV+xgz/kSfwzy+k=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = CustomerManager;\nvar _c;\n$RefreshReg$(_c, \"CustomerManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/tools/customer-manager/page.tsx\n"));

/***/ })

});