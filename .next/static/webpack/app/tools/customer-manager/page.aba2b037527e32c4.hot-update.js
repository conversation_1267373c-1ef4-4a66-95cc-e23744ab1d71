"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tools/customer-manager/page",{

/***/ "(app-pages-browser)/./src/app/tools/customer-manager/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/tools/customer-manager/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CustomerManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Plus,Search,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Plus,Search,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Plus,Search,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Plus,Search,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Plus,Search,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Edit,Plus,Search,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CustomerManager() {\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCustomer, setEditingCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        fee_agreement: ''\n    });\n    const fetchCustomers = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch('/api/tools/customers');\n            const data = await response.json();\n            if (data.customers) {\n                setCustomers(data.customers);\n            }\n        } catch (error) {\n            console.error('Error fetching customers:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveCustomer = async ()=>{\n        if (!formData.name.trim()) return;\n        setLoading(true);\n        try {\n            const url = editingCustomer ? \"/api/tools/customers/\".concat(editingCustomer.id) : '/api/tools/customers';\n            const method = editingCustomer ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (data.success) {\n                await fetchCustomers();\n                resetForm();\n            }\n        } catch (error) {\n            console.error('Error saving customer:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const deleteCustomer = async (customerId)=>{\n        if (!confirm('Are you sure you want to delete this customer?')) return;\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/tools/customers/\".concat(customerId), {\n                method: 'DELETE'\n            });\n            const data = await response.json();\n            if (data.success) {\n                await fetchCustomers();\n            }\n        } catch (error) {\n            console.error('Error deleting customer:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const editCustomer = (customer)=>{\n        setEditingCustomer(customer);\n        setFormData({\n            name: customer.name,\n            fee_agreement: customer.fee_agreement || ''\n        });\n        setShowForm(true);\n    };\n    const resetForm = ()=>{\n        setFormData({\n            name: '',\n            fee_agreement: ''\n        });\n        setEditingCustomer(null);\n        setShowForm(false);\n    };\n    const filteredCustomers = customers.filter((customer)=>customer.name.toLowerCase().includes(searchTerm.toLowerCase()));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CustomerManager.useEffect\": ()=>{\n            if (session) {\n                fetchCustomers();\n            }\n        }\n    }[\"CustomerManager.useEffect\"], [\n        session\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/\",\n                                        className: \"mr-4 p-2 text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Customer Manager\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowForm(true),\n                                className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Customer\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search by name...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    children: [\n                                        \"Customers (\",\n                                        filteredCustomers.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this) : filteredCustomers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center text-gray-500\",\n                                children: searchTerm ? 'No customers found matching your search.' : 'No customers yet. Add your first customer name to get started.'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"divide-y divide-gray-200\",\n                                children: filteredCustomers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 hover:bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-gray-900\",\n                                                            children: customer.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        customer.fee_agreement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Fee Agreement: \",\n                                                                customer.fee_agreement\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-2 text-xs text-gray-400\",\n                                                            children: [\n                                                                \"Added \",\n                                                                new Date(customer.created_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>editCustomer(customer),\n                                                            className: \"p-2 text-gray-400 hover:text-blue-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deleteCustomer(customer.id),\n                                                            className: \"p-2 text-gray-400 hover:text-red-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Edit_Plus_Search_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, customer.id, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    showForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                    children: editingCustomer ? 'Edit Customer' : 'Add New Customer'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.name,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            name: e.target.value\n                                                        }),\n                                                    className: \"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    placeholder: \"John Doe\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Fee Agreement\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.fee_agreement,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            fee_agreement: e.target.value\n                                                        }),\n                                                    className: \"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    placeholder: \"e.g., $500/hour, Fixed $5000, Contingency 30%\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: resetForm,\n                                            className: \"px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: saveCustomer,\n                                            disabled: loading || !formData.name.trim(),\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                                            children: loading ? 'Saving...' : editingCustomer ? 'Update' : 'Add'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomerManager, \"gs/fy3TsRtlbobjZ/PLx8vyaFaU=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = CustomerManager;\nvar _c;\n$RefreshReg$(_c, \"CustomerManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/tools/customer-manager/page.tsx\n"));

/***/ })

});