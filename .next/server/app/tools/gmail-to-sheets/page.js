(()=>{var e={};e.id=406,e.ids=[406],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5196:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19328:(e,t,s)=>{Promise.resolve().then(s.bind(s,39516))},21171:(e,t,s)=>{Promise.resolve().then(s.bind(s,21741))},21741:(e,t,s)=>{"use strict";s.d(t,{Providers:()=>i});var r=s(60687),a=s(82136);function i({children:e}){return(0,r.jsx)(a.SessionProvider,{children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29519:(e,t,s)=>{"use strict";s.d(t,{Providers:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/aris/src/app/providers.tsx","Providers")},33873:e=>{"use strict";e.exports=require("path")},39516:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(60687),a=s(43210),i=s(82136),o=s(28559),n=s(62688);let l=(0,n.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),d=(0,n.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var c=s(85814),m=s.n(c);function h(){let{data:e}=(0,i.useSession)(),[t,s]=(0,a.useState)([]),[n,c]=(0,a.useState)(!1),[h,u]=(0,a.useState)(""),x=async()=>{c(!0);try{let e=await fetch(`/api/tools/gmail/emails?q=${encodeURIComponent(h)}`),t=await e.json();t.emails&&s(t.emails)}catch(e){console.error("Error fetching emails:",e)}finally{c(!1)}},p=async()=>{if(0!==t.length){c(!0);try{let e=await fetch("/api/tools/gmail/download-to-sheets",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({emails:t})});(await e.json()).success&&alert("Emails downloaded to spreadsheet successfully!")}catch(e){console.error("Error downloading emails:",e)}finally{c(!1)}}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m(),{href:"/",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,r.jsx)(o.A,{className:"w-5 h-5"})}),(0,r.jsx)(l,{className:"w-8 h-8 text-blue-600 mr-3"}),(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Gmail to Sheets"})]}),(0,r.jsxs)("button",{onClick:p,disabled:n||0===t.length,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center disabled:opacity-50",children:[(0,r.jsx)(d,{className:"w-4 h-4 mr-2"}),"Download to Sheets"]})]})})}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Search Gmail"}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("input",{type:"text",value:h,onChange:e=>u(e.target.value),placeholder:"Enter search query (e.g., from:<EMAIL>, subject:invoice)",className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,r.jsx)("button",{onClick:x,disabled:n,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:n?"Searching...":"Search"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:'Use Gmail search operators like "from:", "subject:", "has:attachment", etc.'})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsxs)("h2",{className:"text-lg font-medium text-gray-900",children:["Email Results (",t.length,")"]})}),n?(0,r.jsx)("div",{className:"p-8 text-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"})}):0===t.length?(0,r.jsx)("div",{className:"p-8 text-center text-gray-500",children:h?"No emails found matching your search.":"Enter a search query to find emails."}):(0,r.jsx)("div",{className:"divide-y divide-gray-200 max-h-96 overflow-y-auto",children:t.map(e=>(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:e.subject}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["From: ",e.from]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:e.date}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:e.snippet})]})})},e.id))})]}),(0,r.jsxs)("div",{className:"mt-8 bg-blue-50 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-blue-900 mb-2",children:"How to use Gmail to Sheets"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("li",{children:"• Use Gmail search operators to find specific emails"}),(0,r.jsx)("li",{children:'• Click "Search" to fetch matching emails'}),(0,r.jsx)("li",{children:'• Review the results and click "Download to Sheets" to export'}),(0,r.jsx)("li",{children:"• A new spreadsheet will be created with email data"})]})]})]})]})}},41644:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},55776:(e,t,s)=>{Promise.resolve().then(s.bind(s,78040))},57021:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),o=s.n(i),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let d={children:["",{children:["tools",{children:["gmail-to-sheets",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,78040)),"/Users/<USER>/Documents/GitHub/aris/src/app/tools/gmail-to-sheets/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Documents/GitHub/aris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/GitHub/aris/src/app/tools/gmail-to-sheets/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/tools/gmail-to-sheets/page",pathname:"/tools/gmail-to-sheets",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68027:(e,t,s)=>{Promise.resolve().then(s.bind(s,29519))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},78040:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/gmail-to-sheets/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/aris/src/app/tools/gmail-to-sheets/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>d});var r=s(37413),a=s(2202),i=s.n(a),o=s(64988),n=s.n(o);s(61135);var l=s(29519);let d={title:"API Integration Hub",description:"Securely connect and manage your API integrations"};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${i().variable} ${n().variable} antialiased`,children:(0,r.jsx)(l.Providers,{children:e})})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[243,254,797,644],()=>s(57021));module.exports=r})();