(()=>{var e={};e.id=404,e.ids=[404],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5196:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21171:(e,t,s)=>{Promise.resolve().then(s.bind(s,21741))},21324:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/calendar-to-sheets/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/aris/src/app/tools/calendar-to-sheets/page.tsx","default")},21741:(e,t,s)=>{"use strict";s.d(t,{Providers:()=>n});var r=s(60687),a=s(82136);function n({children:e}){return(0,r.jsx)(a.SessionProvider,{children:e})}},22109:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(60687),a=s(43210),n=s(82136),i=s(28559);let o=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var l=s(96474),d=s(37911),c=s(85814),h=s.n(c);function m(){let{data:e}=(0,n.useSession)(),[t,s]=(0,a.useState)([]),[c,m]=(0,a.useState)([]),[p,u]=(0,a.useState)(!1),[x,v]=(0,a.useState)(!1),[b,g]=(0,a.useState)({name:"",sheetName:"Calendar Events"}),y=async()=>{u(!0);try{let e=await fetch("/api/tools/calendar/events"),t=await e.json();t.events&&s(t.events)}catch(e){console.error("Error fetching calendar events:",e)}finally{u(!1)}},f=async()=>{if(b.name){u(!0);try{let e=await fetch("/api/tools/calendar/create-spreadsheet",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)}),t=await e.json();t.success&&(m([...c,t.spreadsheet]),g({name:"",sheetName:"Calendar Events"}),v(!1))}catch(e){console.error("Error creating spreadsheet:",e)}finally{u(!1)}}},j=async e=>{u(!0);try{let s=await fetch("/api/tools/calendar/update-spreadsheet",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({spreadsheetId:e,events:t})});(await s.json()).success&&alert("Spreadsheet updated successfully!")}catch(e){console.error("Error updating spreadsheet:",e)}finally{u(!1)}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h(),{href:"/",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,r.jsx)(i.A,{className:"w-5 h-5"})}),(0,r.jsx)(o,{className:"w-8 h-8 text-blue-600 mr-3"}),(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Calendar to Sheets"})]}),(0,r.jsxs)("button",{onClick:()=>v(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,r.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Create Spreadsheet"]})]})})}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Recent Calendar Events"}),(0,r.jsx)("button",{onClick:y,disabled:p,className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:p?"Loading...":"Refresh"})]}),(0,r.jsx)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:0===t.length?(0,r.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No calendar events found. Make sure your Google Calendar is connected."}):t.map(e=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:e.summary}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[new Date(e.start.dateTime).toLocaleString()," -",new Date(e.end.dateTime).toLocaleString()]}),e.description&&(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:e.description})]},e.id))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"Connected Spreadsheets"}),(0,r.jsx)("div",{className:"space-y-4",children:0===c.length?(0,r.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No spreadsheets created yet. Create one to start syncing calendar events."}):c.map(e=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Sheet: ",e.sheetName]}),(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[(0,r.jsx)("div",{className:`w-2 h-2 rounded-full mr-2 ${e.isActive?"bg-green-400":"bg-gray-300"}`}),(0,r.jsx)("span",{className:`text-xs ${e.isActive?"text-green-600":"text-gray-500"}`,children:e.isActive?"Active":"Inactive"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>j(e.id),disabled:p,className:"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors",children:"Update"}),(0,r.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(d.A,{className:"w-4 h-4"})})]})]})},e.id))})]})]}),x&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Create New Spreadsheet"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Spreadsheet Name"}),(0,r.jsx)("input",{type:"text",value:b.name,onChange:e=>g({...b,name:e.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"My Calendar Events"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sheet Name"}),(0,r.jsx)("input",{type:"text",value:b.sheetName,onChange:e=>g({...b,sheetName:e.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Calendar Events"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,r.jsx)("button",{onClick:()=>v(!1),className:"px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,r.jsx)("button",{onClick:f,disabled:p||!b.name,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:p?"Creating...":"Create"})]})]})})]})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29519:(e,t,s)=>{"use strict";s.d(t,{Providers:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/aris/src/app/providers.tsx","Providers")},33873:e=>{"use strict";e.exports=require("path")},37911:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},41644:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68027:(e,t,s)=>{Promise.resolve().then(s.bind(s,29519))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},77710:(e,t,s)=>{Promise.resolve().then(s.bind(s,22109))},79551:e=>{"use strict";e.exports=require("url")},91622:(e,t,s)=>{Promise.resolve().then(s.bind(s,21324))},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>d});var r=s(37413),a=s(2202),n=s.n(a),i=s(64988),o=s.n(i);s(61135);var l=s(29519);let d={title:"API Integration Hub",description:"Securely connect and manage your API integrations"};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${o().variable} antialiased`,children:(0,r.jsx)(l.Providers,{children:e})})})}},96317:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["tools",{children:["calendar-to-sheets",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,21324)),"/Users/<USER>/Documents/GitHub/aris/src/app/tools/calendar-to-sheets/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Documents/GitHub/aris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/GitHub/aris/src/app/tools/calendar-to-sheets/page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/tools/calendar-to-sheets/page",pathname:"/tools/calendar-to-sheets",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[243,254,797,644],()=>s(96317));module.exports=r})();