(()=>{var e={};e.id=956,e.ids=[956],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5196:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21171:(e,s,t)=>{Promise.resolve().then(t.bind(t,21741))},21741:(e,s,t)=>{"use strict";t.d(s,{Providers:()=>a});var r=t(60687),n=t(82136);function a({children:e}){return(0,r.jsx)(n.SessionProvider,{children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29519:(e,s,t)=>{"use strict";t.d(s,{Providers:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/aris/src/app/providers.tsx","Providers")},33873:e=>{"use strict";e.exports=require("path")},35974:(e,s,t)=>{Promise.resolve().then(t.bind(t,55919))},37911:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},41644:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},55919:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var r=t(60687),n=t(43210),a=t(82136),i=t(28559);let o=(0,t(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var l=t(96474),c=t(37911),d=t(85814),h=t.n(d);function m(){let{data:e}=(0,a.useSession)(),[s,t]=(0,n.useState)([]),[d,m]=(0,n.useState)([]),[x,u]=(0,n.useState)(!1),[p,b]=(0,n.useState)(!1),[g,v]=(0,n.useState)(""),y=async()=>{try{let e=await fetch("/api/tools/slack/connections"),s=await e.json();s.connections&&m(s.connections)}catch(e){console.error("Error fetching connections:",e)}},f=async()=>{if(g){u(!0);try{let e=await fetch("/api/tools/slack/connect-channel",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({channelId:g})});(await e.json()).success&&(await y(),v(""),b(!1))}catch(e){console.error("Error connecting channel:",e)}finally{u(!1)}}},j=async()=>{window.location.href="/api/auth/slack"};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h(),{href:"/",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,r.jsx)(i.A,{className:"w-5 h-5"})}),(0,r.jsx)(o,{className:"w-8 h-8 text-blue-600 mr-3"}),(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Slack to Sheets"})]}),(0,r.jsxs)("button",{onClick:()=>b(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,r.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Connect Channel"]})]})})}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[0!==s.length||x?(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"Available Channels"}),(0,r.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:s.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-medium text-gray-900",children:["#",e.name]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.is_member?"Member":"Not a member"})]}),(0,r.jsx)("div",{className:"flex items-center",children:d.find(s=>s.channel_id===e.id)?(0,r.jsx)("span",{className:"text-green-600 text-sm font-medium",children:"Connected"}):(0,r.jsx)("span",{className:"text-gray-400 text-sm",children:"Not connected"})})]},e.id))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"Connected Channels"}),0===d.length?(0,r.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No channels connected yet. Connect a channel to start syncing messages."}):(0,r.jsx)("div",{className:"space-y-4",children:d.map(e=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-medium text-gray-900",children:["#",e.channel_name]}),(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[(0,r.jsx)("div",{className:`w-2 h-2 rounded-full mr-2 ${e.is_active?"bg-green-400":"bg-gray-300"}`}),(0,r.jsx)("span",{className:`text-xs ${e.is_active?"text-green-600":"text-gray-500"}`,children:e.is_active?"Active":"Inactive"})]})]}),(0,r.jsx)("a",{href:e.spreadsheet_url,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(c.A,{className:"w-4 h-4"})})]})},e.id))})]})]}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center",children:[(0,r.jsx)(o,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-medium text-gray-900 mb-2",children:"Connect to Slack"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Connect your Slack workspace to start syncing channel messages to spreadsheets."}),(0,r.jsx)("button",{onClick:j,className:"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors",children:"Connect Slack Workspace"})]}),p&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Connect Channel to Spreadsheet"}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Channel"}),(0,r.jsxs)("select",{value:g,onChange:e=>v(e.target.value),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Choose a channel..."}),s.filter(e=>!d.find(s=>s.channel_id===e.id)).map(e=>(0,r.jsxs)("option",{value:e.id,children:["#",e.name]},e.id))]})]})}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,r.jsx)("button",{onClick:()=>b(!1),className:"px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,r.jsx)("button",{onClick:f,disabled:x||!g,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:x?"Connecting...":"Connect"})]})]})}),(0,r.jsxs)("div",{className:"mt-8 bg-blue-50 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-blue-900 mb-2",children:"How to use Slack to Sheets"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("li",{children:"• Connect your Slack workspace to access channels"}),(0,r.jsx)("li",{children:"• Select a channel to connect to a new spreadsheet"}),(0,r.jsx)("li",{children:"• Messages from the channel will be automatically synced"}),(0,r.jsx)("li",{children:"• View and manage your data in the connected spreadsheet"})]})]})]})]})}},56330:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/slack-to-sheets/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/aris/src/app/tools/slack-to-sheets/page.tsx","default")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67518:(e,s,t)=>{Promise.resolve().then(t.bind(t,56330))},68027:(e,s,t)=>{Promise.resolve().then(t.bind(t,29519))},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,metadata:()=>c});var r=t(37413),n=t(2202),a=t.n(n),i=t(64988),o=t.n(i);t(61135);var l=t(29519);let c={title:"API Integration Hub",description:"Securely connect and manage your API integrations"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${a().variable} ${o().variable} antialiased`,children:(0,r.jsx)(l.Providers,{children:e})})})}},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},98681:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=t(65239),n=t(48088),a=t(88170),i=t.n(a),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let c={children:["",{children:["tools",{children:["slack-to-sheets",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,56330)),"/Users/<USER>/Documents/GitHub/aris/src/app/tools/slack-to-sheets/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Documents/GitHub/aris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Documents/GitHub/aris/src/app/tools/slack-to-sheets/page.tsx"],h={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/tools/slack-to-sheets/page",pathname:"/tools/slack-to-sheets",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[243,254,797,644],()=>t(98681));module.exports=r})();