(()=>{var e={};e.id=469,e.ids=[469],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5196:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21171:(e,t,s)=>{Promise.resolve().then(s.bind(s,21741))},21741:(e,t,s)=>{"use strict";s.d(t,{Providers:()=>i});var r=s(60687),a=s(82136);function i({children:e}){return(0,r.jsx)(a.SessionProvider,{children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29519:(e,t,s)=>{"use strict";s.d(t,{Providers:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/aris/src/app/providers.tsx","Providers")},30265:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},41644:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},45001:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c={children:["",{children:["tools",{children:["customer-manager",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,30265)),"/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Documents/GitHub/aris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Documents/GitHub/aris/src/app/tools/customer-manager/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/tools/customer-manager/page",pathname:"/tools/customer-manager",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68027:(e,t,s)=>{Promise.resolve().then(s.bind(s,29519))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},89655:(e,t,s)=>{Promise.resolve().then(s.bind(s,97010))},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>c});var r=s(37413),a=s(2202),i=s.n(a),n=s(64988),o=s.n(n);s(61135);var l=s(29519);let c={title:"API Integration Hub",description:"Securely connect and manage your API integrations"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${i().variable} ${o().variable} antialiased`,children:(0,r.jsx)(l.Providers,{children:e})})})}},95231:(e,t,s)=>{Promise.resolve().then(s.bind(s,30265))},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},97010:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(60687),a=s(43210),i=s(82136),n=s(28559),o=s(62688);let l=(0,o.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var c=s(96474);let d=(0,o.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),m=(0,o.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),u=(0,o.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var x=s(85814),h=s.n(x);function p(){let{data:e}=(0,i.useSession)(),[t,s]=(0,a.useState)([]),[o,x]=(0,a.useState)(!1),[p,g]=(0,a.useState)(!1),[v,b]=(0,a.useState)(null),[y,f]=(0,a.useState)(""),[j,N]=(0,a.useState)({name:"",fee_agreement:"",aka:"",is_active:!0}),w=async()=>{x(!0);try{let e=await fetch("/api/tools/customers"),t=await e.json();t.customers&&s(t.customers)}catch(e){console.error("Error fetching customers:",e)}finally{x(!1)}},k=async()=>{if(j.name.trim()){x(!0);try{let e=v?`/api/tools/customers/${v.id}`:"/api/tools/customers",t=v?"PUT":"POST",s=await fetch(e,{method:t,headers:{"Content-Type":"application/json"},body:JSON.stringify(j)});(await s.json()).success&&(await w(),C())}catch(e){console.error("Error saving customer:",e)}finally{x(!1)}}},P=async e=>{if(confirm("Are you sure you want to delete this customer?")){x(!0);try{let t=await fetch(`/api/tools/customers/${e}`,{method:"DELETE"});(await t.json()).success&&await w()}catch(e){console.error("Error deleting customer:",e)}finally{x(!1)}}},_=e=>{b(e),N({name:e.name,fee_agreement:e.fee_agreement||"",aka:e.aka||"",is_active:e.is_active}),g(!0)},C=()=>{N({name:"",fee_agreement:"",aka:"",is_active:!0}),b(null),g(!1)},A=async(e,t)=>{x(!0);try{let s=await fetch(`/api/tools/customers/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_active:!t})});(await s.json()).success&&await w()}catch(e){console.error("Error updating customer status:",e)}finally{x(!1)}},M=t.filter(e=>e.name.toLowerCase().includes(y.toLowerCase())).sort((e,t)=>e.is_active!==t.is_active?e.is_active?-1:1:e.name.localeCompare(t.name));return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h(),{href:"/",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,r.jsx)(n.A,{className:"w-5 h-5"})}),(0,r.jsx)(l,{className:"w-8 h-8 text-blue-600 mr-3"}),(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Customer Manager"})]}),(0,r.jsxs)("button",{onClick:()=>g(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Add Customer"]})]})})}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d,{className:"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search by name...",value:y,onChange:e=>f(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsxs)("h2",{className:"text-lg font-medium text-gray-900",children:["Customers (",M.length,")"]})}),o?(0,r.jsx)("div",{className:"p-8 text-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"})}):0===M.length?(0,r.jsx)("div",{className:"p-8 text-center text-gray-500",children:y?"No customers found matching your search.":"No customers yet. Add your first customer name to get started."}):(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:M.map(e=>(0,r.jsx)("div",{className:`p-6 hover:bg-gray-50 ${!e.is_active?"opacity-60 bg-gray-50":""}`,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("h3",{className:`text-lg font-medium ${e.is_active?"text-gray-900":"text-gray-500"}`,children:e.name}),(0,r.jsxs)("div",{className:"ml-3 flex items-center",children:[(0,r.jsx)("div",{className:`w-2 h-2 rounded-full mr-2 ${e.is_active?"bg-green-400":"bg-gray-400"}`}),(0,r.jsx)("span",{className:`text-xs ${e.is_active?"text-green-600":"text-gray-500"}`,children:e.is_active?"Active":"Inactive"})]})]}),e.aka&&(0,r.jsxs)("p",{className:`mt-1 text-sm ${e.is_active?"text-gray-600":"text-gray-500"}`,children:["AKA: ",e.aka]}),e.fee_agreement&&(0,r.jsxs)("p",{className:`mt-1 text-sm ${e.is_active?"text-gray-600":"text-gray-500"}`,children:["Fee Agreement: ",e.fee_agreement]}),(0,r.jsxs)("p",{className:"mt-2 text-xs text-gray-400",children:["Added ",new Date(e.created_at).toLocaleDateString()]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>A(e.id,e.is_active),className:`px-3 py-1 text-xs font-medium rounded-full transition-colors ${e.is_active?"bg-red-100 text-red-700 hover:bg-red-200":"bg-green-100 text-green-700 hover:bg-green-200"}`,title:e.is_active?"Mark as Inactive":"Mark as Active",children:e.is_active?"Inactive":"Active"}),(0,r.jsx)("button",{onClick:()=>_(e),className:"p-2 text-gray-400 hover:text-blue-600",children:(0,r.jsx)(m,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:()=>P(e.id),className:"p-2 text-gray-400 hover:text-red-600",children:(0,r.jsx)(u,{className:"w-4 h-4"})})]})]})},e.id))})]}),p&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:v?"Edit Customer":"Add New Customer"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name *"}),(0,r.jsx)("input",{type:"text",value:j.name,onChange:e=>N({...j,name:e.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"John Doe",autoFocus:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"AKA (separated by comma)"}),(0,r.jsx)("input",{type:"text",value:j.aka,onChange:e=>N({...j,aka:e.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., Johnny, J. Doe, John Smith"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fee Agreement"}),(0,r.jsx)("input",{type:"text",value:j.fee_agreement,onChange:e=>N({...j,fee_agreement:e.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., $500/hour, Fixed $5000, Contingency 30%"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:j.is_active,onChange:e=>N({...j,is_active:e.target.checked}),className:"mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Active Client"})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Inactive clients will be grayed out and sorted below active clients"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,r.jsx)("button",{onClick:C,className:"px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,r.jsx)("button",{onClick:k,disabled:o||!j.name.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:o?"Saving...":v?"Update":"Add"})]})]})})]})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[243,254,797,644],()=>s(45001));module.exports=r})();