(()=>{var e={};e.id=772,e.ids=[772],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18477:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>u,serverHooks:()=>l,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>p});var a=r(96559),n=r(48088),o=r(37719),i=r(32190);async function p(){let e={status:"healthy",timestamp:new Date().toISOString(),environment:"production",checks:{nextauth_secret:!!process.env.NEXTAUTH_SECRET,supabase_url:!0,supabase_anon_key:!0,supabase_service_key:!!process.env.SUPABASE_SERVICE_ROLE_KEY,google_client_id:!!process.env.GOOGLE_CLIENT_ID,google_client_secret:!!process.env.GOOGLE_CLIENT_SECRET,encryption_key:!!process.env.ENCRYPTION_KEY}},t=Object.values(e.checks).every(e=>!0===e);return i.NextResponse.json(e,{status:t?200:500})}let u=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"/Users/<USER>/Documents/GitHub/aris/src/app/api/health/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:l}=u;function v(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,580],()=>r(18477));module.exports=s})();