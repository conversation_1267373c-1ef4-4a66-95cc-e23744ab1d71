(()=>{var e={};e.id=748,e.ids=[534,748,936],e.modules={413:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>v,serverHooks:()=>q,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>y});var s={};t.r(s),t.d(s,{DELETE:()=>x,GET:()=>l,POST:()=>d});var o=t(96559),n=t(48088),a=t(37719),i=t(32190),u=t(19854),c=t(6710),p=t(56534);async function l(e){try{let r=await (0,u.getServerSession)();if(!r?.user?.email)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=t.get("service"),o=c.supabase.from("user_credentials").select("*").eq("user_id",r.user.email);s&&(o=o.eq("service",s));let{data:n,error:a}=await o;if(a)return console.error("Database error:",a),i.NextResponse.json({error:"Database error"},{status:500});let l=n?.map(e=>({...e,credentials:(0,p.KG)(e.encrypted_credentials)}));return i.NextResponse.json({credentials:l})}catch(e){return console.error("API error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e){try{let r=await (0,u.getServerSession)();if(!r?.user?.email)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{service:t,credentials:s}=await e.json();if(!t||!s)return i.NextResponse.json({error:"Missing required fields"},{status:400});let o=(0,p.encryptCredentials)(s),{data:n,error:a}=await c.supabase.from("user_credentials").upsert({user_id:r.user.email,service:t,encrypted_credentials:o,updated_at:new Date().toISOString()}).select();if(a)return console.error("Database error:",a),i.NextResponse.json({error:"Database error"},{status:500});return i.NextResponse.json({success:!0,data:n})}catch(e){return console.error("API error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e){try{let r=await (0,u.getServerSession)();if(!r?.user?.email)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=t.get("service");if(!s)return i.NextResponse.json({error:"Service parameter required"},{status:400});let{error:o}=await c.supabase.from("user_credentials").delete().eq("user_id",r.user.email).eq("service",s);if(o)return console.error("Database error:",o),i.NextResponse.json({error:"Database error"},{status:500});return i.NextResponse.json({success:!0})}catch(e){return console.error("API error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let v=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/credentials/route",pathname:"/api/credentials",filename:"route",bundlePath:"app/api/credentials/route"},resolvedPagePath:"/Users/<USER>/Documents/GitHub/aris/src/app/api/credentials/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:y,serverHooks:q}=v;function h(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:y})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,r,t)=>{"use strict";t.d(r,{supabase:()=>n});var s=t(46500);let o=process.env.SUPABASE_SERVICE_ROLE_KEY,n=(0,s.createClient)("https://aeufphhufxbtjekzzorj.supabase.co",o)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,r,t)=>{"use strict";t.d(r,{KG:()=>i,encryptCredentials:()=>a});var s=t(40383),o=t.n(s);let n=process.env.ENCRYPTION_KEY||"default-key-change-in-production",a=e=>{try{let r=JSON.stringify(e);return o().AES.encrypt(r,n).toString()}catch(e){throw console.error("Encryption error:",e),Error("Failed to encrypt credentials")}},i=e=>{try{let r=o().AES.decrypt(e,n).toString(o().enc.Utf8);return JSON.parse(r)}catch(e){throw console.error("Decryption error:",e),Error("Failed to decrypt credentials")}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,500,854,383],()=>t(413));module.exports=s})();