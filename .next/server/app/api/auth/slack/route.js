(()=>{var e={};e.id=385,e.ids=[385],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75976:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>u});var a=t(96559),i=t(48088),o=t(37719),n=t(32190),c=t(19854);async function u(e){try{let r=await (0,c.getServerSession)();if(!r?.user?.email)return n.NextResponse.redirect(new URL("/auth/error?error=unauthorized",e.url));let{searchParams:s}=new URL(e.url),a=s.get("code"),i=s.get("error");if(i)return console.error("Slack OAuth error:",i),n.NextResponse.redirect(new URL("/settings?error=slack_auth_failed",e.url));if(!a){let e=new URL("https://slack.com/oauth/v2/authorize");return e.searchParams.set("client_id",process.env.SLACK_CLIENT_ID),e.searchParams.set("scope","channels:read,chat:write,users:read"),e.searchParams.set("redirect_uri",`${process.env.NEXTAUTH_URL}/api/auth/slack`),e.searchParams.set("state",r.user.email),n.NextResponse.redirect(e.toString())}let o=await fetch("https://slack.com/api/oauth.v2.access",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({client_id:process.env.SLACK_CLIENT_ID,client_secret:process.env.SLACK_CLIENT_SECRET,code:a,redirect_uri:`${process.env.NEXTAUTH_URL}/api/auth/slack`})}),u=await o.json();if(!u.ok)return console.error("Slack token exchange error:",u.error),n.NextResponse.redirect(new URL("/settings?error=slack_token_failed",e.url));let p={access_token:u.access_token,team_id:u.team.id,team_name:u.team.name,user_id:u.authed_user.id,scope:u.scope},{supabase:l}=await Promise.all([t.e(500),t.e(936)]).then(t.bind(t,6710)),{encryptCredentials:d}=await Promise.all([t.e(383),t.e(534)]).then(t.bind(t,56534)),m=d(p);return await l.from("user_credentials").upsert({user_id:r.user.email,service:"slack",encrypted_credentials:m,updated_at:new Date().toISOString()}),await l.from("activity_logs").insert({user_id:r.user.email,tool_name:"slack-integration",action:"connect_slack",status:"success",details:{team_name:u.team.name,team_id:u.team.id}}),n.NextResponse.redirect(new URL("/settings?success=slack_connected",e.url))}catch(r){return console.error("Slack OAuth error:",r),n.NextResponse.redirect(new URL("/settings?error=slack_connection_failed",e.url))}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/slack/route",pathname:"/api/auth/slack",filename:"route",bundlePath:"app/api/auth/slack/route"},resolvedPagePath:"/Users/<USER>/Documents/GitHub/aris/src/app/api/auth/slack/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:m}=p;function h(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,854],()=>t(75976));module.exports=s})();