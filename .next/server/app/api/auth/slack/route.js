/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/slack/route";
exports.ids = ["app/api/auth/slack/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fslack%2Froute&page=%2Fapi%2Fauth%2Fslack%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fslack%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fslack%2Froute&page=%2Fapi%2Fauth%2Fslack%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fslack%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_zack_Documents_GitHub_aris_src_app_api_auth_slack_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/slack/route.ts */ \"(rsc)/./src/app/api/auth/slack/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/slack/route\",\n        pathname: \"/api/auth/slack\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/slack/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/GitHub/aris/src/app/api/auth/slack/route.ts\",\n    nextConfigOutput,\n    userland: _Users_zack_Documents_GitHub_aris_src_app_api_auth_slack_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fslack%2Froute&page=%2Fapi%2Fauth%2Fslack%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fslack%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/slack/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/auth/slack/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)();\n        if (!session?.user?.email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/auth/error?error=unauthorized', request.url));\n        }\n        const { searchParams } = new URL(request.url);\n        const code = searchParams.get('code');\n        const error = searchParams.get('error');\n        if (error) {\n            console.error('Slack OAuth error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/settings?error=slack_auth_failed', request.url));\n        }\n        if (!code) {\n            // Redirect to Slack OAuth\n            const slackAuthUrl = new URL('https://slack.com/oauth/v2/authorize');\n            slackAuthUrl.searchParams.set('client_id', process.env.SLACK_CLIENT_ID);\n            slackAuthUrl.searchParams.set('scope', 'channels:read,chat:write,users:read');\n            slackAuthUrl.searchParams.set('redirect_uri', `${process.env.NEXTAUTH_URL}/api/auth/slack`);\n            slackAuthUrl.searchParams.set('state', session.user.email);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(slackAuthUrl.toString());\n        }\n        // Exchange code for access token\n        const tokenResponse = await fetch('https://slack.com/api/oauth.v2.access', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/x-www-form-urlencoded'\n            },\n            body: new URLSearchParams({\n                client_id: process.env.SLACK_CLIENT_ID,\n                client_secret: process.env.SLACK_CLIENT_SECRET,\n                code,\n                redirect_uri: `${process.env.NEXTAUTH_URL}/api/auth/slack`\n            })\n        });\n        const tokenData = await tokenResponse.json();\n        if (!tokenData.ok) {\n            console.error('Slack token exchange error:', tokenData.error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/settings?error=slack_token_failed', request.url));\n        }\n        // Store encrypted credentials\n        const credentials = {\n            access_token: tokenData.access_token,\n            team_id: tokenData.team.id,\n            team_name: tokenData.team.name,\n            user_id: tokenData.authed_user.id,\n            scope: tokenData.scope\n        };\n        // Save to database (we'll need to import the encryption and database functions)\n        const { supabase } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@supabase\"), __webpack_require__.e(\"vendor-chunks/tr46\"), __webpack_require__.e(\"vendor-chunks/ws\"), __webpack_require__.e(\"vendor-chunks/whatwg-url\"), __webpack_require__.e(\"vendor-chunks/webidl-conversions\"), __webpack_require__.e(\"_rsc_node_modules_supabase_realtime-js_dist_main_sync_recursive-_rsc_src_lib_database_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/database */ \"(rsc)/./src/lib/database.ts\"));\n        const { encryptCredentials } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/crypto-js\"), __webpack_require__.e(\"_rsc_src_lib_encryption_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/encryption */ \"(rsc)/./src/lib/encryption.ts\"));\n        const encryptedCredentials = encryptCredentials(credentials);\n        await supabase.from('user_credentials').upsert({\n            user_id: session.user.email,\n            service: 'slack',\n            encrypted_credentials: encryptedCredentials,\n            updated_at: new Date().toISOString()\n        });\n        // Log activity\n        await supabase.from('activity_logs').insert({\n            user_id: session.user.email,\n            tool_name: 'slack-integration',\n            action: 'connect_slack',\n            status: 'success',\n            details: {\n                team_name: tokenData.team.name,\n                team_id: tokenData.team.id\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/settings?success=slack_connected', request.url));\n    } catch (error) {\n        console.error('Slack OAuth error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/settings?error=slack_connection_failed', request.url));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/slack/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fslack%2Froute&page=%2Fapi%2Fauth%2Fslack%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fslack%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();