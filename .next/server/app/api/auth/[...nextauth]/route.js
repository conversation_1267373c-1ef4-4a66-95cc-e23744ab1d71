/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_zack_Documents_GitHub_aris_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/GitHub/aris/src/app/api/auth/[...nextauth]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_zack_Documents_GitHub_aris_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_encryption__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/encryption */ \"(rsc)/./src/lib/encryption.ts\");\n\n\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()({\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET,\n            authorization: {\n                params: {\n                    scope: 'openid email profile https://www.googleapis.com/auth/calendar https://www.googleapis.com/auth/gmail.readonly https://www.googleapis.com/auth/spreadsheets',\n                    access_type: 'offline',\n                    prompt: 'consent'\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            if (account?.provider === 'google' && account.access_token) {\n                try {\n                    // Store encrypted Google credentials\n                    const credentials = {\n                        access_token: account.access_token,\n                        refresh_token: account.refresh_token,\n                        scope: account.scope,\n                        token_type: account.token_type,\n                        expiry_date: account.expires_at\n                    };\n                    const encryptedCredentials = (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_3__.encryptCredentials)(credentials);\n                    await _lib_database__WEBPACK_IMPORTED_MODULE_2__.supabase.from('user_credentials').upsert({\n                        user_id: user.email,\n                        service: 'google',\n                        encrypted_credentials: encryptedCredentials,\n                        updated_at: new Date().toISOString()\n                    });\n                    return true;\n                } catch (error) {\n                    console.error('Error storing credentials:', error);\n                    return false;\n                }\n            }\n            return true;\n        },\n        async jwt ({ token, account, user }) {\n            if (account) {\n                token.accessToken = account.access_token;\n                token.refreshToken = account.refresh_token;\n            }\n            if (user) {\n                token.userId = user.email;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            session.accessToken = token.accessToken;\n            session.userId = token.userId;\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        error: '/auth/error'\n    },\n    secret: process.env.NEXTAUTH_SECRET\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://aeufphhufxbtjekzzorj.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n// Database initialization SQL\nconst initializeDatabase = async ()=>{\n    const { error } = await supabase.rpc('create_tables', {\n        sql: `\n      -- User credentials table\n      CREATE TABLE IF NOT EXISTS user_credentials (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        service TEXT NOT NULL CHECK (service IN ('google', 'slack')),\n        encrypted_credentials TEXT NOT NULL,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        UNIQUE(user_id, service)\n      );\n\n      -- App configurations table\n      CREATE TABLE IF NOT EXISTS app_configurations (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        tool_name TEXT NOT NULL,\n        configuration JSONB NOT NULL DEFAULT '{}',\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Activity logs table\n      CREATE TABLE IF NOT EXISTS activity_logs (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        tool_name TEXT NOT NULL,\n        action TEXT NOT NULL,\n        status TEXT NOT NULL CHECK (status IN ('success', 'error')),\n        details JSONB DEFAULT '{}',\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Customers table\n      CREATE TABLE IF NOT EXISTS customers (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        name TEXT NOT NULL,\n        email TEXT,\n        company TEXT,\n        phone TEXT,\n        notes TEXT,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Indexes\n      CREATE INDEX IF NOT EXISTS idx_user_credentials_user_id ON user_credentials(user_id);\n      CREATE INDEX IF NOT EXISTS idx_app_configurations_user_id ON app_configurations(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_customers_user_id ON customers(user_id);\n    `\n    });\n    if (error) {\n        console.error('Database initialization error:', error);\n        throw error;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/encryption.ts":
/*!*******************************!*\
  !*** ./src/lib/encryption.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decryptCredentials: () => (/* binding */ decryptCredentials),\n/* harmony export */   encryptCredentials: () => (/* binding */ encryptCredentials),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   validateCredentials: () => (/* binding */ validateCredentials)\n/* harmony export */ });\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto-js */ \"(rsc)/./node_modules/crypto-js/index.js\");\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';\nconst encryptCredentials = (credentials)=>{\n    try {\n        const jsonString = JSON.stringify(credentials);\n        const encrypted = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.encrypt(jsonString, ENCRYPTION_KEY).toString();\n        return encrypted;\n    } catch (error) {\n        console.error('Encryption error:', error);\n        throw new Error('Failed to encrypt credentials');\n    }\n};\nconst decryptCredentials = (encryptedData)=>{\n    try {\n        const bytes = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.decrypt(encryptedData, ENCRYPTION_KEY);\n        const decryptedString = bytes.toString((crypto_js__WEBPACK_IMPORTED_MODULE_0___default().enc).Utf8);\n        return JSON.parse(decryptedString);\n    } catch (error) {\n        console.error('Decryption error:', error);\n        throw new Error('Failed to decrypt credentials');\n    }\n};\nconst hashPassword = (password)=>{\n    return crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(password).toString();\n};\nconst validateCredentials = (credentials, service)=>{\n    if (service === 'google') {\n        return !!(credentials.access_token && credentials.refresh_token && credentials.scope && credentials.token_type);\n    }\n    if (service === 'slack') {\n        return !!(credentials.access_token && credentials.team_id && credentials.user_id && credentials.scope);\n    }\n    return false;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/encryption.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/crypto-js","vendor-chunks/jose","vendor-chunks/ws","vendor-chunks/openid-client","vendor-chunks/whatwg-url","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();