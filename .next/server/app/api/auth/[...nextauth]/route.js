(()=>{var e={};e.id=14,e.ids=[14,534,936],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,r,t)=>{"use strict";t.d(r,{supabase:()=>i});var s=t(46500);let o=process.env.SUPABASE_SERVICE_ROLE_KEY,i=(0,s.createClient)("https://aeufphhufxbtjekzzorj.supabase.co",o)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},36344:(e,r)=>{"use strict";r.A=function(e){return{id:"google",name:"Google",type:"oauth",wellKnown:"https://accounts.google.com/.well-known/openid-configuration",authorization:{params:{scope:"openid email profile"}},idToken:!0,checks:["pkce","state"],profile:e=>({id:e.sub,name:e.name,email:e.email,image:e.picture}),style:{logo:"/google.svg",bg:"#fff",text:"#000"},options:e}}},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,r,t)=>{"use strict";t.d(r,{KG:()=>a,encryptCredentials:()=>n});var s=t(40383),o=t.n(s);let i=process.env.ENCRYPTION_KEY||"default-key-change-in-production",n=e=>{try{let r=JSON.stringify(e);return o().AES.encrypt(r,i).toString()}catch(e){throw console.error("Encryption error:",e),Error("Failed to encrypt credentials")}},a=e=>{try{let r=o().AES.decrypt(e,i).toString(o().enc.Utf8);return JSON.parse(r)}catch(e){throw console.error("Decryption error:",e),Error("Failed to decrypt credentials")}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63571:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>h,serverHooks:()=>y,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>d});var o=t(96559),i=t(48088),n=t(37719),a=t(19854),c=t.n(a),u=t(36344),p=t(6710),l=t(56534);let d=c()({providers:[(0,u.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{scope:"openid email profile https://www.googleapis.com/auth/calendar https://www.googleapis.com/auth/gmail.readonly https://www.googleapis.com/auth/spreadsheets",access_type:"offline",prompt:"consent"}}})],callbacks:{async signIn({user:e,account:r,profile:t}){if(r?.provider==="google"&&r.access_token)try{let t={access_token:r.access_token,refresh_token:r.refresh_token,scope:r.scope,token_type:r.token_type,expiry_date:r.expires_at},s=(0,l.encryptCredentials)(t);await p.supabase.from("user_credentials").upsert({user_id:e.email,service:"google",encrypted_credentials:s,updated_at:new Date().toISOString()})}catch(e){return console.error("Error storing credentials:",e),!1}return!0},jwt:async({token:e,account:r,user:t})=>(r&&(e.accessToken=r.access_token,e.refreshToken=r.refresh_token),t&&(e.userId=t.email),e),session:async({session:e,token:r})=>(e.accessToken=r.accessToken,e.userId=r.userId,e)},secret:process.env.NEXTAUTH_SECRET}),h=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"/Users/<USER>/Documents/GitHub/aris/src/app/api/auth/[...nextauth]/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:y}=h;function m(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},96559:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,500,854,383],()=>t(63571));module.exports=s})();