(()=>{var e={};e.id=543,e.ids=[534,543,936],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,r,t)=>{"use strict";t.d(r,{supabase:()=>a});var s=t(46500);let o=process.env.SUPABASE_SERVICE_ROLE_KEY,a=(0,s.createClient)("https://aeufphhufxbtjekzzorj.supabase.co",o)},7326:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>h,serverHooks:()=>m,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>_});var s={};t.r(s),t.d(s,{POST:()=>d});var o=t(96559),a=t(48088),n=t(37719),i=t(32190),c=t(19854),u=t(25976),l=t(6710),p=t(56534);async function d(e){try{let r=await (0,c.getServerSession)();if(!r?.user?.email)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{channelId:t}=await e.json();if(!t)return i.NextResponse.json({error:"Channel ID is required"},{status:400});let{data:s,error:o}=await l.supabase.from("user_credentials").select("encrypted_credentials").eq("user_id",r.user.email).eq("service","slack").single();if(o||!s)return i.NextResponse.json({error:"Slack credentials not found"},{status:404});let a=(0,p.KG)(s.encrypted_credentials),n=await fetch(`https://slack.com/api/conversations.info?channel=${t}`,{headers:{Authorization:`Bearer ${a.access_token}`,"Content-Type":"application/json"}}),d=await n.json();if(!d.ok)return console.error("Slack API error:",d.error),i.NextResponse.json({error:"Failed to get channel info"},{status:500});let h=d.channel.name,{data:x,error:_}=await l.supabase.from("user_credentials").select("encrypted_credentials").eq("user_id",r.user.email).eq("service","google").single();if(_||!x)return i.NextResponse.json({error:"Google credentials not found"},{status:404});let m=(0,p.KG)(x.encrypted_credentials),g=new u.q7g.auth.OAuth2(process.env.GOOGLE_CLIENT_ID,process.env.GOOGLE_CLIENT_SECRET,process.env.NEXTAUTH_URL+"/api/auth/callback/google");g.setCredentials({access_token:m.access_token,refresh_token:m.refresh_token});let q=u.q7g.sheets({version:"v4",auth:g}),f=`Slack #${h} Messages`,v=(await q.spreadsheets.create({requestBody:{properties:{title:f},sheets:[{properties:{title:"Messages"}}]}})).data.spreadsheetId,k=`https://docs.google.com/spreadsheets/d/${v}`;await q.spreadsheets.values.update({spreadsheetId:v,range:"Messages!A1:E1",valueInputOption:"RAW",requestBody:{values:[["Timestamp","User","Message","Thread","Reactions"]]}}),await q.spreadsheets.batchUpdate({spreadsheetId:v,requestBody:{requests:[{repeatCell:{range:{sheetId:0,startRowIndex:0,endRowIndex:1,startColumnIndex:0,endColumnIndex:5},cell:{userEnteredFormat:{backgroundColor:{red:.9,green:.9,blue:.9},textFormat:{bold:!0}}},fields:"userEnteredFormat(backgroundColor,textFormat)"}}]}});let{data:y,error:w}=await l.supabase.from("app_configurations").insert({user_id:r.user.email,tool_name:"slack-to-sheets",configuration:{channel_id:t,channel_name:h,spreadsheet_id:v,spreadsheet_url:k,team_id:a.team_id,team_name:a.team_name},is_active:!0}).select().single();if(w)return console.error("Database error:",w),i.NextResponse.json({error:"Failed to save configuration"},{status:500});return await l.supabase.from("activity_logs").insert({user_id:r.user.email,tool_name:"slack-to-sheets",action:"connect_channel",status:"success",details:{channel_id:t,channel_name:h,spreadsheet_id:v}}),i.NextResponse.json({success:!0,connection:{id:y.id,channel_id:t,channel_name:h,spreadsheet_url:k,is_active:!0}})}catch(r){console.error("Slack connect channel error:",r);let e=await (0,c.getServerSession)();return e?.user?.email&&await l.supabase.from("activity_logs").insert({user_id:e.user.email,tool_name:"slack-to-sheets",action:"connect_channel",status:"error",details:{error:r instanceof Error?r.message:"Unknown error"}}),i.NextResponse.json({error:"Failed to connect channel"},{status:500})}}let h=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/tools/slack/connect-channel/route",pathname:"/api/tools/slack/connect-channel",filename:"route",bundlePath:"app/api/tools/slack/connect-channel/route"},resolvedPagePath:"/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/slack/connect-channel/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:_,serverHooks:m}=h;function g(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:_})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,r,t)=>{"use strict";t.d(r,{KG:()=>i,encryptCredentials:()=>n});var s=t(40383),o=t.n(s);let a=process.env.ENCRYPTION_KEY||"default-key-change-in-production",n=e=>{try{let r=JSON.stringify(e);return o().AES.encrypt(r,a).toString()}catch(e){throw console.error("Encryption error:",e),Error("Failed to encrypt credentials")}},i=e=>{try{let r=o().AES.decrypt(e,a).toString(o().enc.Utf8);return JSON.parse(r)}catch(e){throw console.error("Decryption error:",e),Error("Failed to decrypt credentials")}}},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,500,854,383,976],()=>t(7326));module.exports=s})();