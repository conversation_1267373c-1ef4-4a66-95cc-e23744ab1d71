(()=>{var e={};e.id=269,e.ids=[269,534,936],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,r,t)=>{"use strict";t.d(r,{supabase:()=>a});var s=t(46500);let o=process.env.SUPABASE_SERVICE_ROLE_KEY,a=(0,s.createClient)("https://aeufphhufxbtjekzzorj.supabase.co",o)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,r,t)=>{"use strict";t.d(r,{KG:()=>i,encryptCredentials:()=>n});var s=t(40383),o=t.n(s);let a=process.env.ENCRYPTION_KEY||"default-key-change-in-production",n=e=>{try{let r=JSON.stringify(e);return o().AES.encrypt(r,a).toString()}catch(e){throw console.error("Encryption error:",e),Error("Failed to encrypt credentials")}},i=e=>{try{let r=o().AES.decrypt(e,a).toString(o().enc.Utf8);return JSON.parse(r)}catch(e){throw console.error("Decryption error:",e),Error("Failed to decrypt credentials")}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92004:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(96559),a=t(48088),n=t(37719),i=t(32190),c=t(19854),u=t(6710),l=t(56534);async function p(e){try{let e=await (0,c.getServerSession)();if(!e?.user?.email)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:r,error:t}=await u.supabase.from("user_credentials").select("encrypted_credentials").eq("user_id",e.user.email).eq("service","slack").single();if(t||!r)return i.NextResponse.json({error:"Slack credentials not found"},{status:404});let s=(0,l.KG)(r.encrypted_credentials),o=await fetch("https://slack.com/api/conversations.list",{headers:{Authorization:`Bearer ${s.access_token}`,"Content-Type":"application/json"}}),a=await o.json();if(!a.ok)return console.error("Slack API error:",a.error),i.NextResponse.json({error:"Failed to fetch Slack channels"},{status:500});let n=a.channels.filter(e=>!e.is_archived&&e.is_channel).map(e=>({id:e.id,name:e.name,is_member:e.is_member,is_private:e.is_private,num_members:e.num_members}));return await u.supabase.from("activity_logs").insert({user_id:e.user.email,tool_name:"slack-to-sheets",action:"fetch_channels",status:"success",details:{channels_count:n.length}}),i.NextResponse.json({channels:n})}catch(r){console.error("Slack channels API error:",r);let e=await (0,c.getServerSession)();return e?.user?.email&&await u.supabase.from("activity_logs").insert({user_id:e.user.email,tool_name:"slack-to-sheets",action:"fetch_channels",status:"error",details:{error:r instanceof Error?r.message:"Unknown error"}}),i.NextResponse.json({error:"Failed to fetch channels"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/tools/slack/channels/route",pathname:"/api/tools/slack/channels",filename:"route",bundlePath:"app/api/tools/slack/channels/route"},resolvedPagePath:"/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/slack/channels/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:x,serverHooks:m}=d;function f(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:x})}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,500,854,383],()=>t(92004));module.exports=s})();