(()=>{var e={};e.id=512,e.ids=[512,936],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,r,t)=>{"use strict";t.d(r,{supabase:()=>i});var s=t(46500);let o=process.env.SUPABASE_SERVICE_ROLE_KEY,i=(0,s.createClient)("https://aeufphhufxbtjekzzorj.supabase.co",o)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69618:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(96559),i=t(48088),n=t(37719),a=t(32190),u=t(19854),c=t(6710);async function p(e){try{let e=await (0,u.getServerSession)();if(!e?.user?.email)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{data:r,error:t}=await c.supabase.from("app_configurations").select("*").eq("user_id",e.user.email).eq("tool_name","slack-to-sheets").eq("is_active",!0);if(t)return console.error("Database error:",t),a.NextResponse.json({error:"Database error"},{status:500});let s=r?.map(e=>({id:e.id,channel_id:e.configuration.channel_id,channel_name:e.configuration.channel_name,spreadsheet_url:e.configuration.spreadsheet_url,spreadsheet_id:e.configuration.spreadsheet_id,is_active:e.is_active,created_at:e.created_at}))||[];return a.NextResponse.json({connections:s})}catch(e){return console.error("Slack connections API error:",e),a.NextResponse.json({error:"Failed to fetch connections"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/tools/slack/connections/route",pathname:"/api/tools/slack/connections",filename:"route",bundlePath:"app/api/tools/slack/connections/route"},resolvedPagePath:"/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/slack/connections/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:x,serverHooks:h}=l;function q(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:x})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,500,854],()=>t(69618));module.exports=s})();