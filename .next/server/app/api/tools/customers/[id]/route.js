/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/tools/customers/[id]/route";
exports.ids = ["app/api/tools/customers/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fcustomers%2F%5Bid%5D%2Froute&page=%2Fapi%2Ftools%2Fcustomers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fcustomers%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fcustomers%2F%5Bid%5D%2Froute&page=%2Fapi%2Ftools%2Fcustomers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fcustomers%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_zack_Documents_GitHub_aris_src_app_api_tools_customers_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/tools/customers/[id]/route.ts */ \"(rsc)/./src/app/api/tools/customers/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/tools/customers/[id]/route\",\n        pathname: \"/api/tools/customers/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/tools/customers/[id]/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/customers/[id]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_zack_Documents_GitHub_aris_src_app_api_tools_customers_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fcustomers%2F%5Bid%5D%2Froute&page=%2Fapi%2Ftools%2Fcustomers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fcustomers%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/tools/customers/[id]/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/tools/customers/[id]/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n\n\n\nasync function PUT(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)();\n        if (!session?.user?.email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { name, fee_agreement, aka, is_active } = await request.json();\n        // Handle status-only updates (when toggling active/inactive)\n        if (is_active !== undefined && !name) {\n            const { data, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.supabase.from('customers').update({\n                is_active,\n                updated_at: new Date().toISOString()\n            }).eq('id', params.id).eq('user_id', session.user.email).select().single();\n            if (error) {\n                console.error('Database error:', error);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Database error'\n                }, {\n                    status: 500\n                });\n            }\n            if (!data) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Customer not found'\n                }, {\n                    status: 404\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                customer: data\n            });\n        }\n        // Handle full updates\n        if (!name || !name.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Name is required'\n            }, {\n                status: 400\n            });\n        }\n        const { data, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.supabase.from('customers').update({\n            name: name.trim(),\n            fee_agreement: fee_agreement || null,\n            aka: aka || null,\n            is_active: is_active !== undefined ? is_active : true,\n            updated_at: new Date().toISOString()\n        }).eq('id', params.id).eq('user_id', session.user.email).select().single();\n        if (error) {\n            console.error('Database error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Database error'\n            }, {\n                status: 500\n            });\n        }\n        if (!data) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Customer not found'\n            }, {\n                status: 404\n            });\n        }\n        // Log activity\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.supabase.from('activity_logs').insert({\n            user_id: session.user.email,\n            tool_name: 'customer-manager',\n            action: 'update_customer',\n            status: 'success',\n            details: {\n                customer_id: params.id,\n                name\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            customer: data\n        });\n    } catch (error) {\n        console.error('API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request, { params }) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)();\n        if (!session?.user?.email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // First get the customer to log the name\n        const { data: customer } = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.supabase.from('customers').select('name').eq('id', params.id).eq('user_id', session.user.email).single();\n        const { error } = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.supabase.from('customers').delete().eq('id', params.id).eq('user_id', session.user.email);\n        if (error) {\n            console.error('Database error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Database error'\n            }, {\n                status: 500\n            });\n        }\n        // Log activity\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.supabase.from('activity_logs').insert({\n            user_id: session.user.email,\n            tool_name: 'customer-manager',\n            action: 'delete_customer',\n            status: 'success',\n            details: {\n                customer_id: params.id,\n                name: customer?.name || 'Unknown'\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error('API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/tools/customers/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://aeufphhufxbtjekzzorj.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n// Database initialization SQL\nconst initializeDatabase = async ()=>{\n    const { error } = await supabase.rpc('create_tables', {\n        sql: `\n      -- User credentials table\n      CREATE TABLE IF NOT EXISTS user_credentials (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        service TEXT NOT NULL CHECK (service IN ('google', 'slack')),\n        encrypted_credentials TEXT NOT NULL,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        UNIQUE(user_id, service)\n      );\n\n      -- App configurations table\n      CREATE TABLE IF NOT EXISTS app_configurations (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        tool_name TEXT NOT NULL,\n        configuration JSONB NOT NULL DEFAULT '{}',\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Activity logs table\n      CREATE TABLE IF NOT EXISTS activity_logs (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        tool_name TEXT NOT NULL,\n        action TEXT NOT NULL,\n        status TEXT NOT NULL CHECK (status IN ('success', 'error')),\n        details JSONB DEFAULT '{}',\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Customers table\n      CREATE TABLE IF NOT EXISTS customers (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        name TEXT NOT NULL,\n        fee_agreement TEXT,\n        aka TEXT,\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Indexes\n      CREATE INDEX IF NOT EXISTS idx_user_credentials_user_id ON user_credentials(user_id);\n      CREATE INDEX IF NOT EXISTS idx_app_configurations_user_id ON app_configurations(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_customers_user_id ON customers(user_id);\n    `\n    });\n    if (error) {\n        console.error('Database initialization error:', error);\n        throw error;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/jose","vendor-chunks/ws","vendor-chunks/openid-client","vendor-chunks/whatwg-url","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fcustomers%2F%5Bid%5D%2Froute&page=%2Fapi%2Ftools%2Fcustomers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fcustomers%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();