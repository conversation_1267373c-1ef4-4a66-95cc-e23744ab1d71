(()=>{var e={};e.id=441,e.ids=[441,936],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,r,s)=>{"use strict";s.d(r,{supabase:()=>a});var t=s(46500);let o=process.env.SUPABASE_SERVICE_ROLE_KEY,a=(0,t.createClient)("https://aeufphhufxbtjekzzorj.supabase.co",o)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},98781:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>q,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{DELETE:()=>d,PUT:()=>p});var o=s(96559),a=s(48088),u=s(37719),i=s(32190),n=s(19854),c=s(6710);async function p(e,{params:r}){try{let s=await (0,n.getServerSession)();if(!s?.user?.email)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{name:t,fee_agreement:o,aka:a,is_active:u}=await e.json();if(void 0!==u&&!t){let{data:e,error:t}=await c.supabase.from("customers").update({is_active:u,updated_at:new Date().toISOString()}).eq("id",r.id).eq("user_id",s.user.email).select().single();if(t)return console.error("Database error:",t),i.NextResponse.json({error:"Database error"},{status:500});if(!e)return i.NextResponse.json({error:"Customer not found"},{status:404});return i.NextResponse.json({success:!0,customer:e})}if(!t||!t.trim())return i.NextResponse.json({error:"Name is required"},{status:400});let{data:p,error:d}=await c.supabase.from("customers").update({name:t.trim(),fee_agreement:o||null,aka:a||null,is_active:void 0===u||u,updated_at:new Date().toISOString()}).eq("id",r.id).eq("user_id",s.user.email).select().single();if(d)return console.error("Database error:",d),i.NextResponse.json({error:"Database error"},{status:500});if(!p)return i.NextResponse.json({error:"Customer not found"},{status:404});return await c.supabase.from("activity_logs").insert({user_id:s.user.email,tool_name:"customer-manager",action:"update_customer",status:"success",details:{customer_id:r.id,name:t}}),i.NextResponse.json({success:!0,customer:p})}catch(e){return console.error("API error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e,{params:r}){try{let e=await (0,n.getServerSession)();if(!e?.user?.email)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s}=await c.supabase.from("customers").select("name").eq("id",r.id).eq("user_id",e.user.email).single(),{error:t}=await c.supabase.from("customers").delete().eq("id",r.id).eq("user_id",e.user.email);if(t)return console.error("Database error:",t),i.NextResponse.json({error:"Database error"},{status:500});return await c.supabase.from("activity_logs").insert({user_id:e.user.email,tool_name:"customer-manager",action:"delete_customer",status:"success",details:{customer_id:r.id,name:s?.name||"Unknown"}}),i.NextResponse.json({success:!0})}catch(e){return console.error("API error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/tools/customers/[id]/route",pathname:"/api/tools/customers/[id]",filename:"route",bundlePath:"app/api/tools/customers/[id]/route"},resolvedPagePath:"/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/customers/[id]/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:f}=l;function q(){return(0,u.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,580,500,854],()=>s(98781));module.exports=t})();