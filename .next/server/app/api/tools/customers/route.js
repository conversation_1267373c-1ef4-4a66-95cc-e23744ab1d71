(()=>{var e={};e.id=113,e.ids=[113,936],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,r,s)=>{"use strict";s.d(r,{supabase:()=>u});var t=s(46500);let o=process.env.SUPABASE_SERVICE_ROLE_KEY,u=(0,t.createClient)("https://aeufphhufxbtjekzzorj.supabase.co",o)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84786:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>q,routeModule:()=>d,serverHooks:()=>v,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var t={};s.r(t),s.d(t,{GET:()=>p,POST:()=>l});var o=s(96559),u=s(48088),a=s(37719),i=s(32190),n=s(19854),c=s(6710);async function p(e){try{let e=await (0,n.getServerSession)();if(!e?.user?.email)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:r,error:s}=await c.supabase.from("customers").select("*").eq("user_id",e.user.email).order("is_active",{ascending:!1}).order("name",{ascending:!0});if(s)return console.error("Database error:",s),i.NextResponse.json({error:"Database error"},{status:500});return i.NextResponse.json({customers:r})}catch(e){return console.error("API error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e){try{let r=await (0,n.getServerSession)();if(!r?.user?.email)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{name:s,fee_agreement:t,aka:o,is_active:u}=await e.json();if(!s||!s.trim())return i.NextResponse.json({error:"Name is required"},{status:400});let{data:a,error:p}=await c.supabase.from("customers").insert({user_id:r.user.email,name:s.trim(),fee_agreement:t||null,aka:o||null,is_active:void 0===u||u}).select().single();if(p)return console.error("Database error:",p),i.NextResponse.json({error:"Database error"},{status:500});return await c.supabase.from("activity_logs").insert({user_id:r.user.email,tool_name:"customer-manager",action:"create_customer",status:"success",details:{customer_id:a.id,name:s}}),i.NextResponse.json({success:!0,customer:a})}catch(e){return console.error("API error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/tools/customers/route",pathname:"/api/tools/customers",filename:"route",bundlePath:"app/api/tools/customers/route"},resolvedPagePath:"/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/customers/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:v}=d;function q(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,580,500,854],()=>s(84786));module.exports=t})();