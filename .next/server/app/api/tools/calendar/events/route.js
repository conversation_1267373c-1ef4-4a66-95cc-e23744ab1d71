(()=>{var e={};e.id=640,e.ids=[534,640,936],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,r,t)=>{"use strict";t.d(r,{supabase:()=>i});var s=t(46500);let o=process.env.SUPABASE_SERVICE_ROLE_KEY,i=(0,s.createClient)("https://aeufphhufxbtjekzzorj.supabase.co",o)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,r,t)=>{"use strict";t.d(r,{KG:()=>n,encryptCredentials:()=>a});var s=t(40383),o=t.n(s);let i=process.env.ENCRYPTION_KEY||"default-key-change-in-production",a=e=>{try{let r=JSON.stringify(e);return o().AES.encrypt(r,i).toString()}catch(e){throw console.error("Encryption error:",e),Error("Failed to encrypt credentials")}},n=e=>{try{let r=o().AES.decrypt(e,i).toString(o().enc.Utf8);return JSON.parse(r)}catch(e){throw console.error("Decryption error:",e),Error("Failed to decrypt credentials")}}},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66098:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>x,serverHooks:()=>h,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>d});var o=t(96559),i=t(48088),a=t(37719),n=t(32190),u=t(19854),c=t(25976),p=t(6710),l=t(56534);async function d(e){try{let e=await (0,u.getServerSession)();if(!e?.user?.email)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{data:r,error:t}=await p.supabase.from("user_credentials").select("encrypted_credentials").eq("user_id",e.user.email).eq("service","google").single();if(t||!r)return n.NextResponse.json({error:"Google credentials not found"},{status:404});let s=(0,l.KG)(r.encrypted_credentials),o=new c.q7g.auth.OAuth2(process.env.GOOGLE_CLIENT_ID,process.env.GOOGLE_CLIENT_SECRET,process.env.NEXTAUTH_URL+"/api/auth/callback/google");o.setCredentials({access_token:s.access_token,refresh_token:s.refresh_token});let i=c.q7g.calendar({version:"v3",auth:o}),a=new Date;a.setDate(a.getDate()-30);let d=new Date;d.setDate(d.getDate()+30);let x=await i.events.list({calendarId:"primary",timeMin:a.toISOString(),timeMax:d.toISOString(),maxResults:50,singleEvents:!0,orderBy:"startTime"}),v=x.data.items?.map(e=>({id:e.id,summary:e.summary||"No title",start:e.start,end:e.end,description:e.description,location:e.location,attendees:e.attendees?.map(e=>e.email)||[]}))||[];return await p.supabase.from("activity_logs").insert({user_id:e.user.email,tool_name:"calendar-to-sheets",action:"fetch_events",status:"success",details:{event_count:v.length}}),n.NextResponse.json({events:v})}catch(r){console.error("Calendar API error:",r);let e=await (0,u.getServerSession)();return e?.user?.email&&await p.supabase.from("activity_logs").insert({user_id:e.user.email,tool_name:"calendar-to-sheets",action:"fetch_events",status:"error",details:{error:r instanceof Error?r.message:"Unknown error"}}),n.NextResponse.json({error:"Failed to fetch calendar events"},{status:500})}}let x=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/tools/calendar/events/route",pathname:"/api/tools/calendar/events",filename:"route",bundlePath:"app/api/tools/calendar/events/route"},resolvedPagePath:"/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/calendar/events/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:m,serverHooks:h}=x;function q(){return(0,a.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:m})}},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,500,854,383,976],()=>t(66098));module.exports=s})();