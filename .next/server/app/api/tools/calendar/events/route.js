/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/tools/calendar/events/route";
exports.ids = ["app/api/tools/calendar/events/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fcalendar%2Fevents%2Froute&page=%2Fapi%2Ftools%2Fcalendar%2Fevents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fcalendar%2Fevents%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fcalendar%2Fevents%2Froute&page=%2Fapi%2Ftools%2Fcalendar%2Fevents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fcalendar%2Fevents%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_zack_Documents_GitHub_aris_src_app_api_tools_calendar_events_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/tools/calendar/events/route.ts */ \"(rsc)/./src/app/api/tools/calendar/events/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/tools/calendar/events/route\",\n        pathname: \"/api/tools/calendar/events\",\n        filename: \"route\",\n        bundlePath: \"app/api/tools/calendar/events/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/calendar/events/route.ts\",\n    nextConfigOutput,\n    userland: _Users_zack_Documents_GitHub_aris_src_app_api_tools_calendar_events_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fcalendar%2Fevents%2Froute&page=%2Fapi%2Ftools%2Fcalendar%2Fevents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fcalendar%2Fevents%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/tools/calendar/events/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/tools/calendar/events/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var googleapis__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! googleapis */ \"(rsc)/./node_modules/googleapis/build/src/index.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_encryption__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/encryption */ \"(rsc)/./src/lib/encryption.ts\");\n\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)();\n        if (!session?.user?.email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get Google credentials from database\n        const { data: credentialsData, error: credentialsError } = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.supabase.from('user_credentials').select('encrypted_credentials').eq('user_id', session.user.email).eq('service', 'google').single();\n        if (credentialsError || !credentialsData) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Google credentials not found'\n            }, {\n                status: 404\n            });\n        }\n        const credentials = (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_3__.decryptCredentials)(credentialsData.encrypted_credentials);\n        // Set up Google Calendar API\n        const oauth2Client = new googleapis__WEBPACK_IMPORTED_MODULE_4__.google.auth.OAuth2(process.env.GOOGLE_CLIENT_ID, process.env.GOOGLE_CLIENT_SECRET, process.env.NEXTAUTH_URL + '/api/auth/callback/google');\n        oauth2Client.setCredentials({\n            access_token: credentials.access_token,\n            refresh_token: credentials.refresh_token\n        });\n        const calendar = googleapis__WEBPACK_IMPORTED_MODULE_4__.google.calendar({\n            version: 'v3',\n            auth: oauth2Client\n        });\n        // Get calendar events from the last 30 days\n        const timeMin = new Date();\n        timeMin.setDate(timeMin.getDate() - 30);\n        const timeMax = new Date();\n        timeMax.setDate(timeMax.getDate() + 30);\n        const response = await calendar.events.list({\n            calendarId: 'primary',\n            timeMin: timeMin.toISOString(),\n            timeMax: timeMax.toISOString(),\n            maxResults: 50,\n            singleEvents: true,\n            orderBy: 'startTime'\n        });\n        const events = response.data.items?.map((event)=>({\n                id: event.id,\n                summary: event.summary || 'No title',\n                start: event.start,\n                end: event.end,\n                description: event.description,\n                location: event.location,\n                attendees: event.attendees?.map((attendee)=>attendee.email) || []\n            })) || [];\n        // Log activity\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.supabase.from('activity_logs').insert({\n            user_id: session.user.email,\n            tool_name: 'calendar-to-sheets',\n            action: 'fetch_events',\n            status: 'success',\n            details: {\n                event_count: events.length\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            events\n        });\n    } catch (error) {\n        console.error('Calendar API error:', error);\n        // Log error\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)();\n        if (session?.user?.email) {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_2__.supabase.from('activity_logs').insert({\n                user_id: session.user.email,\n                tool_name: 'calendar-to-sheets',\n                action: 'fetch_events',\n                status: 'error',\n                details: {\n                    error: error instanceof Error ? error.message : 'Unknown error'\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch calendar events'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/tools/calendar/events/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://aeufphhufxbtjekzzorj.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n// Database initialization SQL\nconst initializeDatabase = async ()=>{\n    const { error } = await supabase.rpc('create_tables', {\n        sql: `\n      -- User credentials table\n      CREATE TABLE IF NOT EXISTS user_credentials (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        service TEXT NOT NULL CHECK (service IN ('google', 'slack')),\n        encrypted_credentials TEXT NOT NULL,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        UNIQUE(user_id, service)\n      );\n\n      -- App configurations table\n      CREATE TABLE IF NOT EXISTS app_configurations (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        tool_name TEXT NOT NULL,\n        configuration JSONB NOT NULL DEFAULT '{}',\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Activity logs table\n      CREATE TABLE IF NOT EXISTS activity_logs (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        tool_name TEXT NOT NULL,\n        action TEXT NOT NULL,\n        status TEXT NOT NULL CHECK (status IN ('success', 'error')),\n        details JSONB DEFAULT '{}',\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Customers table\n      CREATE TABLE IF NOT EXISTS customers (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        name TEXT NOT NULL,\n        email TEXT,\n        company TEXT,\n        phone TEXT,\n        notes TEXT,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Indexes\n      CREATE INDEX IF NOT EXISTS idx_user_credentials_user_id ON user_credentials(user_id);\n      CREATE INDEX IF NOT EXISTS idx_app_configurations_user_id ON app_configurations(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_customers_user_id ON customers(user_id);\n    `\n    });\n    if (error) {\n        console.error('Database initialization error:', error);\n        throw error;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/encryption.ts":
/*!*******************************!*\
  !*** ./src/lib/encryption.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decryptCredentials: () => (/* binding */ decryptCredentials),\n/* harmony export */   encryptCredentials: () => (/* binding */ encryptCredentials),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   validateCredentials: () => (/* binding */ validateCredentials)\n/* harmony export */ });\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto-js */ \"(rsc)/./node_modules/crypto-js/index.js\");\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';\nconst encryptCredentials = (credentials)=>{\n    try {\n        const jsonString = JSON.stringify(credentials);\n        const encrypted = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.encrypt(jsonString, ENCRYPTION_KEY).toString();\n        return encrypted;\n    } catch (error) {\n        console.error('Encryption error:', error);\n        throw new Error('Failed to encrypt credentials');\n    }\n};\nconst decryptCredentials = (encryptedData)=>{\n    try {\n        const bytes = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.decrypt(encryptedData, ENCRYPTION_KEY);\n        const decryptedString = bytes.toString((crypto_js__WEBPACK_IMPORTED_MODULE_0___default().enc).Utf8);\n        return JSON.parse(decryptedString);\n    } catch (error) {\n        console.error('Decryption error:', error);\n        throw new Error('Failed to decrypt credentials');\n    }\n};\nconst hashPassword = (password)=>{\n    return crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(password).toString();\n};\nconst validateCredentials = (credentials, service)=>{\n    if (service === 'google') {\n        return !!(credentials.access_token && credentials.refresh_token && credentials.scope && credentials.token_type);\n    }\n    if (service === 'slack') {\n        return !!(credentials.access_token && credentials.team_id && credentials.user_id && credentials.scope);\n    }\n    return false;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/encryption.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/crypto-js","vendor-chunks/jose","vendor-chunks/ws","vendor-chunks/openid-client","vendor-chunks/whatwg-url","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/googleapis","vendor-chunks/google-auth-library","vendor-chunks/uuid","vendor-chunks/googleapis-common","vendor-chunks/math-intrinsics","vendor-chunks/gaxios","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/jws","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/json-bigint","vendor-chunks/google-logging-utils","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/https-proxy-agent","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/gcp-metadata","vendor-chunks/function-bind","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/agent-base","vendor-chunks/node-fetch","vendor-chunks/url-template","vendor-chunks/supports-color","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/jwa","vendor-chunks/is-stream","vendor-chunks/hasown","vendor-chunks/has-flag","vendor-chunks/gtoken","vendor-chunks/get-intrinsic","vendor-chunks/extend","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound","vendor-chunks/buffer-equal-constant-time","vendor-chunks/bignumber.js","vendor-chunks/base64-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fcalendar%2Fevents%2Froute&page=%2Fapi%2Ftools%2Fcalendar%2Fevents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fcalendar%2Fevents%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();