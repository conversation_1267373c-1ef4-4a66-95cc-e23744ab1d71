(()=>{var e={};e.id=409,e.ids=[409,534,936],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,r,t)=>{"use strict";t.d(r,{supabase:()=>o});var s=t(46500);let a=process.env.SUPABASE_SERVICE_ROLE_KEY,o=(0,s.createClient)("https://aeufphhufxbtjekzzorj.supabase.co",a)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,r,t)=>{"use strict";t.d(r,{KG:()=>n,encryptCredentials:()=>i});var s=t(40383),a=t.n(s);let o=process.env.ENCRYPTION_KEY||"default-key-change-in-production",i=e=>{try{let r=JSON.stringify(e);return a().AES.encrypt(r,o).toString()}catch(e){throw console.error("Encryption error:",e),Error("Failed to encrypt credentials")}},n=e=>{try{let r=a().AES.decrypt(e,o).toString(a().enc.Utf8);return JSON.parse(r)}catch(e){throw console.error("Decryption error:",e),Error("Failed to decrypt credentials")}}},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71943:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>h,serverHooks:()=>q,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{POST:()=>l});var a=t(96559),o=t(48088),i=t(37719),n=t(32190),u=t(19854),p=t(25976),c=t(6710),d=t(56534);async function l(e){try{let r=await (0,u.getServerSession)();if(!r?.user?.email)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{spreadsheetId:t,events:s}=await e.json();if(!t||!s)return n.NextResponse.json({error:"Missing required fields"},{status:400});let{data:a,error:o}=await c.supabase.from("user_credentials").select("encrypted_credentials").eq("user_id",r.user.email).eq("service","google").single();if(o||!a)return n.NextResponse.json({error:"Google credentials not found"},{status:404});let i=(0,d.KG)(a.encrypted_credentials),{data:l,error:h}=await c.supabase.from("app_configurations").select("configuration").eq("user_id",r.user.email).eq("tool_name","calendar-to-sheets").eq("id",t).single();if(h||!l)return n.NextResponse.json({error:"Spreadsheet configuration not found"},{status:404});let x=l.configuration,g=x.spreadsheet_id,q=x.sheet_name,m=new p.q7g.auth.OAuth2(process.env.GOOGLE_CLIENT_ID,process.env.GOOGLE_CLIENT_SECRET,process.env.NEXTAUTH_URL+"/api/auth/callback/google");m.setCredentials({access_token:i.access_token,refresh_token:i.refresh_token});let v=p.q7g.sheets({version:"v4",auth:m});await v.spreadsheets.values.clear({spreadsheetId:g,range:`${q}!A2:G`});let _=s.map(e=>{let r=e.start?.dateTime?new Date(e.start.dateTime):null,t=e.end?.dateTime?new Date(e.end.dateTime):null;return[e.summary||"No title",r?r.toLocaleDateString():"",r?r.toLocaleTimeString():"",t?t.toLocaleDateString():"",t?t.toLocaleTimeString():"",e.description||"",e.location||""]});return _.length>0&&await v.spreadsheets.values.update({spreadsheetId:g,range:`${q}!A2:G${_.length+1}`,valueInputOption:"RAW",requestBody:{values:_}}),await c.supabase.from("activity_logs").insert({user_id:r.user.email,tool_name:"calendar-to-sheets",action:"update_spreadsheet",status:"success",details:{spreadsheet_id:g,events_count:_.length}}),n.NextResponse.json({success:!0,message:`Updated spreadsheet with ${_.length} events`})}catch(r){console.error("Spreadsheet update error:",r);let e=await (0,u.getServerSession)();return e?.user?.email&&await c.supabase.from("activity_logs").insert({user_id:e.user.email,tool_name:"calendar-to-sheets",action:"update_spreadsheet",status:"error",details:{error:r instanceof Error?r.message:"Unknown error"}}),n.NextResponse.json({error:"Failed to update spreadsheet"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/tools/calendar/update-spreadsheet/route",pathname:"/api/tools/calendar/update-spreadsheet",filename:"route",bundlePath:"app/api/tools/calendar/update-spreadsheet/route"},resolvedPagePath:"/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/calendar/update-spreadsheet/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:q}=h;function m(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,500,854,383,976],()=>t(71943));module.exports=s})();