(()=>{var e={};e.id=558,e.ids=[534,558,936],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,r,t)=>{"use strict";t.d(r,{supabase:()=>a});var s=t(46500);let o=process.env.SUPABASE_SERVICE_ROLE_KEY,a=(0,s.createClient)("https://aeufphhufxbtjekzzorj.supabase.co",o)},8686:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>_,routeModule:()=>h,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>q});var s={};t.r(s),t.d(s,{POST:()=>l});var o=t(96559),a=t(48088),i=t(37719),n=t(32190),u=t(19854),c=t(25976),p=t(6710),d=t(56534);async function l(e){try{let r=await (0,u.getServerSession)();if(!r?.user?.email)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{name:t,sheetName:s}=await e.json();if(!t||!s)return n.NextResponse.json({error:"Missing required fields"},{status:400});let{data:o,error:a}=await p.supabase.from("user_credentials").select("encrypted_credentials").eq("user_id",r.user.email).eq("service","google").single();if(a||!o)return n.NextResponse.json({error:"Google credentials not found"},{status:404});let i=(0,d.KG)(o.encrypted_credentials),l=new c.q7g.auth.OAuth2(process.env.GOOGLE_CLIENT_ID,process.env.GOOGLE_CLIENT_SECRET,process.env.NEXTAUTH_URL+"/api/auth/callback/google");l.setCredentials({access_token:i.access_token,refresh_token:i.refresh_token});let h=c.q7g.sheets({version:"v4",auth:l}),x=(await h.spreadsheets.create({requestBody:{properties:{title:t},sheets:[{properties:{title:s}}]}})).data.spreadsheetId,q=`https://docs.google.com/spreadsheets/d/${x}`;await h.spreadsheets.values.update({spreadsheetId:x,range:`${s}!A1:G1`,valueInputOption:"RAW",requestBody:{values:[["Title","Start Date","Start Time","End Date","End Time","Description","Location"]]}}),await h.spreadsheets.batchUpdate({spreadsheetId:x,requestBody:{requests:[{repeatCell:{range:{sheetId:0,startRowIndex:0,endRowIndex:1,startColumnIndex:0,endColumnIndex:7},cell:{userEnteredFormat:{backgroundColor:{red:.9,green:.9,blue:.9},textFormat:{bold:!0}}},fields:"userEnteredFormat(backgroundColor,textFormat)"}}]}});let{data:g,error:_}=await p.supabase.from("app_configurations").insert({user_id:r.user.email,tool_name:"calendar-to-sheets",configuration:{spreadsheet_id:x,spreadsheet_name:t,sheet_name:s,spreadsheet_url:q},is_active:!0}).select().single();if(_)return console.error("Database error:",_),n.NextResponse.json({error:"Failed to save configuration"},{status:500});await p.supabase.from("activity_logs").insert({user_id:r.user.email,tool_name:"calendar-to-sheets",action:"create_spreadsheet",status:"success",details:{spreadsheet_id:x,name:t}});let m={id:g.id,name:t,url:q,sheetName:s,isActive:!0};return n.NextResponse.json({success:!0,spreadsheet:m})}catch(r){console.error("Spreadsheet creation error:",r);let e=await (0,u.getServerSession)();return e?.user?.email&&await p.supabase.from("activity_logs").insert({user_id:e.user.email,tool_name:"calendar-to-sheets",action:"create_spreadsheet",status:"error",details:{error:r instanceof Error?r.message:"Unknown error"}}),n.NextResponse.json({error:"Failed to create spreadsheet"},{status:500})}}let h=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/tools/calendar/create-spreadsheet/route",pathname:"/api/tools/calendar/create-spreadsheet",filename:"route",bundlePath:"app/api/tools/calendar/create-spreadsheet/route"},resolvedPagePath:"/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/calendar/create-spreadsheet/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:q,serverHooks:g}=h;function _(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:q})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,r,t)=>{"use strict";t.d(r,{KG:()=>n,encryptCredentials:()=>i});var s=t(40383),o=t.n(s);let a=process.env.ENCRYPTION_KEY||"default-key-change-in-production",i=e=>{try{let r=JSON.stringify(e);return o().AES.encrypt(r,a).toString()}catch(e){throw console.error("Encryption error:",e),Error("Failed to encrypt credentials")}},n=e=>{try{let r=o().AES.decrypt(e,a).toString(o().enc.Utf8);return JSON.parse(r)}catch(e){throw console.error("Decryption error:",e),Error("Failed to decrypt credentials")}}},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,500,854,383,976],()=>t(8686));module.exports=s})();