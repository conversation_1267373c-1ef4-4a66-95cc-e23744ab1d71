/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/tools/calendar/create-spreadsheet/route";
exports.ids = ["app/api/tools/calendar/create-spreadsheet/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fcalendar%2Fcreate-spreadsheet%2Froute&page=%2Fapi%2Ftools%2Fcalendar%2Fcreate-spreadsheet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fcalendar%2Fcreate-spreadsheet%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fcalendar%2Fcreate-spreadsheet%2Froute&page=%2Fapi%2Ftools%2Fcalendar%2Fcreate-spreadsheet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fcalendar%2Fcreate-spreadsheet%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_zack_Documents_GitHub_aris_src_app_api_tools_calendar_create_spreadsheet_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/tools/calendar/create-spreadsheet/route.ts */ \"(rsc)/./src/app/api/tools/calendar/create-spreadsheet/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/tools/calendar/create-spreadsheet/route\",\n        pathname: \"/api/tools/calendar/create-spreadsheet\",\n        filename: \"route\",\n        bundlePath: \"app/api/tools/calendar/create-spreadsheet/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/GitHub/aris/src/app/api/tools/calendar/create-spreadsheet/route.ts\",\n    nextConfigOutput,\n    userland: _Users_zack_Documents_GitHub_aris_src_app_api_tools_calendar_create_spreadsheet_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fcalendar%2Fcreate-spreadsheet%2Froute&page=%2Fapi%2Ftools%2Fcalendar%2Fcreate-spreadsheet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fcalendar%2Fcreate-spreadsheet%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/tools/calendar/create-spreadsheet/route.ts":
/*!****************************************************************!*\
  !*** ./src/app/api/tools/calendar/create-spreadsheet/route.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var googleapis__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! googleapis */ \"(rsc)/./node_modules/googleapis/build/src/index.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_encryption__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/encryption */ \"(rsc)/./src/lib/encryption.ts\");\n\n\n\n\n\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)();\n        if (!session?.user?.email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { name, sheetName } = await request.json();\n        if (!name || !sheetName) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Get Google credentials from database\n        const { data: credentialsData, error: credentialsError } = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.supabase.from('user_credentials').select('encrypted_credentials').eq('user_id', session.user.email).eq('service', 'google').single();\n        if (credentialsError || !credentialsData) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Google credentials not found'\n            }, {\n                status: 404\n            });\n        }\n        const credentials = (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_3__.decryptCredentials)(credentialsData.encrypted_credentials);\n        // Set up Google Sheets API\n        const oauth2Client = new googleapis__WEBPACK_IMPORTED_MODULE_4__.google.auth.OAuth2(process.env.GOOGLE_CLIENT_ID, process.env.GOOGLE_CLIENT_SECRET, process.env.NEXTAUTH_URL + '/api/auth/callback/google');\n        oauth2Client.setCredentials({\n            access_token: credentials.access_token,\n            refresh_token: credentials.refresh_token\n        });\n        const sheets = googleapis__WEBPACK_IMPORTED_MODULE_4__.google.sheets({\n            version: 'v4',\n            auth: oauth2Client\n        });\n        // Create new spreadsheet\n        const createResponse = await sheets.spreadsheets.create({\n            requestBody: {\n                properties: {\n                    title: name\n                },\n                sheets: [\n                    {\n                        properties: {\n                            title: sheetName\n                        }\n                    }\n                ]\n            }\n        });\n        const spreadsheetId = createResponse.data.spreadsheetId;\n        const spreadsheetUrl = `https://docs.google.com/spreadsheets/d/${spreadsheetId}`;\n        // Add headers to the sheet\n        await sheets.spreadsheets.values.update({\n            spreadsheetId,\n            range: `${sheetName}!A1:G1`,\n            valueInputOption: 'RAW',\n            requestBody: {\n                values: [\n                    [\n                        'Title',\n                        'Start Date',\n                        'Start Time',\n                        'End Date',\n                        'End Time',\n                        'Description',\n                        'Location'\n                    ]\n                ]\n            }\n        });\n        // Format headers\n        await sheets.spreadsheets.batchUpdate({\n            spreadsheetId,\n            requestBody: {\n                requests: [\n                    {\n                        repeatCell: {\n                            range: {\n                                sheetId: 0,\n                                startRowIndex: 0,\n                                endRowIndex: 1,\n                                startColumnIndex: 0,\n                                endColumnIndex: 7\n                            },\n                            cell: {\n                                userEnteredFormat: {\n                                    backgroundColor: {\n                                        red: 0.9,\n                                        green: 0.9,\n                                        blue: 0.9\n                                    },\n                                    textFormat: {\n                                        bold: true\n                                    }\n                                }\n                            },\n                            fields: 'userEnteredFormat(backgroundColor,textFormat)'\n                        }\n                    }\n                ]\n            }\n        });\n        // Save configuration to database\n        const { data: configData, error: configError } = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.supabase.from('app_configurations').insert({\n            user_id: session.user.email,\n            tool_name: 'calendar-to-sheets',\n            configuration: {\n                spreadsheet_id: spreadsheetId,\n                spreadsheet_name: name,\n                sheet_name: sheetName,\n                spreadsheet_url: spreadsheetUrl\n            },\n            is_active: true\n        }).select().single();\n        if (configError) {\n            console.error('Database error:', configError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to save configuration'\n            }, {\n                status: 500\n            });\n        }\n        // Log activity\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.supabase.from('activity_logs').insert({\n            user_id: session.user.email,\n            tool_name: 'calendar-to-sheets',\n            action: 'create_spreadsheet',\n            status: 'success',\n            details: {\n                spreadsheet_id: spreadsheetId,\n                name\n            }\n        });\n        const spreadsheetConfig = {\n            id: configData.id,\n            name,\n            url: spreadsheetUrl,\n            sheetName,\n            isActive: true\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            spreadsheet: spreadsheetConfig\n        });\n    } catch (error) {\n        console.error('Spreadsheet creation error:', error);\n        // Log error\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)();\n        if (session?.user?.email) {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_2__.supabase.from('activity_logs').insert({\n                user_id: session.user.email,\n                tool_name: 'calendar-to-sheets',\n                action: 'create_spreadsheet',\n                status: 'error',\n                details: {\n                    error: error instanceof Error ? error.message : 'Unknown error'\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create spreadsheet'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/tools/calendar/create-spreadsheet/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://aeufphhufxbtjekzzorj.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n// Database initialization SQL\nconst initializeDatabase = async ()=>{\n    const { error } = await supabase.rpc('create_tables', {\n        sql: `\n      -- User credentials table\n      CREATE TABLE IF NOT EXISTS user_credentials (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        service TEXT NOT NULL CHECK (service IN ('google', 'slack')),\n        encrypted_credentials TEXT NOT NULL,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        UNIQUE(user_id, service)\n      );\n\n      -- App configurations table\n      CREATE TABLE IF NOT EXISTS app_configurations (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        tool_name TEXT NOT NULL,\n        configuration JSONB NOT NULL DEFAULT '{}',\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Activity logs table\n      CREATE TABLE IF NOT EXISTS activity_logs (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        tool_name TEXT NOT NULL,\n        action TEXT NOT NULL,\n        status TEXT NOT NULL CHECK (status IN ('success', 'error')),\n        details JSONB DEFAULT '{}',\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Customers table\n      CREATE TABLE IF NOT EXISTS customers (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        name TEXT NOT NULL,\n        email TEXT,\n        company TEXT,\n        phone TEXT,\n        notes TEXT,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Indexes\n      CREATE INDEX IF NOT EXISTS idx_user_credentials_user_id ON user_credentials(user_id);\n      CREATE INDEX IF NOT EXISTS idx_app_configurations_user_id ON app_configurations(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_customers_user_id ON customers(user_id);\n    `\n    });\n    if (error) {\n        console.error('Database initialization error:', error);\n        throw error;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RhdGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvRDtBQUVwRCxNQUFNQyxjQUFjQywwQ0FBb0M7QUFDeEQsTUFBTUcscUJBQXFCSCxRQUFRQyxHQUFHLENBQUNHLHlCQUF5QjtBQUV6RCxNQUFNQyxXQUFXUCxtRUFBWUEsQ0FBQ0MsYUFBYUksb0JBQW1CO0FBNENyRSw4QkFBOEI7QUFDdkIsTUFBTUcscUJBQXFCO0lBQ2hDLE1BQU0sRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUYsU0FBU0csR0FBRyxDQUFDLGlCQUFpQjtRQUNwREMsS0FBSyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBb0ROLENBQUM7SUFDSDtJQUVBLElBQUlGLE9BQU87UUFDVEcsUUFBUUgsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDaEQsTUFBTUE7SUFDUjtBQUNGLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy96YWNrL0RvY3VtZW50cy9HaXRIdWIvYXJpcy9zcmMvbGliL2RhdGFiYXNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9zdXBhYmFzZS1qcydcblxuY29uc3Qgc3VwYWJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhXG5jb25zdCBzdXBhYmFzZVNlcnZpY2VLZXkgPSBwcm9jZXNzLmVudi5TVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIVxuXG5leHBvcnQgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQoc3VwYWJhc2VVcmwsIHN1cGFiYXNlU2VydmljZUtleSlcblxuLy8gRGF0YWJhc2Ugc2NoZW1hIHR5cGVzXG5leHBvcnQgaW50ZXJmYWNlIFVzZXJDcmVkZW50aWFscyB7XG4gIGlkOiBzdHJpbmdcbiAgdXNlcl9pZDogc3RyaW5nXG4gIHNlcnZpY2U6ICdnb29nbGUnIHwgJ3NsYWNrJ1xuICBlbmNyeXB0ZWRfY3JlZGVudGlhbHM6IHN0cmluZ1xuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQXBwQ29uZmlndXJhdGlvbiB7XG4gIGlkOiBzdHJpbmdcbiAgdXNlcl9pZDogc3RyaW5nXG4gIHRvb2xfbmFtZTogc3RyaW5nXG4gIGNvbmZpZ3VyYXRpb246IGFueVxuICBpc19hY3RpdmU6IGJvb2xlYW5cbiAgY3JlYXRlZF9hdDogc3RyaW5nXG4gIHVwZGF0ZWRfYXQ6IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEFjdGl2aXR5TG9nIHtcbiAgaWQ6IHN0cmluZ1xuICB1c2VyX2lkOiBzdHJpbmdcbiAgdG9vbF9uYW1lOiBzdHJpbmdcbiAgYWN0aW9uOiBzdHJpbmdcbiAgc3RhdHVzOiAnc3VjY2VzcycgfCAnZXJyb3InXG4gIGRldGFpbHM6IGFueVxuICBjcmVhdGVkX2F0OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDdXN0b21lciB7XG4gIGlkOiBzdHJpbmdcbiAgdXNlcl9pZDogc3RyaW5nXG4gIG5hbWU6IHN0cmluZ1xuICBlbWFpbDogc3RyaW5nXG4gIGNvbXBhbnk6IHN0cmluZ1xuICBwaG9uZT86IHN0cmluZ1xuICBub3Rlcz86IHN0cmluZ1xuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG59XG5cbi8vIERhdGFiYXNlIGluaXRpYWxpemF0aW9uIFNRTFxuZXhwb3J0IGNvbnN0IGluaXRpYWxpemVEYXRhYmFzZSA9IGFzeW5jICgpID0+IHtcbiAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UucnBjKCdjcmVhdGVfdGFibGVzJywge1xuICAgIHNxbDogYFxuICAgICAgLS0gVXNlciBjcmVkZW50aWFscyB0YWJsZVxuICAgICAgQ1JFQVRFIFRBQkxFIElGIE5PVCBFWElTVFMgdXNlcl9jcmVkZW50aWFscyAoXG4gICAgICAgIGlkIFVVSUQgREVGQVVMVCBnZW5fcmFuZG9tX3V1aWQoKSBQUklNQVJZIEtFWSxcbiAgICAgICAgdXNlcl9pZCBURVhUIE5PVCBOVUxMLFxuICAgICAgICBzZXJ2aWNlIFRFWFQgTk9UIE5VTEwgQ0hFQ0sgKHNlcnZpY2UgSU4gKCdnb29nbGUnLCAnc2xhY2snKSksXG4gICAgICAgIGVuY3J5cHRlZF9jcmVkZW50aWFscyBURVhUIE5PVCBOVUxMLFxuICAgICAgICBjcmVhdGVkX2F0IFRJTUVTVEFNUCBXSVRIIFRJTUUgWk9ORSBERUZBVUxUIE5PVygpLFxuICAgICAgICB1cGRhdGVkX2F0IFRJTUVTVEFNUCBXSVRIIFRJTUUgWk9ORSBERUZBVUxUIE5PVygpLFxuICAgICAgICBVTklRVUUodXNlcl9pZCwgc2VydmljZSlcbiAgICAgICk7XG5cbiAgICAgIC0tIEFwcCBjb25maWd1cmF0aW9ucyB0YWJsZVxuICAgICAgQ1JFQVRFIFRBQkxFIElGIE5PVCBFWElTVFMgYXBwX2NvbmZpZ3VyYXRpb25zIChcbiAgICAgICAgaWQgVVVJRCBERUZBVUxUIGdlbl9yYW5kb21fdXVpZCgpIFBSSU1BUlkgS0VZLFxuICAgICAgICB1c2VyX2lkIFRFWFQgTk9UIE5VTEwsXG4gICAgICAgIHRvb2xfbmFtZSBURVhUIE5PVCBOVUxMLFxuICAgICAgICBjb25maWd1cmF0aW9uIEpTT05CIE5PVCBOVUxMIERFRkFVTFQgJ3t9JyxcbiAgICAgICAgaXNfYWN0aXZlIEJPT0xFQU4gREVGQVVMVCB0cnVlLFxuICAgICAgICBjcmVhdGVkX2F0IFRJTUVTVEFNUCBXSVRIIFRJTUUgWk9ORSBERUZBVUxUIE5PVygpLFxuICAgICAgICB1cGRhdGVkX2F0IFRJTUVTVEFNUCBXSVRIIFRJTUUgWk9ORSBERUZBVUxUIE5PVygpXG4gICAgICApO1xuXG4gICAgICAtLSBBY3Rpdml0eSBsb2dzIHRhYmxlXG4gICAgICBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyBhY3Rpdml0eV9sb2dzIChcbiAgICAgICAgaWQgVVVJRCBERUZBVUxUIGdlbl9yYW5kb21fdXVpZCgpIFBSSU1BUlkgS0VZLFxuICAgICAgICB1c2VyX2lkIFRFWFQgTk9UIE5VTEwsXG4gICAgICAgIHRvb2xfbmFtZSBURVhUIE5PVCBOVUxMLFxuICAgICAgICBhY3Rpb24gVEVYVCBOT1QgTlVMTCxcbiAgICAgICAgc3RhdHVzIFRFWFQgTk9UIE5VTEwgQ0hFQ0sgKHN0YXR1cyBJTiAoJ3N1Y2Nlc3MnLCAnZXJyb3InKSksXG4gICAgICAgIGRldGFpbHMgSlNPTkIgREVGQVVMVCAne30nLFxuICAgICAgICBjcmVhdGVkX2F0IFRJTUVTVEFNUCBXSVRIIFRJTUUgWk9ORSBERUZBVUxUIE5PVygpXG4gICAgICApO1xuXG4gICAgICAtLSBDdXN0b21lcnMgdGFibGVcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIGN1c3RvbWVycyAoXG4gICAgICAgIGlkIFVVSUQgREVGQVVMVCBnZW5fcmFuZG9tX3V1aWQoKSBQUklNQVJZIEtFWSxcbiAgICAgICAgdXNlcl9pZCBURVhUIE5PVCBOVUxMLFxuICAgICAgICBuYW1lIFRFWFQgTk9UIE5VTEwsXG4gICAgICAgIGVtYWlsIFRFWFQsXG4gICAgICAgIGNvbXBhbnkgVEVYVCxcbiAgICAgICAgcGhvbmUgVEVYVCxcbiAgICAgICAgbm90ZXMgVEVYVCxcbiAgICAgICAgY3JlYXRlZF9hdCBUSU1FU1RBTVAgV0lUSCBUSU1FIFpPTkUgREVGQVVMVCBOT1coKSxcbiAgICAgICAgdXBkYXRlZF9hdCBUSU1FU1RBTVAgV0lUSCBUSU1FIFpPTkUgREVGQVVMVCBOT1coKVxuICAgICAgKTtcblxuICAgICAgLS0gSW5kZXhlc1xuICAgICAgQ1JFQVRFIElOREVYIElGIE5PVCBFWElTVFMgaWR4X3VzZXJfY3JlZGVudGlhbHNfdXNlcl9pZCBPTiB1c2VyX2NyZWRlbnRpYWxzKHVzZXJfaWQpO1xuICAgICAgQ1JFQVRFIElOREVYIElGIE5PVCBFWElTVFMgaWR4X2FwcF9jb25maWd1cmF0aW9uc191c2VyX2lkIE9OIGFwcF9jb25maWd1cmF0aW9ucyh1c2VyX2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9hY3Rpdml0eV9sb2dzX3VzZXJfaWQgT04gYWN0aXZpdHlfbG9ncyh1c2VyX2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9jdXN0b21lcnNfdXNlcl9pZCBPTiBjdXN0b21lcnModXNlcl9pZCk7XG4gICAgYFxuICB9KVxuXG4gIGlmIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0RhdGFiYXNlIGluaXRpYWxpemF0aW9uIGVycm9yOicsIGVycm9yKVxuICAgIHRocm93IGVycm9yXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJzdXBhYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJzdXBhYmFzZVNlcnZpY2VLZXkiLCJTVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIiwic3VwYWJhc2UiLCJpbml0aWFsaXplRGF0YWJhc2UiLCJlcnJvciIsInJwYyIsInNxbCIsImNvbnNvbGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/encryption.ts":
/*!*******************************!*\
  !*** ./src/lib/encryption.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decryptCredentials: () => (/* binding */ decryptCredentials),\n/* harmony export */   encryptCredentials: () => (/* binding */ encryptCredentials),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   validateCredentials: () => (/* binding */ validateCredentials)\n/* harmony export */ });\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto-js */ \"(rsc)/./node_modules/crypto-js/index.js\");\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';\nconst encryptCredentials = (credentials)=>{\n    try {\n        const jsonString = JSON.stringify(credentials);\n        const encrypted = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.encrypt(jsonString, ENCRYPTION_KEY).toString();\n        return encrypted;\n    } catch (error) {\n        console.error('Encryption error:', error);\n        throw new Error('Failed to encrypt credentials');\n    }\n};\nconst decryptCredentials = (encryptedData)=>{\n    try {\n        const bytes = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.decrypt(encryptedData, ENCRYPTION_KEY);\n        const decryptedString = bytes.toString((crypto_js__WEBPACK_IMPORTED_MODULE_0___default().enc).Utf8);\n        return JSON.parse(decryptedString);\n    } catch (error) {\n        console.error('Decryption error:', error);\n        throw new Error('Failed to decrypt credentials');\n    }\n};\nconst hashPassword = (password)=>{\n    return crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(password).toString();\n};\nconst validateCredentials = (credentials, service)=>{\n    if (service === 'google') {\n        return !!(credentials.access_token && credentials.refresh_token && credentials.scope && credentials.token_type);\n    }\n    if (service === 'slack') {\n        return !!(credentials.access_token && credentials.team_id && credentials.user_id && credentials.scope);\n    }\n    return false;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/encryption.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/crypto-js","vendor-chunks/jose","vendor-chunks/ws","vendor-chunks/openid-client","vendor-chunks/whatwg-url","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/googleapis","vendor-chunks/google-auth-library","vendor-chunks/bignumber.js","vendor-chunks/node-fetch","vendor-chunks/googleapis-common","vendor-chunks/gaxios","vendor-chunks/qs","vendor-chunks/json-bigint","vendor-chunks/google-logging-utils","vendor-chunks/object-inspect","vendor-chunks/gcp-metadata","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/https-proxy-agent","vendor-chunks/gtoken","vendor-chunks/uuid","vendor-chunks/agent-base","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/url-template","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/base64-js","vendor-chunks/side-channel-list","vendor-chunks/extend","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/side-channel-weakmap","vendor-chunks/has-symbols","vendor-chunks/function-bind","vendor-chunks/side-channel-map","vendor-chunks/safe-buffer","vendor-chunks/side-channel","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/buffer-equal-constant-time","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/call-bound","vendor-chunks/is-stream","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftools%2Fcalendar%2Fcreate-spreadsheet%2Froute&page=%2Fapi%2Ftools%2Fcalendar%2Fcreate-spreadsheet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Fcalendar%2Fcreate-spreadsheet%2Froute.ts&appDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzack%2FDocuments%2FGitHub%2Faris&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();