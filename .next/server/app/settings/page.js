(()=>{var e={};e.id=662,e.ids=[662],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5196:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21171:(e,s,t)=>{Promise.resolve().then(t.bind(t,21741))},21741:(e,s,t)=>{"use strict";t.d(s,{Providers:()=>n});var r=t(60687),i=t(82136);function n({children:e}){return(0,r.jsx)(i.SessionProvider,{children:e})}},23134:(e,s,t)=>{Promise.resolve().then(t.bind(t,84003))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29519:(e,s,t)=>{"use strict";t.d(s,{Providers:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/aris/src/app/providers.tsx","Providers")},33873:e=>{"use strict";e.exports=require("path")},41644:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},42173:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(65239),i=t(48088),n=t(88170),a=t.n(n),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d={children:["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,74198)),"/Users/<USER>/Documents/GitHub/aris/src/app/settings/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Documents/GitHub/aris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/GitHub/aris/src/app/settings/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68027:(e,s,t)=>{Promise.resolve().then(t.bind(t,29519))},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74198:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/GitHub/aris/src/app/settings/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/aris/src/app/settings/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},84003:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var r=t(60687),i=t(43210),n=t(82136),a=t(28559),l=t(62688);let o=(0,l.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),d=(0,l.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),c=(0,l.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var m=t(99891),x=t(85814),h=t.n(x);function u(){let{data:e}=(0,n.useSession)(),[s,t]=(0,i.useState)([]),[l,x]=(0,i.useState)(!1),u=async()=>{x(!0);try{let e=await fetch("/api/credentials"),s=await e.json(),r=[{service:"Google",connected:s.credentials?.some(e=>"google"===e.service)||!1,lastUpdated:s.credentials?.find(e=>"google"===e.service)?.updated_at,scopes:["Calendar","Gmail","Sheets"]},{service:"Slack",connected:s.credentials?.some(e=>"slack"===e.service)||!1,lastUpdated:s.credentials?.find(e=>"slack"===e.service)?.updated_at,scopes:["Channels","Messages","Users"]}];t(r)}catch(e){console.error("Error fetching service status:",e)}finally{x(!1)}},p=async e=>{if(confirm(`Are you sure you want to disconnect ${e}? This will stop all related integrations.`)){x(!0);try{(await fetch(`/api/credentials?service=${e.toLowerCase()}`,{method:"DELETE"})).ok&&(await u(),alert(`${e} disconnected successfully`))}catch(e){console.error("Error disconnecting service:",e)}finally{x(!1)}}},v=()=>{window.location.href="/api/auth/slack"};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center h-16",children:[(0,r.jsx)(h(),{href:"/",className:"mr-4 p-2 text-gray-400 hover:text-gray-600",children:(0,r.jsx)(a.A,{className:"w-5 h-5"})}),(0,r.jsx)(o,{className:"w-8 h-8 text-blue-600 mr-3"}),(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Settings"})]})})}),(0,r.jsxs)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Account Information"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Name"}),(0,r.jsx)("p",{className:"text-gray-900",children:e?.user?.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Email"}),(0,r.jsx)("p",{className:"text-gray-900",children:e?.user?.email})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Connected Services"}),(0,r.jsx)("button",{onClick:u,disabled:l,className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:l?"Refreshing...":"Refresh"})]}),(0,r.jsx)("div",{className:"space-y-4",children:s.map(e=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("div",{className:"flex items-center mr-4",children:[e.connected?(0,r.jsx)(d,{className:"w-5 h-5 text-green-500 mr-2"}):(0,r.jsx)(c,{className:"w-5 h-5 text-red-500 mr-2"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.service})]}),(0,r.jsx)("div",{children:(0,r.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${e.connected?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.connected?"Connected":"Disconnected"})})]}),(0,r.jsx)("div",{className:"flex space-x-2",children:e.connected?(0,r.jsx)("button",{onClick:()=>p(e.service),disabled:l,className:"px-3 py-1 text-sm text-red-600 border border-red-300 rounded hover:bg-red-50 transition-colors",children:"Disconnect"}):"Slack"===e.service&&(0,r.jsx)("button",{onClick:v,className:"px-3 py-1 text-sm text-blue-600 border border-blue-300 rounded hover:bg-blue-50 transition-colors",children:"Connect"})})]}),e.connected&&(0,r.jsxs)("div",{className:"mt-3 text-sm text-gray-600",children:[(0,r.jsxs)("p",{children:["Last updated: ",e.lastUpdated?new Date(e.lastUpdated).toLocaleString():"Unknown"]}),(0,r.jsxs)("p",{children:["Permissions: ",e.scopes?.join(", ")]})]})]},e.service))})]}),(0,r.jsx)("div",{className:"mt-8 bg-blue-50 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(m.A,{className:"w-6 h-6 text-blue-600 mr-3 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-blue-900 mb-2",children:"Security & Privacy"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("li",{children:"• All credentials are encrypted using AES-256 encryption"}),(0,r.jsx)("li",{children:"• API tokens are stored securely and never exposed in logs"}),(0,r.jsx)("li",{children:"• You can disconnect services at any time"}),(0,r.jsx)("li",{children:"• Data is only accessed when you explicitly trigger an action"}),(0,r.jsx)("li",{children:"• No data is shared with third parties"})]})]})]})})]})]})}},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,metadata:()=>d});var r=t(37413),i=t(2202),n=t.n(i),a=t(64988),l=t.n(a);t(61135);var o=t(29519);let d={title:"API Integration Hub",description:"Securely connect and manage your API integrations"};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${l().variable} antialiased`,children:(0,r.jsx)(o.Providers,{children:e})})})}},99574:(e,s,t)=>{Promise.resolve().then(t.bind(t,74198))},99891:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[243,254,797,644],()=>t(42173));module.exports=r})();