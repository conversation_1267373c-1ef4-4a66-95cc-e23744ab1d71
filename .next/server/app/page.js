(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4913:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),n=r(48088),i=r(88170),o=r.n(i),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21204)),"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Documents/GitHub/aris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5196:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11824:(e,t,r)=>{Promise.resolve().then(r.bind(r,21204))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21171:(e,t,r)=>{Promise.resolve().then(r.bind(r,21741))},21204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/aris/src/app/page.tsx","default")},21552:(e,t,r)=>{Promise.resolve().then(r.bind(r,75694))},21741:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>i});var s=r(60687),n=r(82136);function i({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29519:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/GitHub/aris/src/app/providers.tsx","Providers")},33873:e=>{"use strict";e.exports=require("path")},41644:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68027:(e,t,r)=>{Promise.resolve().then(r.bind(r,29519))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},75694:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(60687),n=r(82136),i=r(99891);function o(){let{data:e,status:t}=(0,n.useSession)();return"loading"===t?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):e?(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(i.A,{className:"w-8 h-8 text-blue-600 mr-3"}),(0,s.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"API Integration Hub"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-700",children:["Welcome, ",e.user?.name]}),(0,s.jsx)("button",{onClick:()=>(0,n.signOut)(),className:"text-sm text-gray-500 hover:text-gray-700",children:"Sign out"})]})]})})}),(0,s.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Welcome to your Integration Hub!"}),(0,s.jsxs)("p",{className:"text-gray-600 mb-8",children:["You are successfully logged in as ",e.user?.email]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Next Steps:"}),(0,s.jsxs)("ul",{className:"text-left space-y-2 text-gray-600",children:[(0,s.jsx)("li",{children:"• Go to Settings to connect your Slack workspace"}),(0,s.jsx)("li",{children:"• Set up your Google integrations"}),(0,s.jsx)("li",{children:"• Explore the integration tools"})]})]})]})})]}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,s.jsx)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(i.A,{className:"w-16 h-16 text-blue-600 mx-auto mb-4"}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"API Integration Hub"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8",children:"Securely connect and manage your Google, Slack, and other API integrations"}),(0,s.jsx)("button",{onClick:()=>(0,n.signIn)("google"),className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium",children:"Sign in with Google"})]})})})}},79551:e=>{"use strict";e.exports=require("url")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var s=r(37413),n=r(2202),i=r.n(n),o=r(64988),a=r.n(o);r(61135);var l=r(29519);let d={title:"API Integration Hub",description:"Securely connect and manage your API integrations"};function c({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${i().variable} ${a().variable} antialiased`,children:(0,s.jsx)(l.Providers,{children:e})})})}},99891:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,254,797],()=>r(4913));module.exports=s})();