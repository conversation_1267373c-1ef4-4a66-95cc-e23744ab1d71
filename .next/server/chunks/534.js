"use strict";exports.id=534,exports.ids=[534],exports.modules={56534:(r,e,t)=>{t.d(e,{KG:()=>s,encryptCredentials:()=>i});var o=t(40383),n=t.n(o);let c=process.env.ENCRYPTION_KEY||"default-key-change-in-production",i=r=>{try{let e=JSON.stringify(r);return n().AES.encrypt(e,c).toString()}catch(r){throw console.error("Encryption error:",r),Error("Failed to encrypt credentials")}},s=r=>{try{let e=n().AES.decrypt(r,c).toString(n().enc.Utf8);return JSON.parse(e)}catch(r){throw console.error("Decryption error:",r),Error("Failed to decrypt credentials")}}}};