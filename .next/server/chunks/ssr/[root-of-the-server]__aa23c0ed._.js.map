{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/aris/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession, signIn, signOut } from 'next-auth/react'\nimport { useEffect, useState } from 'react'\nimport { Calendar, Mail, MessageSquare, Users, Settings, LogOut, Shield } from 'lucide-react'\nimport Link from 'next/link'\n\ninterface Tool {\n  id: string\n  name: string\n  description: string\n  icon: React.ReactNode\n  path: string\n  isActive: boolean\n}\n\nexport default function Home() {\n  const { data: session, status } = useSession()\n  const [tools, setTools] = useState<Tool[]>([\n    {\n      id: 'calendar-to-sheets',\n      name: 'Calendar to Sheets',\n      description: 'Create and update spreadsheets from calendar events',\n      icon: <Calendar className=\"w-6 h-6\" />,\n      path: '/tools/calendar-to-sheets',\n      isActive: false\n    },\n    {\n      id: 'gmail-to-sheets',\n      name: 'Gmail to Sheets',\n      description: 'Download and update email data to spreadsheets',\n      icon: <Mail className=\"w-6 h-6\" />,\n      path: '/tools/gmail-to-sheets',\n      isActive: false\n    },\n    {\n      id: 'slack-to-sheets',\n      name: 'Slack to Sheets',\n      description: 'Connect Slack channels to spreadsheets',\n      icon: <MessageSquare className=\"w-6 h-6\" />,\n      path: '/tools/slack-to-sheets',\n      isActive: false\n    },\n    {\n      id: 'customer-manager',\n      name: 'Customer Manager',\n      description: 'Manage and edit customer information',\n      icon: <Users className=\"w-6 h-6\" />,\n      path: '/tools/customer-manager',\n      isActive: false\n    }\n  ])\n\n  if (status === 'loading') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  if (!session) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-8\">\n          <div className=\"text-center\">\n            <Shield className=\"w-16 h-16 text-blue-600 mx-auto mb-4\" />\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">API Integration Hub</h1>\n            <p className=\"text-gray-600 mb-8\">\n              Securely connect and manage your Google, Slack, and other API integrations\n            </p>\n            <button\n              onClick={() => signIn('google')}\n              className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium\"\n            >\n              Sign in with Google\n            </button>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Shield className=\"w-8 h-8 text-blue-600 mr-3\" />\n              <h1 className=\"text-xl font-semibold text-gray-900\">API Integration Hub</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-700\">Welcome, {session.user?.name}</span>\n              <Link href=\"/settings\" className=\"p-2 text-gray-400 hover:text-gray-600\">\n                <Settings className=\"w-5 h-5\" />\n              </Link>\n              <button\n                onClick={() => signOut()}\n                className=\"p-2 text-gray-400 hover:text-gray-600\"\n              >\n                <LogOut className=\"w-5 h-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Integration Tools</h2>\n          <p className=\"text-gray-600\">\n            Manage your API integrations and automate data workflows\n          </p>\n        </div>\n\n        {/* Tools Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {tools.map((tool) => (\n            <Link\n              key={tool.id}\n              href={tool.path}\n              className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\"\n            >\n              <div className=\"flex items-center mb-4\">\n                <div className=\"p-2 bg-blue-100 rounded-lg mr-3\">\n                  {tool.icon}\n                </div>\n                <div className=\"flex-1\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">{tool.name}</h3>\n                  <div className=\"flex items-center mt-1\">\n                    <div className={`w-2 h-2 rounded-full mr-2 ${tool.isActive ? 'bg-green-400' : 'bg-gray-300'}`} />\n                    <span className={`text-xs ${tool.isActive ? 'text-green-600' : 'text-gray-500'}`}>\n                      {tool.isActive ? 'Active' : 'Inactive'}\n                    </span>\n                  </div>\n                </div>\n              </div>\n              <p className=\"text-sm text-gray-600\">{tool.description}</p>\n            </Link>\n          ))}\n        </div>\n\n        {/* Quick Stats */}\n        <div className=\"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Connected Services</h3>\n            <div className=\"text-3xl font-bold text-blue-600\">2</div>\n            <p className=\"text-sm text-gray-600\">Google, Slack</p>\n          </div>\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Active Integrations</h3>\n            <div className=\"text-3xl font-bold text-green-600\">0</div>\n            <p className=\"text-sm text-gray-600\">Running workflows</p>\n          </div>\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Last Activity</h3>\n            <div className=\"text-lg font-medium text-gray-900\">Just now</div>\n            <p className=\"text-sm text-gray-600\">User login</p>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAgBe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;QACzC;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YAC<PERSON>,MAAM;YACN,aAAa;YACb,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,MAAM;YACN,UAAU;QACZ;KACD;IAED,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE;4BACtB,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAwB;4CAAU,QAAQ,IAAI,EAAE;;;;;;;kDAChE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAC/B,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;wCACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;wCACrB,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5B,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAM/B,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAqC,KAAK,IAAI;;;;;;kEAC5D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,CAAC,0BAA0B,EAAE,KAAK,QAAQ,GAAG,iBAAiB,eAAe;;;;;;0EAC7F,8OAAC;gEAAK,WAAW,CAAC,QAAQ,EAAE,KAAK,QAAQ,GAAG,mBAAmB,iBAAiB;0EAC7E,KAAK,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;;;;;;;kDAKpC,8OAAC;wCAAE,WAAU;kDAAyB,KAAK,WAAW;;;;;;;+BAlBjD,KAAK,EAAE;;;;;;;;;;kCAwBlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAmC;;;;;;kDAClD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}]}