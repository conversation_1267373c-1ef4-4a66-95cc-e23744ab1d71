{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/aris/src/app/api/health/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport async function GET() {\n  const health = {\n    status: 'healthy',\n    timestamp: new Date().toISOString(),\n    environment: process.env.NODE_ENV,\n    checks: {\n      nextauth_secret: !!process.env.NEXTAUTH_SECRET,\n      supabase_url: !!process.env.NEXT_PUBLIC_SUPABASE_URL,\n      supabase_anon_key: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n      supabase_service_key: !!process.env.SUPABASE_SERVICE_ROLE_KEY,\n      google_client_id: !!process.env.GOOGLE_CLIENT_ID,\n      google_client_secret: !!process.env.GOOGLE_CLIENT_SECRET,\n      encryption_key: !!process.env.ENCRYPTION_KEY,\n    }\n  }\n\n  const allChecksPass = Object.values(health.checks).every(check => check === true)\n  \n  return NextResponse.json(health, { \n    status: allChecksPass ? 200 : 500 \n  })\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe;IACpB,MAAM,SAAS;QACb,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW;QACX,QAAQ;YACN,iBAAiB,CAAC,CAAC,QAAQ,GAAG,CAAC,eAAe;YAC9C,cAAc,CAAC;YACf,mBAAmB,CAAC;YACpB,sBAAsB,CAAC,CAAC,QAAQ,GAAG,CAAC,yBAAyB;YAC7D,kBAAkB,CAAC,CAAC,QAAQ,GAAG,CAAC,gBAAgB;YAChD,sBAAsB,CAAC,CAAC,QAAQ,GAAG,CAAC,oBAAoB;YACxD,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,cAAc;QAC9C;IACF;IAEA,MAAM,gBAAgB,OAAO,MAAM,CAAC,OAAO,MAAM,EAAE,KAAK,CAAC,CAAA,QAAS,UAAU;IAE5E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;QAC/B,QAAQ,gBAAgB,MAAM;IAChC;AACF", "debugId": null}}]}