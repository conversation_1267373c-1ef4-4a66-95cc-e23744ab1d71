{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/aris/src/lib/database.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseServiceKey)\n\n// Database schema types\nexport interface UserCredentials {\n  id: string\n  user_id: string\n  service: 'google' | 'slack'\n  encrypted_credentials: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface AppConfiguration {\n  id: string\n  user_id: string\n  tool_name: string\n  configuration: any\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface ActivityLog {\n  id: string\n  user_id: string\n  tool_name: string\n  action: string\n  status: 'success' | 'error'\n  details: any\n  created_at: string\n}\n\nexport interface Customer {\n  id: string\n  user_id: string\n  name: string\n  email: string\n  company: string\n  phone?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\n// Database initialization SQL\nexport const initializeDatabase = async () => {\n  const { error } = await supabase.rpc('create_tables', {\n    sql: `\n      -- User credentials table\n      CREATE TABLE IF NOT EXISTS user_credentials (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        service TEXT NOT NULL CHECK (service IN ('google', 'slack')),\n        encrypted_credentials TEXT NOT NULL,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        UNIQUE(user_id, service)\n      );\n\n      -- App configurations table\n      CREATE TABLE IF NOT EXISTS app_configurations (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        tool_name TEXT NOT NULL,\n        configuration JSONB NOT NULL DEFAULT '{}',\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Activity logs table\n      CREATE TABLE IF NOT EXISTS activity_logs (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        tool_name TEXT NOT NULL,\n        action TEXT NOT NULL,\n        status TEXT NOT NULL CHECK (status IN ('success', 'error')),\n        details JSONB DEFAULT '{}',\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Customers table\n      CREATE TABLE IF NOT EXISTS customers (\n        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n        user_id TEXT NOT NULL,\n        name TEXT NOT NULL,\n        email TEXT,\n        company TEXT,\n        phone TEXT,\n        notes TEXT,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      );\n\n      -- Indexes\n      CREATE INDEX IF NOT EXISTS idx_user_credentials_user_id ON user_credentials(user_id);\n      CREATE INDEX IF NOT EXISTS idx_app_configurations_user_id ON app_configurations(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_customers_user_id ON customers(user_id);\n    `\n  })\n\n  if (error) {\n    console.error('Database initialization error:', error)\n    throw error\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAEzD,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AA6C3C,MAAM,qBAAqB;IAChC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,iBAAiB;QACpD,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoDN,CAAC;IACH;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/aris/src/lib/encryption.ts"], "sourcesContent": ["import CryptoJS from 'crypto-js'\n\nconst ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-key-change-in-production'\n\nexport interface EncryptedCredentials {\n  google?: {\n    access_token: string\n    refresh_token: string\n    scope: string\n    token_type: string\n    expiry_date: number\n  }\n  slack?: {\n    access_token: string\n    team_id: string\n    team_name: string\n    user_id: string\n    scope: string\n  }\n}\n\nexport const encryptCredentials = (credentials: any): string => {\n  try {\n    const jsonString = JSON.stringify(credentials)\n    const encrypted = CryptoJS.AES.encrypt(jsonString, ENCRYPTION_KEY).toString()\n    return encrypted\n  } catch (error) {\n    console.error('Encryption error:', error)\n    throw new Error('Failed to encrypt credentials')\n  }\n}\n\nexport const decryptCredentials = (encryptedData: string): any => {\n  try {\n    const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY)\n    const decryptedString = bytes.toString(CryptoJS.enc.Utf8)\n    return JSON.parse(decryptedString)\n  } catch (error) {\n    console.error('Decryption error:', error)\n    throw new Error('Failed to decrypt credentials')\n  }\n}\n\nexport const hashPassword = (password: string): string => {\n  return CryptoJS.SHA256(password).toString()\n}\n\nexport const validateCredentials = (credentials: any, service: 'google' | 'slack'): boolean => {\n  if (service === 'google') {\n    return !!(\n      credentials.access_token &&\n      credentials.refresh_token &&\n      credentials.scope &&\n      credentials.token_type\n    )\n  }\n  \n  if (service === 'slack') {\n    return !!(\n      credentials.access_token &&\n      credentials.team_id &&\n      credentials.user_id &&\n      credentials.scope\n    )\n  }\n  \n  return false\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AAmB9C,MAAM,qBAAqB,CAAC;IACjC,IAAI;QACF,MAAM,aAAa,KAAK,SAAS,CAAC;QAClC,MAAM,YAAY,uIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,gBAAgB,QAAQ;QAC3E,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,IAAI;QACF,MAAM,QAAQ,uIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,eAAe;QAClD,MAAM,kBAAkB,MAAM,QAAQ,CAAC,uIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,IAAI;QACxD,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,uIAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,UAAU,QAAQ;AAC3C;AAEO,MAAM,sBAAsB,CAAC,aAAkB;IACpD,IAAI,YAAY,UAAU;QACxB,OAAO,CAAC,CAAC,CACP,YAAY,YAAY,IACxB,YAAY,aAAa,IACzB,YAAY,KAAK,IACjB,YAAY,UAAU,AACxB;IACF;IAEA,IAAI,YAAY,SAAS;QACvB,OAAO,CAAC,CAAC,CACP,YAAY,YAAY,IACxB,YAAY,OAAO,IACnB,YAAY,OAAO,IACnB,YAAY,KAAK,AACnB;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/aris/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth'\nimport GoogleProvider from 'next-auth/providers/google'\nimport { supabase } from '@/lib/database'\nimport { encryptCredentials } from '@/lib/encryption'\n\nconst handler = NextAuth({\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n      authorization: {\n        params: {\n          scope: 'openid email profile https://www.googleapis.com/auth/calendar https://www.googleapis.com/auth/gmail.readonly https://www.googleapis.com/auth/spreadsheets',\n          access_type: 'offline',\n          prompt: 'consent',\n        },\n      },\n    }),\n  ],\n  callbacks: {\n    async signIn({ user, account, profile }) {\n      if (account?.provider === 'google' && account.access_token) {\n        try {\n          // Store encrypted Google credentials\n          const credentials = {\n            access_token: account.access_token,\n            refresh_token: account.refresh_token,\n            scope: account.scope,\n            token_type: account.token_type,\n            expiry_date: account.expires_at,\n          }\n\n          const encryptedCredentials = encryptCredentials(credentials)\n\n          await supabase\n            .from('user_credentials')\n            .upsert({\n              user_id: user.id!,\n              service: 'google',\n              encrypted_credentials: encryptedCredentials,\n              updated_at: new Date().toISOString(),\n            })\n\n          return true\n        } catch (error) {\n          console.error('Error storing credentials:', error)\n          return false\n        }\n      }\n      return true\n    },\n    async jwt({ token, account, user }) {\n      if (account) {\n        token.accessToken = account.access_token\n        token.refreshToken = account.refresh_token\n      }\n      if (user) {\n        token.userId = user.id\n      }\n      return token\n    },\n    async session({ session, token }) {\n      session.accessToken = token.accessToken as string\n      session.userId = token.userId as string\n      return session\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n})\n\nexport { handler as GET, handler as POST }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE;IACvB,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;YAC9C,eAAe;gBACb,QAAQ;oBACN,OAAO;oBACP,aAAa;oBACb,QAAQ;gBACV;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,IAAI,SAAS,aAAa,YAAY,QAAQ,YAAY,EAAE;gBAC1D,IAAI;oBACF,qCAAqC;oBACrC,MAAM,cAAc;wBAClB,cAAc,QAAQ,YAAY;wBAClC,eAAe,QAAQ,aAAa;wBACpC,OAAO,QAAQ,KAAK;wBACpB,YAAY,QAAQ,UAAU;wBAC9B,aAAa,QAAQ,UAAU;oBACjC;oBAEA,MAAM,uBAAuB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;oBAEhD,MAAM,wHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,oBACL,MAAM,CAAC;wBACN,SAAS,KAAK,EAAE;wBAChB,SAAS;wBACT,uBAAuB;wBACvB,YAAY,IAAI,OAAO,WAAW;oBACpC;oBAEF,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE;YAChC,IAAI,SAAS;gBACX,MAAM,WAAW,GAAG,QAAQ,YAAY;gBACxC,MAAM,YAAY,GAAG,QAAQ,aAAa;YAC5C;YACA,IAAI,MAAM;gBACR,MAAM,MAAM,GAAG,KAAK,EAAE;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,QAAQ,WAAW,GAAG,MAAM,WAAW;YACvC,QAAQ,MAAM,GAAG,MAAM,MAAM;YAC7B,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}]}