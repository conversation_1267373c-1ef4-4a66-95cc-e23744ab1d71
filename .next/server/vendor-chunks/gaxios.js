"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gaxios";
exports.ids = ["vendor-chunks/gaxios"];
exports.modules = {

/***/ "(rsc)/./node_modules/gaxios/build/src/common.js":
/*!*************************************************!*\
  !*** ./node_modules/gaxios/build/src/common.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GaxiosError = exports.GAXIOS_ERROR_SYMBOL = void 0;\nexports.defaultErrorRedactor = defaultErrorRedactor;\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst util_1 = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/gaxios/build/src/util.js\");\nconst extend_1 = __importDefault(__webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\"));\n/**\n * Support `instanceof` operator for `GaxiosError`s in different versions of this library.\n *\n * @see {@link GaxiosError[Symbol.hasInstance]}\n */\nexports.GAXIOS_ERROR_SYMBOL = Symbol.for(`${util_1.pkg.name}-gaxios-error`);\n/* eslint-disable-next-line @typescript-eslint/no-explicit-any */\nclass GaxiosError extends Error {\n    /**\n     * Support `instanceof` operator for `GaxiosError` across builds/duplicated files.\n     *\n     * @see {@link GAXIOS_ERROR_SYMBOL}\n     * @see {@link GaxiosError[GAXIOS_ERROR_SYMBOL]}\n     */\n    static [(_a = exports.GAXIOS_ERROR_SYMBOL, Symbol.hasInstance)](instance) {\n        if (instance &&\n            typeof instance === 'object' &&\n            exports.GAXIOS_ERROR_SYMBOL in instance &&\n            instance[exports.GAXIOS_ERROR_SYMBOL] === util_1.pkg.version) {\n            return true;\n        }\n        // fallback to native\n        return Function.prototype[Symbol.hasInstance].call(GaxiosError, instance);\n    }\n    constructor(message, config, response, error) {\n        var _b;\n        super(message);\n        this.config = config;\n        this.response = response;\n        this.error = error;\n        /**\n         * Support `instanceof` operator for `GaxiosError` across builds/duplicated files.\n         *\n         * @see {@link GAXIOS_ERROR_SYMBOL}\n         * @see {@link GaxiosError[Symbol.hasInstance]}\n         * @see {@link https://github.com/microsoft/TypeScript/issues/13965#issuecomment-278570200}\n         * @see {@link https://stackoverflow.com/questions/46618852/require-and-instanceof}\n         * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/@@hasInstance#reverting_to_default_instanceof_behavior}\n         */\n        this[_a] = util_1.pkg.version;\n        // deep-copy config as we do not want to mutate\n        // the existing config for future retries/use\n        this.config = (0, extend_1.default)(true, {}, config);\n        if (this.response) {\n            this.response.config = (0, extend_1.default)(true, {}, this.response.config);\n        }\n        if (this.response) {\n            try {\n                this.response.data = translateData(this.config.responseType, (_b = this.response) === null || _b === void 0 ? void 0 : _b.data);\n            }\n            catch (_c) {\n                // best effort - don't throw an error within an error\n                // we could set `this.response.config.responseType = 'unknown'`, but\n                // that would mutate future calls with this config object.\n            }\n            this.status = this.response.status;\n        }\n        if (error && 'code' in error && error.code) {\n            this.code = error.code;\n        }\n        if (config.errorRedactor) {\n            config.errorRedactor({\n                config: this.config,\n                response: this.response,\n            });\n        }\n    }\n}\nexports.GaxiosError = GaxiosError;\nfunction translateData(responseType, data) {\n    switch (responseType) {\n        case 'stream':\n            return data;\n        case 'json':\n            return JSON.parse(JSON.stringify(data));\n        case 'arraybuffer':\n            return JSON.parse(Buffer.from(data).toString('utf8'));\n        case 'blob':\n            return JSON.parse(data.text());\n        default:\n            return data;\n    }\n}\n/**\n * An experimental error redactor.\n *\n * @param config Config to potentially redact properties of\n * @param response Config to potentially redact properties of\n *\n * @experimental\n */\nfunction defaultErrorRedactor(data) {\n    const REDACT = '<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.';\n    function redactHeaders(headers) {\n        if (!headers)\n            return;\n        for (const key of Object.keys(headers)) {\n            // any casing of `Authentication`\n            if (/^authentication$/i.test(key)) {\n                headers[key] = REDACT;\n            }\n            // any casing of `Authorization`\n            if (/^authorization$/i.test(key)) {\n                headers[key] = REDACT;\n            }\n            // anything containing secret, such as 'client secret'\n            if (/secret/i.test(key)) {\n                headers[key] = REDACT;\n            }\n        }\n    }\n    function redactString(obj, key) {\n        if (typeof obj === 'object' &&\n            obj !== null &&\n            typeof obj[key] === 'string') {\n            const text = obj[key];\n            if (/grant_type=/i.test(text) ||\n                /assertion=/i.test(text) ||\n                /secret/i.test(text)) {\n                obj[key] = REDACT;\n            }\n        }\n    }\n    function redactObject(obj) {\n        if (typeof obj === 'object' && obj !== null) {\n            if ('grant_type' in obj) {\n                obj['grant_type'] = REDACT;\n            }\n            if ('assertion' in obj) {\n                obj['assertion'] = REDACT;\n            }\n            if ('client_secret' in obj) {\n                obj['client_secret'] = REDACT;\n            }\n        }\n    }\n    if (data.config) {\n        redactHeaders(data.config.headers);\n        redactString(data.config, 'data');\n        redactObject(data.config.data);\n        redactString(data.config, 'body');\n        redactObject(data.config.body);\n        try {\n            const url = new url_1.URL('', data.config.url);\n            if (url.searchParams.has('token')) {\n                url.searchParams.set('token', REDACT);\n            }\n            if (url.searchParams.has('client_secret')) {\n                url.searchParams.set('client_secret', REDACT);\n            }\n            data.config.url = url.toString();\n        }\n        catch (_b) {\n            // ignore error - no need to parse an invalid URL\n        }\n    }\n    if (data.response) {\n        defaultErrorRedactor({ config: data.response.config });\n        redactHeaders(data.response.headers);\n        redactString(data.response, 'data');\n        redactObject(data.response.data);\n    }\n    return data;\n}\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/common.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/gaxios.js":
/*!*************************************************!*\
  !*** ./node_modules/gaxios/build/src/gaxios.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _Gaxios_instances, _a, _Gaxios_urlMayUseProxy, _Gaxios_applyRequestInterceptors, _Gaxios_applyResponseInterceptors, _Gaxios_prepareRequest, _Gaxios_proxyAgent, _Gaxios_getProxyAgent;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Gaxios = void 0;\nconst extend_1 = __importDefault(__webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\"));\nconst https_1 = __webpack_require__(/*! https */ \"https\");\nconst node_fetch_1 = __importDefault(__webpack_require__(/*! node-fetch */ \"(rsc)/./node_modules/node-fetch/lib/index.mjs\"));\nconst querystring_1 = __importDefault(__webpack_require__(/*! querystring */ \"querystring\"));\nconst is_stream_1 = __importDefault(__webpack_require__(/*! is-stream */ \"(rsc)/./node_modules/is-stream/index.js\"));\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst common_1 = __webpack_require__(/*! ./common */ \"(rsc)/./node_modules/gaxios/build/src/common.js\");\nconst retry_1 = __webpack_require__(/*! ./retry */ \"(rsc)/./node_modules/gaxios/build/src/retry.js\");\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst uuid_1 = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm-node/index.js\");\nconst interceptor_1 = __webpack_require__(/*! ./interceptor */ \"(rsc)/./node_modules/gaxios/build/src/interceptor.js\");\n/* eslint-disable @typescript-eslint/no-explicit-any */\nconst fetch = hasFetch() ? window.fetch : node_fetch_1.default;\nfunction hasWindow() {\n    return typeof window !== 'undefined' && !!window;\n}\nfunction hasFetch() {\n    return hasWindow() && !!window.fetch;\n}\nfunction hasBuffer() {\n    return typeof Buffer !== 'undefined';\n}\nfunction hasHeader(options, header) {\n    return !!getHeader(options, header);\n}\nfunction getHeader(options, header) {\n    header = header.toLowerCase();\n    for (const key of Object.keys((options === null || options === void 0 ? void 0 : options.headers) || {})) {\n        if (header === key.toLowerCase()) {\n            return options.headers[key];\n        }\n    }\n    return undefined;\n}\nclass Gaxios {\n    /**\n     * The Gaxios class is responsible for making HTTP requests.\n     * @param defaults The default set of options to be used for this instance.\n     */\n    constructor(defaults) {\n        _Gaxios_instances.add(this);\n        this.agentCache = new Map();\n        this.defaults = defaults || {};\n        this.interceptors = {\n            request: new interceptor_1.GaxiosInterceptorManager(),\n            response: new interceptor_1.GaxiosInterceptorManager(),\n        };\n    }\n    /**\n     * Perform an HTTP request with the given options.\n     * @param opts Set of HTTP options that will be used for this HTTP request.\n     */\n    async request(opts = {}) {\n        opts = await __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_prepareRequest).call(this, opts);\n        opts = await __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_applyRequestInterceptors).call(this, opts);\n        return __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_applyResponseInterceptors).call(this, this._request(opts));\n    }\n    async _defaultAdapter(opts) {\n        const fetchImpl = opts.fetchImplementation || fetch;\n        const res = (await fetchImpl(opts.url, opts));\n        const data = await this.getResponseData(opts, res);\n        return this.translateResponse(opts, res, data);\n    }\n    /**\n     * Internal, retryable version of the `request` method.\n     * @param opts Set of HTTP options that will be used for this HTTP request.\n     */\n    async _request(opts = {}) {\n        var _b;\n        try {\n            let translatedResponse;\n            if (opts.adapter) {\n                translatedResponse = await opts.adapter(opts, this._defaultAdapter.bind(this));\n            }\n            else {\n                translatedResponse = await this._defaultAdapter(opts);\n            }\n            if (!opts.validateStatus(translatedResponse.status)) {\n                if (opts.responseType === 'stream') {\n                    let response = '';\n                    await new Promise(resolve => {\n                        (translatedResponse === null || translatedResponse === void 0 ? void 0 : translatedResponse.data).on('data', chunk => {\n                            response += chunk;\n                        });\n                        (translatedResponse === null || translatedResponse === void 0 ? void 0 : translatedResponse.data).on('end', resolve);\n                    });\n                    translatedResponse.data = response;\n                }\n                throw new common_1.GaxiosError(`Request failed with status code ${translatedResponse.status}`, opts, translatedResponse);\n            }\n            return translatedResponse;\n        }\n        catch (e) {\n            const err = e instanceof common_1.GaxiosError\n                ? e\n                : new common_1.GaxiosError(e.message, opts, undefined, e);\n            const { shouldRetry, config } = await (0, retry_1.getRetryConfig)(err);\n            if (shouldRetry && config) {\n                err.config.retryConfig.currentRetryAttempt =\n                    config.retryConfig.currentRetryAttempt;\n                // The error's config could be redacted - therefore we only want to\n                // copy the retry state over to the existing config\n                opts.retryConfig = (_b = err.config) === null || _b === void 0 ? void 0 : _b.retryConfig;\n                return this._request(opts);\n            }\n            throw err;\n        }\n    }\n    async getResponseData(opts, res) {\n        switch (opts.responseType) {\n            case 'stream':\n                return res.body;\n            case 'json': {\n                let data = await res.text();\n                try {\n                    data = JSON.parse(data);\n                }\n                catch (_b) {\n                    // continue\n                }\n                return data;\n            }\n            case 'arraybuffer':\n                return res.arrayBuffer();\n            case 'blob':\n                return res.blob();\n            case 'text':\n                return res.text();\n            default:\n                return this.getResponseDataFromContentType(res);\n        }\n    }\n    /**\n     * By default, throw for any non-2xx status code\n     * @param status status code from the HTTP response\n     */\n    validateStatus(status) {\n        return status >= 200 && status < 300;\n    }\n    /**\n     * Encode a set of key/value pars into a querystring format (?foo=bar&baz=boo)\n     * @param params key value pars to encode\n     */\n    paramsSerializer(params) {\n        return querystring_1.default.stringify(params);\n    }\n    translateResponse(opts, res, data) {\n        // headers need to be converted from a map to an obj\n        const headers = {};\n        res.headers.forEach((value, key) => {\n            headers[key] = value;\n        });\n        return {\n            config: opts,\n            data: data,\n            headers,\n            status: res.status,\n            statusText: res.statusText,\n            // XMLHttpRequestLike\n            request: {\n                responseURL: res.url,\n            },\n        };\n    }\n    /**\n     * Attempts to parse a response by looking at the Content-Type header.\n     * @param {FetchResponse} response the HTTP response.\n     * @returns {Promise<any>} a promise that resolves to the response data.\n     */\n    async getResponseDataFromContentType(response) {\n        let contentType = response.headers.get('Content-Type');\n        if (contentType === null) {\n            // Maintain existing functionality by calling text()\n            return response.text();\n        }\n        contentType = contentType.toLowerCase();\n        if (contentType.includes('application/json')) {\n            let data = await response.text();\n            try {\n                data = JSON.parse(data);\n            }\n            catch (_b) {\n                // continue\n            }\n            return data;\n        }\n        else if (contentType.match(/^text\\//)) {\n            return response.text();\n        }\n        else {\n            // If the content type is something not easily handled, just return the raw data (blob)\n            return response.blob();\n        }\n    }\n    /**\n     * Creates an async generator that yields the pieces of a multipart/related request body.\n     * This implementation follows the spec: https://www.ietf.org/rfc/rfc2387.txt. However, recursive\n     * multipart/related requests are not currently supported.\n     *\n     * @param {GaxioMultipartOptions[]} multipartOptions the pieces to turn into a multipart/related body.\n     * @param {string} boundary the boundary string to be placed between each part.\n     */\n    async *getMultipartRequest(multipartOptions, boundary) {\n        const finale = `--${boundary}--`;\n        for (const currentPart of multipartOptions) {\n            const partContentType = currentPart.headers['Content-Type'] || 'application/octet-stream';\n            const preamble = `--${boundary}\\r\\nContent-Type: ${partContentType}\\r\\n\\r\\n`;\n            yield preamble;\n            if (typeof currentPart.content === 'string') {\n                yield currentPart.content;\n            }\n            else {\n                yield* currentPart.content;\n            }\n            yield '\\r\\n';\n        }\n        yield finale;\n    }\n}\nexports.Gaxios = Gaxios;\n_a = Gaxios, _Gaxios_instances = new WeakSet(), _Gaxios_urlMayUseProxy = function _Gaxios_urlMayUseProxy(url, noProxy = []) {\n    var _b, _c;\n    const candidate = new url_1.URL(url);\n    const noProxyList = [...noProxy];\n    const noProxyEnvList = ((_c = ((_b = process.env.NO_PROXY) !== null && _b !== void 0 ? _b : process.env.no_proxy)) === null || _c === void 0 ? void 0 : _c.split(',')) || [];\n    for (const rule of noProxyEnvList) {\n        noProxyList.push(rule.trim());\n    }\n    for (const rule of noProxyList) {\n        // Match regex\n        if (rule instanceof RegExp) {\n            if (rule.test(candidate.toString())) {\n                return false;\n            }\n        }\n        // Match URL\n        else if (rule instanceof url_1.URL) {\n            if (rule.origin === candidate.origin) {\n                return false;\n            }\n        }\n        // Match string regex\n        else if (rule.startsWith('*.') || rule.startsWith('.')) {\n            const cleanedRule = rule.replace(/^\\*\\./, '.');\n            if (candidate.hostname.endsWith(cleanedRule)) {\n                return false;\n            }\n        }\n        // Basic string match\n        else if (rule === candidate.origin ||\n            rule === candidate.hostname ||\n            rule === candidate.href) {\n            return false;\n        }\n    }\n    return true;\n}, _Gaxios_applyRequestInterceptors = \n/**\n * Applies the request interceptors. The request interceptors are applied after the\n * call to prepareRequest is completed.\n *\n * @param {GaxiosOptions} options The current set of options.\n *\n * @returns {Promise<GaxiosOptions>} Promise that resolves to the set of options or response after interceptors are applied.\n */\nasync function _Gaxios_applyRequestInterceptors(options) {\n    let promiseChain = Promise.resolve(options);\n    for (const interceptor of this.interceptors.request.values()) {\n        if (interceptor) {\n            promiseChain = promiseChain.then(interceptor.resolved, interceptor.rejected);\n        }\n    }\n    return promiseChain;\n}, _Gaxios_applyResponseInterceptors = \n/**\n * Applies the response interceptors. The response interceptors are applied after the\n * call to request is made.\n *\n * @param {GaxiosOptions} options The current set of options.\n *\n * @returns {Promise<GaxiosOptions>} Promise that resolves to the set of options or response after interceptors are applied.\n */\nasync function _Gaxios_applyResponseInterceptors(response) {\n    let promiseChain = Promise.resolve(response);\n    for (const interceptor of this.interceptors.response.values()) {\n        if (interceptor) {\n            promiseChain = promiseChain.then(interceptor.resolved, interceptor.rejected);\n        }\n    }\n    return promiseChain;\n}, _Gaxios_prepareRequest = \n/**\n * Validates the options, merges them with defaults, and prepare request.\n *\n * @param options The original options passed from the client.\n * @returns Prepared options, ready to make a request\n */\nasync function _Gaxios_prepareRequest(options) {\n    var _b, _c, _d, _e;\n    const opts = (0, extend_1.default)(true, {}, this.defaults, options);\n    if (!opts.url) {\n        throw new Error('URL is required.');\n    }\n    // baseUrl has been deprecated, remove in 2.0\n    const baseUrl = opts.baseUrl || opts.baseURL;\n    if (baseUrl) {\n        opts.url = baseUrl.toString() + opts.url;\n    }\n    opts.paramsSerializer = opts.paramsSerializer || this.paramsSerializer;\n    if (opts.params && Object.keys(opts.params).length > 0) {\n        let additionalQueryParams = opts.paramsSerializer(opts.params);\n        if (additionalQueryParams.startsWith('?')) {\n            additionalQueryParams = additionalQueryParams.slice(1);\n        }\n        const prefix = opts.url.toString().includes('?') ? '&' : '?';\n        opts.url = opts.url + prefix + additionalQueryParams;\n    }\n    if (typeof options.maxContentLength === 'number') {\n        opts.size = options.maxContentLength;\n    }\n    if (typeof options.maxRedirects === 'number') {\n        opts.follow = options.maxRedirects;\n    }\n    opts.headers = opts.headers || {};\n    if (opts.multipart === undefined && opts.data) {\n        const isFormData = typeof FormData === 'undefined'\n            ? false\n            : (opts === null || opts === void 0 ? void 0 : opts.data) instanceof FormData;\n        if (is_stream_1.default.readable(opts.data)) {\n            opts.body = opts.data;\n        }\n        else if (hasBuffer() && Buffer.isBuffer(opts.data)) {\n            // Do not attempt to JSON.stringify() a Buffer:\n            opts.body = opts.data;\n            if (!hasHeader(opts, 'Content-Type')) {\n                opts.headers['Content-Type'] = 'application/json';\n            }\n        }\n        else if (typeof opts.data === 'object') {\n            // If www-form-urlencoded content type has been set, but data is\n            // provided as an object, serialize the content using querystring:\n            if (!isFormData) {\n                if (getHeader(opts, 'content-type') ===\n                    'application/x-www-form-urlencoded') {\n                    opts.body = opts.paramsSerializer(opts.data);\n                }\n                else {\n                    // } else if (!(opts.data instanceof FormData)) {\n                    if (!hasHeader(opts, 'Content-Type')) {\n                        opts.headers['Content-Type'] = 'application/json';\n                    }\n                    opts.body = JSON.stringify(opts.data);\n                }\n            }\n        }\n        else {\n            opts.body = opts.data;\n        }\n    }\n    else if (opts.multipart && opts.multipart.length > 0) {\n        // note: once the minimum version reaches Node 16,\n        // this can be replaced with randomUUID() function from crypto\n        // and the dependency on UUID removed\n        const boundary = (0, uuid_1.v4)();\n        opts.headers['Content-Type'] = `multipart/related; boundary=${boundary}`;\n        const bodyStream = new stream_1.PassThrough();\n        opts.body = bodyStream;\n        (0, stream_1.pipeline)(this.getMultipartRequest(opts.multipart, boundary), bodyStream, () => { });\n    }\n    opts.validateStatus = opts.validateStatus || this.validateStatus;\n    opts.responseType = opts.responseType || 'unknown';\n    if (!opts.headers['Accept'] && opts.responseType === 'json') {\n        opts.headers['Accept'] = 'application/json';\n    }\n    opts.method = opts.method || 'GET';\n    const proxy = opts.proxy ||\n        ((_b = process === null || process === void 0 ? void 0 : process.env) === null || _b === void 0 ? void 0 : _b.HTTPS_PROXY) ||\n        ((_c = process === null || process === void 0 ? void 0 : process.env) === null || _c === void 0 ? void 0 : _c.https_proxy) ||\n        ((_d = process === null || process === void 0 ? void 0 : process.env) === null || _d === void 0 ? void 0 : _d.HTTP_PROXY) ||\n        ((_e = process === null || process === void 0 ? void 0 : process.env) === null || _e === void 0 ? void 0 : _e.http_proxy);\n    const urlMayUseProxy = __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_urlMayUseProxy).call(this, opts.url, opts.noProxy);\n    if (opts.agent) {\n        // don't do any of the following options - use the user-provided agent.\n    }\n    else if (proxy && urlMayUseProxy) {\n        const HttpsProxyAgent = await __classPrivateFieldGet(_a, _a, \"m\", _Gaxios_getProxyAgent).call(_a);\n        if (this.agentCache.has(proxy)) {\n            opts.agent = this.agentCache.get(proxy);\n        }\n        else {\n            opts.agent = new HttpsProxyAgent(proxy, {\n                cert: opts.cert,\n                key: opts.key,\n            });\n            this.agentCache.set(proxy, opts.agent);\n        }\n    }\n    else if (opts.cert && opts.key) {\n        // Configure client for mTLS\n        if (this.agentCache.has(opts.key)) {\n            opts.agent = this.agentCache.get(opts.key);\n        }\n        else {\n            opts.agent = new https_1.Agent({\n                cert: opts.cert,\n                key: opts.key,\n            });\n            this.agentCache.set(opts.key, opts.agent);\n        }\n    }\n    if (typeof opts.errorRedactor !== 'function' &&\n        opts.errorRedactor !== false) {\n        opts.errorRedactor = common_1.defaultErrorRedactor;\n    }\n    return opts;\n}, _Gaxios_getProxyAgent = async function _Gaxios_getProxyAgent() {\n    __classPrivateFieldSet(this, _a, __classPrivateFieldGet(this, _a, \"f\", _Gaxios_proxyAgent) || (await Promise.resolve().then(() => __importStar(__webpack_require__(/*! https-proxy-agent */ \"(rsc)/./node_modules/https-proxy-agent/dist/index.js\")))).HttpsProxyAgent, \"f\", _Gaxios_proxyAgent);\n    return __classPrivateFieldGet(this, _a, \"f\", _Gaxios_proxyAgent);\n};\n/**\n * A cache for the lazily-loaded proxy agent.\n *\n * Should use {@link Gaxios[#getProxyAgent]} to retrieve.\n */\n// using `import` to dynamically import the types here\n_Gaxios_proxyAgent = { value: void 0 };\n//# sourceMappingURL=gaxios.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/gaxios.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/index.js":
/*!************************************************!*\
  !*** ./node_modules/gaxios/build/src/index.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.instance = exports.Gaxios = exports.GaxiosError = void 0;\nexports.request = request;\nconst gaxios_1 = __webpack_require__(/*! ./gaxios */ \"(rsc)/./node_modules/gaxios/build/src/gaxios.js\");\nObject.defineProperty(exports, \"Gaxios\", ({ enumerable: true, get: function () { return gaxios_1.Gaxios; } }));\nvar common_1 = __webpack_require__(/*! ./common */ \"(rsc)/./node_modules/gaxios/build/src/common.js\");\nObject.defineProperty(exports, \"GaxiosError\", ({ enumerable: true, get: function () { return common_1.GaxiosError; } }));\n__exportStar(__webpack_require__(/*! ./interceptor */ \"(rsc)/./node_modules/gaxios/build/src/interceptor.js\"), exports);\n/**\n * The default instance used when the `request` method is directly\n * invoked.\n */\nexports.instance = new gaxios_1.Gaxios();\n/**\n * Make an HTTP request using the given options.\n * @param opts Options for the request\n */\nasync function request(opts) {\n    return exports.instance.request(opts);\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/interceptor.js":
/*!******************************************************!*\
  !*** ./node_modules/gaxios/build/src/interceptor.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2024 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GaxiosInterceptorManager = void 0;\n/**\n * Class to manage collections of GaxiosInterceptors for both requests and responses.\n */\nclass GaxiosInterceptorManager extends Set {\n}\nexports.GaxiosInterceptorManager = GaxiosInterceptorManager;\n//# sourceMappingURL=interceptor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2F4aW9zL2J1aWxkL3NyYy9pbnRlcmNlcHRvci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxnQ0FBZ0M7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQyIsInNvdXJjZXMiOlsiL1VzZXJzL3phY2svRG9jdW1lbnRzL0dpdEh1Yi9hcmlzL25vZGVfbW9kdWxlcy9nYXhpb3MvYnVpbGQvc3JjL2ludGVyY2VwdG9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLy8gQ29weXJpZ2h0IDIwMjQgR29vZ2xlIExMQ1xuLy8gTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbi8vIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbi8vIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuLy9cbi8vICAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuLy9cbi8vIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbi8vIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbi8vIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuLy8gU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuLy8gbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkdheGlvc0ludGVyY2VwdG9yTWFuYWdlciA9IHZvaWQgMDtcbi8qKlxuICogQ2xhc3MgdG8gbWFuYWdlIGNvbGxlY3Rpb25zIG9mIEdheGlvc0ludGVyY2VwdG9ycyBmb3IgYm90aCByZXF1ZXN0cyBhbmQgcmVzcG9uc2VzLlxuICovXG5jbGFzcyBHYXhpb3NJbnRlcmNlcHRvck1hbmFnZXIgZXh0ZW5kcyBTZXQge1xufVxuZXhwb3J0cy5HYXhpb3NJbnRlcmNlcHRvck1hbmFnZXIgPSBHYXhpb3NJbnRlcmNlcHRvck1hbmFnZXI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbnRlcmNlcHRvci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/interceptor.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/retry.js":
/*!************************************************!*\
  !*** ./node_modules/gaxios/build/src/retry.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getRetryConfig = getRetryConfig;\nasync function getRetryConfig(err) {\n    let config = getConfig(err);\n    if (!err || !err.config || (!config && !err.config.retry)) {\n        return { shouldRetry: false };\n    }\n    config = config || {};\n    config.currentRetryAttempt = config.currentRetryAttempt || 0;\n    config.retry =\n        config.retry === undefined || config.retry === null ? 3 : config.retry;\n    config.httpMethodsToRetry = config.httpMethodsToRetry || [\n        'GET',\n        'HEAD',\n        'PUT',\n        'OPTIONS',\n        'DELETE',\n    ];\n    config.noResponseRetries =\n        config.noResponseRetries === undefined || config.noResponseRetries === null\n            ? 2\n            : config.noResponseRetries;\n    config.retryDelayMultiplier = config.retryDelayMultiplier\n        ? config.retryDelayMultiplier\n        : 2;\n    config.timeOfFirstRequest = config.timeOfFirstRequest\n        ? config.timeOfFirstRequest\n        : Date.now();\n    config.totalTimeout = config.totalTimeout\n        ? config.totalTimeout\n        : Number.MAX_SAFE_INTEGER;\n    config.maxRetryDelay = config.maxRetryDelay\n        ? config.maxRetryDelay\n        : Number.MAX_SAFE_INTEGER;\n    // If this wasn't in the list of status codes where we want\n    // to automatically retry, return.\n    const retryRanges = [\n        // https://en.wikipedia.org/wiki/List_of_HTTP_status_codes\n        // 1xx - Retry (Informational, request still processing)\n        // 2xx - Do not retry (Success)\n        // 3xx - Do not retry (Redirect)\n        // 4xx - Do not retry (Client errors)\n        // 408 - Retry (\"Request Timeout\")\n        // 429 - Retry (\"Too Many Requests\")\n        // 5xx - Retry (Server errors)\n        [100, 199],\n        [408, 408],\n        [429, 429],\n        [500, 599],\n    ];\n    config.statusCodesToRetry = config.statusCodesToRetry || retryRanges;\n    // Put the config back into the err\n    err.config.retryConfig = config;\n    // Determine if we should retry the request\n    const shouldRetryFn = config.shouldRetry || shouldRetryRequest;\n    if (!(await shouldRetryFn(err))) {\n        return { shouldRetry: false, config: err.config };\n    }\n    const delay = getNextRetryDelay(config);\n    // We're going to retry!  Incremenent the counter.\n    err.config.retryConfig.currentRetryAttempt += 1;\n    // Create a promise that invokes the retry after the backOffDelay\n    const backoff = config.retryBackoff\n        ? config.retryBackoff(err, delay)\n        : new Promise(resolve => {\n            setTimeout(resolve, delay);\n        });\n    // Notify the user if they added an `onRetryAttempt` handler\n    if (config.onRetryAttempt) {\n        config.onRetryAttempt(err);\n    }\n    // Return the promise in which recalls Gaxios to retry the request\n    await backoff;\n    return { shouldRetry: true, config: err.config };\n}\n/**\n * Determine based on config if we should retry the request.\n * @param err The GaxiosError passed to the interceptor.\n */\nfunction shouldRetryRequest(err) {\n    var _a;\n    const config = getConfig(err);\n    // node-fetch raises an AbortError if signaled:\n    // https://github.com/bitinn/node-fetch#request-cancellation-with-abortsignal\n    if (err.name === 'AbortError' || ((_a = err.error) === null || _a === void 0 ? void 0 : _a.name) === 'AbortError') {\n        return false;\n    }\n    // If there's no config, or retries are disabled, return.\n    if (!config || config.retry === 0) {\n        return false;\n    }\n    // Check if this error has no response (ETIMEDOUT, ENOTFOUND, etc)\n    if (!err.response &&\n        (config.currentRetryAttempt || 0) >= config.noResponseRetries) {\n        return false;\n    }\n    // Only retry with configured HttpMethods.\n    if (!err.config.method ||\n        config.httpMethodsToRetry.indexOf(err.config.method.toUpperCase()) < 0) {\n        return false;\n    }\n    // If this wasn't in the list of status codes where we want\n    // to automatically retry, return.\n    if (err.response && err.response.status) {\n        let isInRange = false;\n        for (const [min, max] of config.statusCodesToRetry) {\n            const status = err.response.status;\n            if (status >= min && status <= max) {\n                isInRange = true;\n                break;\n            }\n        }\n        if (!isInRange) {\n            return false;\n        }\n    }\n    // If we are out of retry attempts, return\n    config.currentRetryAttempt = config.currentRetryAttempt || 0;\n    if (config.currentRetryAttempt >= config.retry) {\n        return false;\n    }\n    return true;\n}\n/**\n * Acquire the raxConfig object from an GaxiosError if available.\n * @param err The Gaxios error with a config object.\n */\nfunction getConfig(err) {\n    if (err && err.config && err.config.retryConfig) {\n        return err.config.retryConfig;\n    }\n    return;\n}\n/**\n * Gets the delay to wait before the next retry.\n *\n * @param {RetryConfig} config The current set of retry options\n * @returns {number} the amount of ms to wait before the next retry attempt.\n */\nfunction getNextRetryDelay(config) {\n    var _a;\n    // Calculate time to wait with exponential backoff.\n    // If this is the first retry, look for a configured retryDelay.\n    const retryDelay = config.currentRetryAttempt ? 0 : (_a = config.retryDelay) !== null && _a !== void 0 ? _a : 100;\n    // Formula: retryDelay + ((retryDelayMultiplier^currentRetryAttempt - 1 / 2) * 1000)\n    const calculatedDelay = retryDelay +\n        ((Math.pow(config.retryDelayMultiplier, config.currentRetryAttempt) - 1) /\n            2) *\n            1000;\n    const maxAllowableDelay = config.totalTimeout - (Date.now() - config.timeOfFirstRequest);\n    return Math.min(calculatedDelay, maxAllowableDelay, config.maxRetryDelay);\n}\n//# sourceMappingURL=retry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/retry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/util.js":
/*!***********************************************!*\
  !*** ./node_modules/gaxios/build/src/util.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2023 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.pkg = void 0;\nexports.pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/gaxios/package.json\");\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2F4aW9zL2J1aWxkL3NyYy91dGlsLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELFdBQVc7QUFDWCx1R0FBMkM7QUFDM0MiLCJzb3VyY2VzIjpbIi9Vc2Vycy96YWNrL0RvY3VtZW50cy9HaXRIdWIvYXJpcy9ub2RlX21vZHVsZXMvZ2F4aW9zL2J1aWxkL3NyYy91dGlsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLy8gQ29weXJpZ2h0IDIwMjMgR29vZ2xlIExMQ1xuLy8gTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbi8vIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbi8vIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuLy9cbi8vICAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuLy9cbi8vIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbi8vIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbi8vIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuLy8gU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuLy8gbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnBrZyA9IHZvaWQgMDtcbmV4cG9ydHMucGtnID0gcmVxdWlyZSgnLi4vLi4vcGFja2FnZS5qc29uJyk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlsLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/util.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/package.json":
/*!******************************************!*\
  !*** ./node_modules/gaxios/package.json ***!
  \******************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"gaxios","version":"6.7.1","description":"A simple common HTTP client specifically for Google APIs and services.","main":"build/src/index.js","types":"build/src/index.d.ts","files":["build/src"],"scripts":{"lint":"gts check","test":"c8 mocha build/test","presystem-test":"npm run compile","system-test":"mocha build/system-test --timeout 80000","compile":"tsc -p .","fix":"gts fix","prepare":"npm run compile","pretest":"npm run compile","webpack":"webpack","prebrowser-test":"npm run compile","browser-test":"node build/browser-test/browser-test-runner.js","docs":"compodoc src/","docs-test":"linkinator docs","predocs-test":"npm run docs","samples-test":"cd samples/ && npm link ../ && npm test && cd ../","prelint":"cd samples; npm link ../; npm install","clean":"gts clean","precompile":"gts clean"},"repository":"googleapis/gaxios","keywords":["google"],"engines":{"node":">=14"},"author":"Google, LLC","license":"Apache-2.0","devDependencies":{"@babel/plugin-proposal-private-methods":"^7.18.6","@compodoc/compodoc":"1.1.19","@types/cors":"^2.8.6","@types/express":"^4.16.1","@types/extend":"^3.0.1","@types/mocha":"^9.0.0","@types/multiparty":"0.0.36","@types/mv":"^2.1.0","@types/ncp":"^2.0.1","@types/node":"^20.0.0","@types/node-fetch":"^2.5.7","@types/sinon":"^17.0.0","@types/tmp":"0.2.6","@types/uuid":"^10.0.0","abort-controller":"^3.0.0","assert":"^2.0.0","browserify":"^17.0.0","c8":"^8.0.0","cheerio":"1.0.0-rc.10","cors":"^2.8.5","execa":"^5.0.0","express":"^4.16.4","form-data":"^4.0.0","gts":"^5.0.0","is-docker":"^2.0.0","karma":"^6.0.0","karma-chrome-launcher":"^3.0.0","karma-coverage":"^2.0.0","karma-firefox-launcher":"^2.0.0","karma-mocha":"^2.0.0","karma-remap-coverage":"^0.1.5","karma-sourcemap-loader":"^0.4.0","karma-webpack":"5.0.0","linkinator":"^3.0.0","mocha":"^8.0.0","multiparty":"^4.2.1","mv":"^2.1.1","ncp":"^2.0.0","nock":"^13.0.0","null-loader":"^4.0.0","puppeteer":"^19.0.0","sinon":"^18.0.0","stream-browserify":"^3.0.0","tmp":"0.2.3","ts-loader":"^8.0.0","typescript":"^5.1.6","webpack":"^5.35.0","webpack-cli":"^4.0.0"},"dependencies":{"extend":"^3.0.2","https-proxy-agent":"^7.0.1","is-stream":"^2.0.0","node-fetch":"^2.6.9","uuid":"^9.0.1"}}');

/***/ })

};
;