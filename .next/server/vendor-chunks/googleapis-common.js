"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/googleapis-common";
exports.ids = ["vendor-chunks/googleapis-common"];
exports.modules = {

/***/ "(rsc)/./node_modules/googleapis-common/build/src/apiIndex.js":
/*!**************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/apiIndex.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getAPI = void 0;\nfunction getAPI(api, options, \n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nversions, context) {\n    let version;\n    if (typeof options === 'string') {\n        version = options;\n        options = {};\n    }\n    else if (typeof options === 'object') {\n        version = options.version;\n        delete options.version;\n    }\n    else {\n        throw new Error('Argument error: Accepts only string or object');\n    }\n    try {\n        const ctr = versions[version];\n        const ep = new ctr(options, context);\n        return Object.freeze(ep);\n    }\n    catch (e) {\n        throw new Error(`Unable to load endpoint ${api}(\"${version}\"): ${e.message}`);\n    }\n}\nexports.getAPI = getAPI;\n//# sourceMappingURL=apiIndex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/apiIndex.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/apirequest.js":
/*!****************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/apirequest.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createAPIRequest = void 0;\nconst google_auth_library_1 = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\nconst qs = __webpack_require__(/*! qs */ \"(rsc)/./node_modules/qs/lib/index.js\");\nconst stream = __webpack_require__(/*! stream */ \"stream\");\nconst urlTemplate = __webpack_require__(/*! url-template */ \"(rsc)/./node_modules/url-template/lib/url-template.js\");\nconst uuid = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm-node/index.js\");\nconst extend = __webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\");\nconst isbrowser_1 = __webpack_require__(/*! ./isbrowser */ \"(rsc)/./node_modules/googleapis-common/build/src/isbrowser.js\");\nconst h2 = __webpack_require__(/*! ./http2 */ \"(rsc)/./node_modules/googleapis-common/build/src/http2.js\");\n// eslint-disable-next-line @typescript-eslint/no-var-requires\nconst pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/googleapis-common/package.json\");\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction isReadableStream(obj) {\n    return (obj !== null &&\n        typeof obj === 'object' &&\n        typeof obj.pipe === 'function' &&\n        obj.readable !== false &&\n        typeof obj._read === 'function' &&\n        typeof obj._readableState === 'object');\n}\nfunction getMissingParams(params, required) {\n    const missing = new Array();\n    required.forEach(param => {\n        // Is the required param in the params object?\n        if (params[param] === undefined) {\n            missing.push(param);\n        }\n    });\n    // If there are any required params missing, return their names in array,\n    // otherwise return null\n    return missing.length > 0 ? missing : null;\n}\nfunction createAPIRequest(parameters, callback) {\n    if (callback) {\n        createAPIRequestAsync(parameters).then(r => callback(null, r), callback);\n    }\n    else {\n        return createAPIRequestAsync(parameters);\n    }\n}\nexports.createAPIRequest = createAPIRequest;\nasync function createAPIRequestAsync(parameters) {\n    var _a, _b, _c, _d;\n    // Combine the GaxiosOptions options passed with this specific\n    // API call with the global options configured at the API Context\n    // level, or at the global level.\n    const options = extend(true, {}, // Ensure we don't leak settings upstream\n    ((_a = parameters.context.google) === null || _a === void 0 ? void 0 : _a._options) || {}, // Google level options\n    parameters.context._options || {}, // Per-API options\n    parameters.options // API call params\n    );\n    const params = extend(true, {}, // New base object\n    options.params, // Combined global/per-api params\n    parameters.params // API call params\n    );\n    options.userAgentDirectives = options.userAgentDirectives || [];\n    const media = params.media || {};\n    /**\n     * In a previous version of this API, the request body was stuffed in a field\n     * named `resource`.  This caused lots of problems, because it's not uncommon\n     * to have an actual named parameter required which is also named `resource`.\n     * This meant that users would have to use `resource_` in those cases, which\n     * pretty much nobody figures out on their own. The request body is now\n     * documented as being in the `requestBody` property, but we also need to keep\n     * using `resource` for reasons of back-compat. Cases that need to be covered\n     * here:\n     * - user provides just a `resource` with a request body\n     * - user provides both a `resource` and a `resource_`\n     * - user provides just a `requestBody`\n     * - user provides both a `requestBody` and a `resource`\n     */\n    let resource = params.requestBody;\n    if (!params.requestBody &&\n        params.resource &&\n        (!parameters.requiredParams.includes('resource') ||\n            typeof params.resource !== 'string')) {\n        resource = params.resource;\n        delete params.resource;\n    }\n    delete params.requestBody;\n    let authClient = params.auth || options.auth;\n    const defaultMime = typeof media.body === 'string' ? 'text/plain' : 'application/octet-stream';\n    delete params.media;\n    delete params.auth;\n    // Grab headers from user provided options\n    const headers = params.headers || {};\n    populateAPIHeader(headers, options.apiVersion);\n    delete params.headers;\n    // Un-alias parameters that were modified due to conflicts with reserved names\n    Object.keys(params).forEach(key => {\n        if (key.slice(-1) === '_') {\n            const newKey = key.slice(0, -1);\n            params[newKey] = params[key];\n            delete params[key];\n        }\n    });\n    // Check for missing required parameters in the API request\n    const missingParams = getMissingParams(params, parameters.requiredParams);\n    if (missingParams) {\n        // Some params are missing - stop further operations and inform the\n        // developer which required params are not included in the request\n        throw new Error('Missing required parameters: ' + missingParams.join(', '));\n    }\n    // Parse urls\n    if (options.url) {\n        let url = options.url;\n        if (typeof url === 'object') {\n            url = url.toString();\n        }\n        options.url = urlTemplate.parse(url).expand(params);\n    }\n    if (parameters.mediaUrl) {\n        parameters.mediaUrl = urlTemplate.parse(parameters.mediaUrl).expand(params);\n    }\n    // Rewrite url if rootUrl is globally set\n    if (parameters.context._options.rootUrl !== undefined &&\n        options.url !== undefined) {\n        const originalUrl = new URL(options.url);\n        const path = originalUrl.href.substr(originalUrl.origin.length);\n        options.url = new URL(path, parameters.context._options.rootUrl).href;\n    }\n    // When forming the querystring, override the serializer so that array\n    // values are serialized like this:\n    // myParams: ['one', 'two'] ---> 'myParams=one&myParams=two'\n    // This serializer also encodes spaces in the querystring as `%20`,\n    // whereas the default serializer in gaxios encodes to a `+`.\n    options.paramsSerializer = params => {\n        return qs.stringify(params, { arrayFormat: 'repeat' });\n    };\n    // delete path params from the params object so they do not end up in query\n    parameters.pathParams.forEach(param => delete params[param]);\n    // if authClient is actually a string, use it as an API KEY\n    if (typeof authClient === 'string') {\n        params.key = params.key || authClient;\n        authClient = undefined;\n    }\n    function multipartUpload(multipart) {\n        const boundary = uuid.v4();\n        const finale = `--${boundary}--`;\n        const rStream = new stream.PassThrough({\n            flush(callback) {\n                this.push('\\r\\n');\n                this.push(finale);\n                callback();\n            },\n        });\n        const pStream = new ProgressStream();\n        const isStream = isReadableStream(multipart[1].body);\n        headers['content-type'] = `multipart/related; boundary=${boundary}`;\n        for (const part of multipart) {\n            const preamble = `--${boundary}\\r\\ncontent-type: ${part['content-type']}\\r\\n\\r\\n`;\n            rStream.push(preamble);\n            if (typeof part.body === 'string') {\n                rStream.push(part.body);\n                rStream.push('\\r\\n');\n            }\n            else {\n                // Gaxios does not natively support onUploadProgress in node.js.\n                // Pipe through the pStream first to read the number of bytes read\n                // for the purpose of tracking progress.\n                pStream.on('progress', bytesRead => {\n                    if (options.onUploadProgress) {\n                        options.onUploadProgress({ bytesRead });\n                    }\n                });\n                part.body.pipe(pStream).pipe(rStream);\n            }\n        }\n        if (!isStream) {\n            rStream.push(finale);\n            rStream.push(null);\n        }\n        options.data = rStream;\n    }\n    function browserMultipartUpload(multipart) {\n        const boundary = uuid.v4();\n        const finale = `--${boundary}--`;\n        headers['content-type'] = `multipart/related; boundary=${boundary}`;\n        let content = '';\n        for (const part of multipart) {\n            const preamble = `--${boundary}\\r\\ncontent-type: ${part['content-type']}\\r\\n\\r\\n`;\n            content += preamble;\n            if (typeof part.body === 'string') {\n                content += part.body;\n                content += '\\r\\n';\n            }\n        }\n        content += finale;\n        options.data = content;\n    }\n    if (parameters.mediaUrl && media.body) {\n        options.url = parameters.mediaUrl;\n        if (resource) {\n            params.uploadType = 'multipart';\n            const multipart = [\n                { 'content-type': 'application/json', body: JSON.stringify(resource) },\n                {\n                    'content-type': media.mimeType || (resource && resource.mimeType) || defaultMime,\n                    body: media.body,\n                },\n            ];\n            if (!(0, isbrowser_1.isBrowser)()) {\n                // gaxios doesn't support multipart/related uploads, so it has to\n                // be implemented here.\n                multipartUpload(multipart);\n            }\n            else {\n                browserMultipartUpload(multipart);\n            }\n        }\n        else {\n            params.uploadType = 'media';\n            Object.assign(headers, { 'content-type': media.mimeType || defaultMime });\n            options.data = media.body;\n        }\n    }\n    else {\n        options.data = resource || undefined;\n    }\n    options.headers = extend(true, options.headers || {}, headers);\n    options.params = params;\n    if (!(0, isbrowser_1.isBrowser)()) {\n        options.headers['Accept-Encoding'] = 'gzip';\n        options.userAgentDirectives.push({\n            product: 'google-api-nodejs-client',\n            version: pkg.version,\n            comment: 'gzip',\n        });\n        const userAgent = options.userAgentDirectives\n            .map(d => {\n            let line = `${d.product}/${d.version}`;\n            if (d.comment) {\n                line += ` (${d.comment})`;\n            }\n            return line;\n        })\n            .join(' ');\n        options.headers['User-Agent'] = userAgent;\n    }\n    // By default gaxios treats any 2xx as valid, and all non 2xx status\n    // codes as errors.  This is a problem for HTTP 304s when used along\n    // with an eTag.\n    if (!options.validateStatus) {\n        options.validateStatus = status => {\n            return (status >= 200 && status < 300) || status === 304;\n        };\n    }\n    // Retry by default\n    options.retry = options.retry === undefined ? true : options.retry;\n    delete options.auth; // is overridden by our auth code\n    // Determine TPC universe\n    if (options.universeDomain &&\n        options.universe_domain &&\n        options.universeDomain !== options.universe_domain) {\n        throw new Error('Please set either universe_domain or universeDomain, but not both.');\n    }\n    const universeDomainEnvVar = typeof process === 'object' && typeof process.env === 'object'\n        ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN']\n        : undefined;\n    const universeDomain = (_d = (_c = (_b = options.universeDomain) !== null && _b !== void 0 ? _b : options.universe_domain) !== null && _c !== void 0 ? _c : universeDomainEnvVar) !== null && _d !== void 0 ? _d : 'googleapis.com';\n    // Update URL to point to the given TPC universe\n    if (universeDomain !== 'googleapis.com' && options.url) {\n        const url = new URL(options.url);\n        if (url.hostname.endsWith('.googleapis.com')) {\n            url.hostname = url.hostname.replace(/googleapis\\.com$/, universeDomain);\n            options.url = url.toString();\n        }\n    }\n    // Perform the HTTP request.  NOTE: this function used to return a\n    // mikeal/request object. Since the transition to Axios, the method is\n    // now void.  This may be a source of confusion for users upgrading from\n    // version 24.0 -> 25.0 or up.\n    if (authClient && typeof authClient === 'object') {\n        // Validate TPC universe\n        const universeFromAuth = typeof authClient.getUniverseDomain === 'function'\n            ? await authClient.getUniverseDomain()\n            : undefined;\n        if (universeFromAuth && universeDomain !== universeFromAuth) {\n            throw new Error(`The configured universe domain (${universeDomain}) does not match the universe domain found in the credentials (${universeFromAuth}). ` +\n                \"If you haven't configured the universe domain explicitly, googleapis.com is the default.\");\n        }\n        if (options.http2) {\n            const authHeaders = await authClient.getRequestHeaders(options.url);\n            const mooOpts = Object.assign({}, options);\n            mooOpts.headers = Object.assign(mooOpts.headers, authHeaders);\n            return h2.request(mooOpts);\n        }\n        else {\n            return authClient.request(options);\n        }\n    }\n    else {\n        return new google_auth_library_1.DefaultTransporter().request(options);\n    }\n}\n/**\n * Basic Passthrough Stream that records the number of bytes read\n * every time the cursor is moved.\n */\nclass ProgressStream extends stream.Transform {\n    constructor() {\n        super(...arguments);\n        this.bytesRead = 0;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    _transform(chunk, encoding, callback) {\n        this.bytesRead += chunk.length;\n        this.emit('progress', this.bytesRead);\n        this.push(chunk);\n        callback();\n    }\n}\nfunction populateAPIHeader(headers, apiVersion) {\n    // TODO: we should eventually think about adding browser support for this\n    // populating the gl-web header (web support should also be added to\n    // google-auth-library-nodejs).\n    if (!(0, isbrowser_1.isBrowser)()) {\n        headers['x-goog-api-client'] =\n            `gdcl/${pkg.version} gl-node/${process.versions.node}`;\n    }\n    if (apiVersion) {\n        headers['x-goog-api-version'] = apiVersion;\n    }\n}\n//# sourceMappingURL=apirequest.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/authplus.js":
/*!**************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/authplus.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AuthPlus = void 0;\nconst google_auth_library_1 = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\nclass AuthPlus extends google_auth_library_1.GoogleAuth {\n    constructor() {\n        super(...arguments);\n        this.JWT = google_auth_library_1.JWT;\n        this.Compute = google_auth_library_1.Compute;\n        this.OAuth2 = google_auth_library_1.OAuth2Client;\n        this.GoogleAuth = google_auth_library_1.GoogleAuth;\n        this.AwsClient = google_auth_library_1.AwsClient;\n        this.IdentityPoolClient = google_auth_library_1.IdentityPoolClient;\n        this.ExternalAccountClient = google_auth_library_1.ExternalAccountClient;\n    }\n    /**\n     * Override getClient(), memoizing an instance of auth for\n     * subsequent calls to getProjectId().\n     */\n    async getClient(options) {\n        this._cachedAuth = new google_auth_library_1.GoogleAuth(options);\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return this._cachedAuth.getClient();\n    }\n    getProjectId(callback) {\n        if (callback) {\n            return this._cachedAuth\n                ? this._cachedAuth.getProjectId(callback)\n                : super.getProjectId(callback);\n        }\n        else {\n            return this._cachedAuth\n                ? this._cachedAuth.getProjectId()\n                : super.getProjectId();\n        }\n    }\n}\nexports.AuthPlus = AuthPlus;\n//# sourceMappingURL=authplus.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/authplus.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/discovery.js":
/*!***************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/discovery.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Discovery = void 0;\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst google_auth_library_1 = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\nconst resolve = __webpack_require__(/*! url */ \"url\");\nconst util = __webpack_require__(/*! util */ \"util\");\nconst apirequest_1 = __webpack_require__(/*! ./apirequest */ \"(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\");\nconst endpoint_1 = __webpack_require__(/*! ./endpoint */ \"(rsc)/./node_modules/googleapis-common/build/src/endpoint.js\");\nconst readFile = util.promisify(fs.readFile);\nclass Discovery {\n    /**\n     * Discovery for discovering API endpoints\n     *\n     * @param options Options for discovery\n     */\n    constructor(options) {\n        this.transporter = new google_auth_library_1.DefaultTransporter();\n        this.options = options || {};\n    }\n    /**\n     * Generate and Endpoint from an endpoint schema object.\n     *\n     * @param schema The schema from which to generate the Endpoint.\n     * @return A function that creates an endpoint.\n     */\n    makeEndpoint(schema) {\n        return (options) => {\n            const ep = new endpoint_1.Endpoint(options);\n            ep.applySchema(ep, schema, schema, ep);\n            return ep;\n        };\n    }\n    /**\n     * Log output of generator. Works just like console.log\n     */\n    log(...args) {\n        if (this.options && this.options.debug) {\n            console.log(...args);\n        }\n    }\n    /**\n     * Generate all APIs and return as in-memory object.\n     * @param discoveryUrl\n     */\n    async discoverAllAPIs(discoveryUrl) {\n        const headers = this.options.includePrivate\n            ? {}\n            : { 'X-User-Ip': '0.0.0.0' };\n        const res = await this.transporter.request({\n            url: discoveryUrl,\n            headers,\n        });\n        const items = res.data.items;\n        const apis = await Promise.all(items.map(async (api) => {\n            const endpointCreator = await this.discoverAPI(api.discoveryRestUrl);\n            return { api, endpointCreator };\n        }));\n        const versionIndex = {};\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const apisIndex = {};\n        for (const set of apis) {\n            if (!apisIndex[set.api.name]) {\n                versionIndex[set.api.name] = {};\n                apisIndex[set.api.name] = (options) => {\n                    const type = typeof options;\n                    let version;\n                    if (type === 'string') {\n                        version = options;\n                        options = {};\n                    }\n                    else if (type === 'object') {\n                        version = options.version;\n                        delete options.version;\n                    }\n                    else {\n                        throw new Error('Argument error: Accepts only string or object');\n                    }\n                    try {\n                        const ep = set.endpointCreator(options, this);\n                        return Object.freeze(ep); // create new & freeze\n                    }\n                    catch (e) {\n                        throw new Error(util.format('Unable to load endpoint %s(\"%s\"): %s', set.api.name, version, e.message));\n                    }\n                };\n            }\n            versionIndex[set.api.name][set.api.version] = set.endpointCreator;\n        }\n        return apisIndex;\n    }\n    /**\n     * Generate API file given discovery URL\n     *\n     * @param apiDiscoveryUrl URL or filename of discovery doc for API\n     * @returns A promise that resolves with a function that creates the endpoint\n     */\n    async discoverAPI(apiDiscoveryUrl) {\n        if (typeof apiDiscoveryUrl === 'string') {\n            const parts = resolve.parse(apiDiscoveryUrl);\n            if (apiDiscoveryUrl && !parts.protocol) {\n                this.log('Reading from file ' + apiDiscoveryUrl);\n                const file = await readFile(apiDiscoveryUrl, { encoding: 'utf8' });\n                return this.makeEndpoint(JSON.parse(file));\n            }\n            else {\n                this.log('Requesting ' + apiDiscoveryUrl);\n                const res = await this.transporter.request({\n                    url: apiDiscoveryUrl,\n                });\n                return this.makeEndpoint(res.data);\n            }\n        }\n        else {\n            const options = apiDiscoveryUrl;\n            this.log('Requesting ' + options.url);\n            const url = options.url;\n            delete options.url;\n            const parameters = {\n                options: { url, method: 'GET' },\n                requiredParams: [],\n                pathParams: [],\n                params: options,\n                context: { google: { _options: {} }, _options: {} },\n            };\n            const res = await (0, apirequest_1.createAPIRequest)(parameters);\n            return this.makeEndpoint(res.data);\n        }\n    }\n}\nexports.Discovery = Discovery;\n//# sourceMappingURL=discovery.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/discovery.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/endpoint.js":
/*!**************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/endpoint.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Endpoint = void 0;\nconst apirequest_1 = __webpack_require__(/*! ./apirequest */ \"(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\");\nclass Endpoint {\n    constructor(options) {\n        this._options = options || {};\n    }\n    /**\n     * Given a schema, add methods and resources to a target.\n     *\n     * @param {object} target The target to which to apply the schema.\n     * @param {object} rootSchema The top-level schema, so we don't lose track of it\n     * during recursion.\n     * @param {object} schema The current schema from which to extract methods and\n     * resources.\n     * @param {object} context The context to add to each method.\n     */\n    applySchema(target, rootSchema, schema, context) {\n        this.applyMethodsFromSchema(target, rootSchema, schema, context);\n        if (schema.resources) {\n            for (const resourceName in schema.resources) {\n                if (Object.prototype.hasOwnProperty.call(schema.resources, resourceName)) {\n                    const resource = schema.resources[resourceName];\n                    if (!target[resourceName]) {\n                        target[resourceName] = {};\n                    }\n                    this.applySchema(target[resourceName], rootSchema, resource, context);\n                }\n            }\n        }\n    }\n    /**\n     * Given a schema, add methods to a target.\n     *\n     * @param {object} target The target to which to apply the methods.\n     * @param {object} rootSchema The top-level schema, so we don't lose track of it\n     * during recursion.\n     * @param {object} schema The current schema from which to extract methods.\n     * @param {object} context The context to add to each method.\n     */\n    applyMethodsFromSchema(target, rootSchema, schema, context) {\n        if (schema.methods) {\n            for (const name in schema.methods) {\n                if (Object.prototype.hasOwnProperty.call(schema.methods, name)) {\n                    const method = schema.methods[name];\n                    target[name] = this.makeMethod(rootSchema, method, context);\n                }\n            }\n        }\n    }\n    /**\n     * Given a method schema, add a method to a target.\n     *\n     * @param target The target to which to add the method.\n     * @param schema The top-level schema that contains the rootUrl, etc.\n     * @param method The method schema from which to generate the method.\n     * @param context The context to add to the method.\n     */\n    makeMethod(schema, method, context) {\n        return (paramsOrCallback, callback) => {\n            const params = typeof paramsOrCallback === 'function' ? {} : paramsOrCallback;\n            callback =\n                typeof paramsOrCallback === 'function'\n                    ? paramsOrCallback\n                    : callback;\n            const schemaUrl = buildurl(schema.rootUrl + schema.servicePath + method.path);\n            const parameters = {\n                options: {\n                    url: schemaUrl.substring(1, schemaUrl.length - 1),\n                    method: method.httpMethod,\n                    apiVersion: method.apiVersion,\n                },\n                params,\n                requiredParams: method.parameterOrder || [],\n                pathParams: this.getPathParams(method.parameters),\n                context,\n            };\n            if (method.mediaUpload &&\n                method.mediaUpload.protocols &&\n                method.mediaUpload.protocols.simple &&\n                method.mediaUpload.protocols.simple.path) {\n                const mediaUrl = buildurl(schema.rootUrl + method.mediaUpload.protocols.simple.path);\n                parameters.mediaUrl = mediaUrl.substring(1, mediaUrl.length - 1);\n            }\n            if (!callback) {\n                return (0, apirequest_1.createAPIRequest)(parameters);\n            }\n            (0, apirequest_1.createAPIRequest)(parameters, callback);\n            return;\n        };\n    }\n    getPathParams(params) {\n        const pathParams = new Array();\n        if (typeof params !== 'object') {\n            params = {};\n        }\n        Object.keys(params).forEach(key => {\n            if (params[key].location === 'path') {\n                pathParams.push(key);\n            }\n        });\n        return pathParams;\n    }\n}\nexports.Endpoint = Endpoint;\n/**\n * Build a string used to create a URL from the discovery doc provided URL.\n * replace double slashes with single slash (except in https://)\n * @private\n * @param  input URL to build from\n * @return Resulting built URL\n */\nfunction buildurl(input) {\n    return input ? `'${input}'`.replace(/([^:]\\/)\\/+/g, '$1') : '';\n}\n//# sourceMappingURL=endpoint.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/endpoint.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/http2.js":
/*!***********************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/http2.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.closeSession = exports.request = exports.sessions = void 0;\nconst http2 = __webpack_require__(/*! http2 */ \"http2\");\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst qs = __webpack_require__(/*! qs */ \"(rsc)/./node_modules/qs/lib/index.js\");\nconst extend = __webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\");\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst util = __webpack_require__(/*! util */ \"util\");\nconst process = __webpack_require__(/*! process */ \"process\");\nconst common_1 = __webpack_require__(/*! gaxios/build/src/common */ \"(rsc)/./node_modules/gaxios/build/src/common.js\");\nconst { HTTP2_HEADER_CONTENT_ENCODING, HTTP2_HEADER_CONTENT_TYPE, HTTP2_HEADER_METHOD, HTTP2_HEADER_PATH, HTTP2_HEADER_STATUS, } = http2.constants;\nconst DEBUG = !!process.env.HTTP2_DEBUG;\n/**\n * List of sessions current in use.\n * @private\n */\nexports.sessions = {};\n/**\n * Public method to make an http2 request.\n * @param config - Request options.\n */\nasync function request(config) {\n    const opts = extend(true, {}, config);\n    opts.validateStatus = opts.validateStatus || validateStatus;\n    opts.responseType = opts.responseType || 'json';\n    const url = new url_1.URL(opts.url);\n    // Check for an existing session to this host, or go create a new one.\n    const sessionData = _getClient(url.host);\n    // Since we're using this session, clear the timeout handle to ensure\n    // it stays in memory and connected for a while further.\n    if (sessionData.timeoutHandle !== undefined) {\n        clearTimeout(sessionData.timeoutHandle);\n    }\n    // Assemble the querystring based on config.params.  We're using the\n    // `qs` module to make life a little easier.\n    let pathWithQs = url.pathname;\n    if (config.params && Object.keys(config.params).length > 0) {\n        const serializer = config.paramsSerializer || qs.stringify;\n        const q = serializer(opts.params);\n        pathWithQs += `?${q}`;\n    }\n    // Assemble the headers based on basic HTTP2 primitives (path, method) and\n    // custom headers sent from the consumer.  Note: I am using `Object.assign`\n    // here making the assumption these objects are not deep.  If it turns out\n    // they are, we may need to use the `extend` npm module for deep cloning.\n    const headers = Object.assign({}, opts.headers, {\n        [HTTP2_HEADER_PATH]: pathWithQs,\n        [HTTP2_HEADER_METHOD]: config.method || 'GET',\n    });\n    // NOTE: This is working around an upstream bug in `apirequest.ts`. The\n    // request path assumes that the `content-type` header is going to be set in\n    // the underlying HTTP Client. This hack provides bug for bug compatability\n    // with this bug in gaxios:\n    // https://github.com/googleapis/gaxios/blob/main/src/gaxios.ts#L202\n    if (!headers[HTTP2_HEADER_CONTENT_TYPE]) {\n        if (opts.responseType !== 'text') {\n            headers[HTTP2_HEADER_CONTENT_TYPE] = 'application/json';\n        }\n    }\n    const res = {\n        config,\n        request: {},\n        headers: [],\n        status: 0,\n        data: {},\n        statusText: '',\n    };\n    const chunks = [];\n    const session = sessionData.session;\n    let req;\n    return new Promise((resolve, reject) => {\n        try {\n            req = session\n                .request(headers)\n                .on('response', headers => {\n                res.headers = headers;\n                res.status = Number(headers[HTTP2_HEADER_STATUS]);\n                let stream = req;\n                if (headers[HTTP2_HEADER_CONTENT_ENCODING] === 'gzip') {\n                    stream = req.pipe(zlib.createGunzip());\n                }\n                if (opts.responseType === 'stream') {\n                    res.data = stream;\n                    resolve(res);\n                    return;\n                }\n                stream\n                    .on('data', d => {\n                    chunks.push(d);\n                })\n                    .on('error', err => {\n                    reject(err);\n                    return;\n                })\n                    .on('end', () => {\n                    const buf = Buffer.concat(chunks);\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    let data = buf;\n                    if (buf) {\n                        if (opts.responseType === 'json') {\n                            try {\n                                data = JSON.parse(buf.toString('utf8'));\n                            }\n                            catch (_a) {\n                                data = buf.toString('utf8');\n                            }\n                        }\n                        else if (opts.responseType === 'text') {\n                            data = buf.toString('utf8');\n                        }\n                        else if (opts.responseType === 'arraybuffer') {\n                            data = buf.buffer;\n                        }\n                        res.data = data;\n                    }\n                    if (!opts.validateStatus(res.status)) {\n                        let message = `Request failed with status code ${res.status}. `;\n                        if (res.data && typeof res.data === 'object') {\n                            const body = util.inspect(res.data, { depth: 5 });\n                            message = `${message}\\n'${body}`;\n                        }\n                        reject(new common_1.GaxiosError(message, opts, res));\n                    }\n                    resolve(res);\n                    return;\n                });\n            })\n                .on('error', e => {\n                reject(e);\n                return;\n            });\n        }\n        catch (e) {\n            closeSession(url);\n            reject(e);\n        }\n        res.request = req;\n        // If data was provided, write it to the request in the form of\n        // a stream, string data, or a basic object.\n        if (config.data) {\n            if (config.data instanceof stream_1.Stream) {\n                config.data.pipe(req);\n            }\n            else if (typeof config.data === 'string') {\n                const data = Buffer.from(config.data);\n                req.end(data);\n            }\n            else if (typeof config.data === 'object') {\n                const data = JSON.stringify(config.data);\n                req.end(data);\n            }\n        }\n        // Create a timeout so the Http2Session will be cleaned up after\n        // a period of non-use. 500 milliseconds was chosen because it's\n        // a nice round number, and I don't know what would be a better\n        // choice. Keeping this channel open will hold a file descriptor\n        // which will prevent the process from exiting.\n        sessionData.timeoutHandle = setTimeout(() => {\n            closeSession(url);\n        }, 500);\n    });\n}\nexports.request = request;\n/**\n * By default, throw for any non-2xx status code\n * @param status - status code from the HTTP response\n */\nfunction validateStatus(status) {\n    return status >= 200 && status < 300;\n}\n/**\n * Obtain an existing h2 session or go create a new one.\n * @param host - The hostname to which the session belongs.\n */\nfunction _getClient(host) {\n    if (!exports.sessions[host]) {\n        if (DEBUG) {\n            console.log(`Creating client for ${host}`);\n        }\n        const session = http2.connect(`https://${host}`);\n        session\n            .on('error', e => {\n            console.error(`*ERROR*: ${e}`);\n            delete exports.sessions[host];\n        })\n            .on('goaway', (errorCode, lastStreamId) => {\n            console.error(`*GOAWAY*: ${errorCode} : ${lastStreamId}`);\n            delete exports.sessions[host];\n        });\n        exports.sessions[host] = { session };\n    }\n    else {\n        if (DEBUG) {\n            console.log(`Used cached client for ${host}`);\n        }\n    }\n    return exports.sessions[host];\n}\nasync function closeSession(url) {\n    const sessionData = exports.sessions[url.host];\n    if (!sessionData) {\n        return;\n    }\n    const { session } = sessionData;\n    delete exports.sessions[url.host];\n    if (DEBUG) {\n        console.error(`Closing ${url.host}`);\n    }\n    session.close(() => {\n        if (DEBUG) {\n            console.error(`Closed ${url.host}`);\n        }\n    });\n    setTimeout(() => {\n        if (session && !session.destroyed) {\n            if (DEBUG) {\n                console.log(`Forcing close ${url.host}`);\n            }\n            if (session) {\n                session.destroy();\n            }\n        }\n    }, 1000);\n}\nexports.closeSession = closeSession;\n//# sourceMappingURL=http2.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/http2.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Endpoint = exports.Discovery = exports.AuthPlus = exports.createAPIRequest = exports.getAPI = exports.GaxiosError = exports.Gaxios = exports.AwsClient = exports.IdentityPoolClient = exports.BaseExternalAccountClient = exports.ExternalAccountClient = exports.GoogleAuth = exports.DefaultTransporter = exports.UserRefreshClient = exports.Compute = exports.JWT = exports.OAuth2Client = void 0;\nvar google_auth_library_1 = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\nObject.defineProperty(exports, \"OAuth2Client\", ({ enumerable: true, get: function () { return google_auth_library_1.OAuth2Client; } }));\nObject.defineProperty(exports, \"JWT\", ({ enumerable: true, get: function () { return google_auth_library_1.JWT; } }));\nObject.defineProperty(exports, \"Compute\", ({ enumerable: true, get: function () { return google_auth_library_1.Compute; } }));\nObject.defineProperty(exports, \"UserRefreshClient\", ({ enumerable: true, get: function () { return google_auth_library_1.UserRefreshClient; } }));\nObject.defineProperty(exports, \"DefaultTransporter\", ({ enumerable: true, get: function () { return google_auth_library_1.DefaultTransporter; } }));\nObject.defineProperty(exports, \"GoogleAuth\", ({ enumerable: true, get: function () { return google_auth_library_1.GoogleAuth; } }));\nObject.defineProperty(exports, \"ExternalAccountClient\", ({ enumerable: true, get: function () { return google_auth_library_1.ExternalAccountClient; } }));\nObject.defineProperty(exports, \"BaseExternalAccountClient\", ({ enumerable: true, get: function () { return google_auth_library_1.BaseExternalAccountClient; } }));\nObject.defineProperty(exports, \"IdentityPoolClient\", ({ enumerable: true, get: function () { return google_auth_library_1.IdentityPoolClient; } }));\nObject.defineProperty(exports, \"AwsClient\", ({ enumerable: true, get: function () { return google_auth_library_1.AwsClient; } }));\nvar gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/src/index.js\");\nObject.defineProperty(exports, \"Gaxios\", ({ enumerable: true, get: function () { return gaxios_1.Gaxios; } }));\nObject.defineProperty(exports, \"GaxiosError\", ({ enumerable: true, get: function () { return gaxios_1.GaxiosError; } }));\nvar apiIndex_1 = __webpack_require__(/*! ./apiIndex */ \"(rsc)/./node_modules/googleapis-common/build/src/apiIndex.js\");\nObject.defineProperty(exports, \"getAPI\", ({ enumerable: true, get: function () { return apiIndex_1.getAPI; } }));\nvar apirequest_1 = __webpack_require__(/*! ./apirequest */ \"(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\");\nObject.defineProperty(exports, \"createAPIRequest\", ({ enumerable: true, get: function () { return apirequest_1.createAPIRequest; } }));\nvar authplus_1 = __webpack_require__(/*! ./authplus */ \"(rsc)/./node_modules/googleapis-common/build/src/authplus.js\");\nObject.defineProperty(exports, \"AuthPlus\", ({ enumerable: true, get: function () { return authplus_1.AuthPlus; } }));\nvar discovery_1 = __webpack_require__(/*! ./discovery */ \"(rsc)/./node_modules/googleapis-common/build/src/discovery.js\");\nObject.defineProperty(exports, \"Discovery\", ({ enumerable: true, get: function () { return discovery_1.Discovery; } }));\nvar endpoint_1 = __webpack_require__(/*! ./endpoint */ \"(rsc)/./node_modules/googleapis-common/build/src/endpoint.js\");\nObject.defineProperty(exports, \"Endpoint\", ({ enumerable: true, get: function () { return endpoint_1.Endpoint; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/isbrowser.js":
/*!***************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/isbrowser.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isBrowser = void 0;\nfunction isBrowser() {\n    return typeof window !== 'undefined';\n}\nexports.isBrowser = isBrowser;\n//# sourceMappingURL=isbrowser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ29vZ2xlYXBpcy1jb21tb24vYnVpbGQvc3JjL2lzYnJvd3Nlci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCIiwic291cmNlcyI6WyIvVXNlcnMvemFjay9Eb2N1bWVudHMvR2l0SHViL2FyaXMvbm9kZV9tb2R1bGVzL2dvb2dsZWFwaXMtY29tbW9uL2J1aWxkL3NyYy9pc2Jyb3dzZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vLyBDb3B5cmlnaHQgMjAyMCBHb29nbGUgTExDXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuLy8geW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuLy8gWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4vL1xuLy8gICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4vL1xuLy8gVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuLy8gZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuLy8gV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4vLyBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4vLyBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuaXNCcm93c2VyID0gdm9pZCAwO1xuZnVuY3Rpb24gaXNCcm93c2VyKCkge1xuICAgIHJldHVybiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJztcbn1cbmV4cG9ydHMuaXNCcm93c2VyID0gaXNCcm93c2VyO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aXNicm93c2VyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/isbrowser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/package.json":
/*!*****************************************************!*\
  !*** ./node_modules/googleapis-common/package.json ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"googleapis-common","version":"7.2.0","description":"A common tooling library used by the googleapis npm module. You probably don\'t want to use this directly.","repository":"googleapis/nodejs-googleapis-common","main":"build/src/index.js","types":"build/src/index.d.ts","files":["build/src","!build/src/**/*.map"],"scripts":{"prebenchmark":"npm run compile","benchmark":"node build/benchmark/bench.js","compile":"tsc -p .","test":"c8 mocha build/test","system-test":"c8 mocha build/system-test --timeout 600000","presystem-test":"npm run compile","fix":"gts fix","prepare":"npm run compile","pretest":"npm run compile","lint":"gts check","samples-test":"mocha build/samples-test","docs":"compodoc src/","docs-test":"linkinator docs","webpack":"webpack","browser-test":"karma start","predocs-test":"npm run docs","prelint":"cd samples; npm link ../; npm install","clean":"gts clean","precompile":"gts clean"},"keywords":[],"author":"Google LLC","license":"Apache-2.0","dependencies":{"extend":"^3.0.2","gaxios":"^6.0.3","google-auth-library":"^9.7.0","qs":"^6.7.0","url-template":"^2.0.8","uuid":"^9.0.0"},"devDependencies":{"@babel/plugin-proposal-private-methods":"^7.18.6","@compodoc/compodoc":"1.1.23","@types/execa":"^0.9.0","@types/extend":"^3.0.1","@types/mocha":"^9.0.0","@types/mv":"^2.1.0","@types/ncp":"^2.0.1","@types/nock":"^11.0.0","@types/proxyquire":"^1.3.28","@types/qs":"^6.5.3","@types/sinon":"^17.0.0","@types/tmp":"0.2.6","@types/url-template":"^2.0.28","@types/uuid":"^9.0.0","c8":"^8.0.0","codecov":"^3.5.0","execa":"^5.0.0","gts":"^5.0.0","http2spy":"^2.0.0","is-docker":"^2.0.0","karma":"^6.0.0","karma-chrome-launcher":"^3.0.0","karma-coverage":"^2.0.0","karma-firefox-launcher":"^2.0.0","karma-mocha":"^2.0.0","karma-remap-coverage":"^0.1.5","karma-sourcemap-loader":"^0.4.0","karma-webpack":"^4.0.0","linkinator":"^3.1.0","mocha":"^9.2.2","mv":"^2.1.1","ncp":"^2.0.0","nock":"^13.0.0","null-loader":"^4.0.0","proxyquire":"^2.1.3","puppeteer":"^18.2.1","sinon":"^17.0.0","tmp":"^0.2.0","ts-loader":"^8.0.0","typescript":"5.1.6","webpack":"^4.0.0","webpack-cli":"^4.0.0"},"engines":{"node":">=14.0.0"}}');

/***/ })

};
;