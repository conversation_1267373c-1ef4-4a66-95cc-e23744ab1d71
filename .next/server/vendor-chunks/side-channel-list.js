"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel-list";
exports.ids = ["vendor-chunks/side-channel-list"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel-list/index.js":
/*!*************************************************!*\
  !*** ./node_modules/side-channel-list/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\n\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\n\n/*\n* This function traverses the list returning the node corresponding to the given key.\n*\n* That node is also moved to the head of the list, so that if it's accessed again we don't need to traverse the whole list.\n* By doing so, all the recently used nodes can be accessed relatively quickly.\n*/\n/** @type {import('./list.d.ts').listGetNode} */\n// eslint-disable-next-line consistent-return\nvar listGetNode = function (list, key, isDelete) {\n\t/** @type {typeof list | NonNullable<(typeof list)['next']>} */\n\tvar prev = list;\n\t/** @type {(typeof list)['next']} */\n\tvar curr;\n\t// eslint-disable-next-line eqeqeq\n\tfor (; (curr = prev.next) != null; prev = curr) {\n\t\tif (curr.key === key) {\n\t\t\tprev.next = curr.next;\n\t\t\tif (!isDelete) {\n\t\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\t\tcurr.next = /** @type {NonNullable<typeof list.next>} */ (list.next);\n\t\t\t\tlist.next = curr; // eslint-disable-line no-param-reassign\n\t\t\t}\n\t\t\treturn curr;\n\t\t}\n\t}\n};\n\n/** @type {import('./list.d.ts').listGet} */\nvar listGet = function (objects, key) {\n\tif (!objects) {\n\t\treturn void undefined;\n\t}\n\tvar node = listGetNode(objects, key);\n\treturn node && node.value;\n};\n/** @type {import('./list.d.ts').listSet} */\nvar listSet = function (objects, key, value) {\n\tvar node = listGetNode(objects, key);\n\tif (node) {\n\t\tnode.value = value;\n\t} else {\n\t\t// Prepend the new node to the beginning of the list\n\t\tobjects.next = /** @type {import('./list.d.ts').ListNode<typeof value, typeof key>} */ ({ // eslint-disable-line no-param-reassign, no-extra-parens\n\t\t\tkey: key,\n\t\t\tnext: objects.next,\n\t\t\tvalue: value\n\t\t});\n\t}\n};\n/** @type {import('./list.d.ts').listHas} */\nvar listHas = function (objects, key) {\n\tif (!objects) {\n\t\treturn false;\n\t}\n\treturn !!listGetNode(objects, key);\n};\n/** @type {import('./list.d.ts').listDelete} */\n// eslint-disable-next-line consistent-return\nvar listDelete = function (objects, key) {\n\tif (objects) {\n\t\treturn listGetNode(objects, key, true);\n\t}\n};\n\n/** @type {import('.')} */\nmodule.exports = function getSideChannelList() {\n\t/** @typedef {ReturnType<typeof getSideChannelList>} Channel */\n\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t/** @type {import('./list.d.ts').RootNode<V, K> | undefined} */ var $o;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\tvar root = $o && $o.next;\n\t\t\tvar deletedNode = listDelete($o, key);\n\t\t\tif (deletedNode && root && root === deletedNode) {\n\t\t\t\t$o = void undefined;\n\t\t\t}\n\t\t\treturn !!deletedNode;\n\t\t},\n\t\tget: function (key) {\n\t\t\treturn listGet($o, key);\n\t\t},\n\t\thas: function (key) {\n\t\t\treturn listHas($o, key);\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$o) {\n\t\t\t\t// Initialize the linked list as an empty node, so that we don't have to special-case handling of the first node: we can always refer to it as (previous node).next, instead of something like (list).head\n\t\t\t\t$o = {\n\t\t\t\t\tnext: void undefined\n\t\t\t\t};\n\t\t\t}\n\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\tlistSet(/** @type {NonNullable<typeof $o>} */ ($o), key, value);\n\t\t}\n\t};\n\t// @ts-expect-error TODO: figure out why this is erroring\n\treturn channel;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel-list/index.js\n");

/***/ })

};
;