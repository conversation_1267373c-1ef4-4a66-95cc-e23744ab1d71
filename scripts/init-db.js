#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function initializeDatabase() {
  console.log('🗄️  Initializing database tables...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase configuration in .env.local');
    console.log('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // Create user_credentials table
    console.log('Creating user_credentials table...');
    const { error: credentialsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS user_credentials (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id TEXT NOT NULL,
          service TEXT NOT NULL CHECK (service IN ('google', 'slack')),
          encrypted_credentials TEXT NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id, service)
        );
      `
    });

    if (credentialsError) {
      console.log('✅ user_credentials table already exists or created');
    } else {
      console.log('✅ user_credentials table created');
    }

    // Create app_configurations table
    console.log('Creating app_configurations table...');
    const { error: configError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS app_configurations (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id TEXT NOT NULL,
          tool_name TEXT NOT NULL,
          configuration JSONB NOT NULL DEFAULT '{}',
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (configError) {
      console.log('✅ app_configurations table already exists or created');
    } else {
      console.log('✅ app_configurations table created');
    }

    // Create activity_logs table
    console.log('Creating activity_logs table...');
    const { error: logsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS activity_logs (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id TEXT NOT NULL,
          tool_name TEXT NOT NULL,
          action TEXT NOT NULL,
          status TEXT NOT NULL CHECK (status IN ('success', 'error')),
          details JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (logsError) {
      console.log('✅ activity_logs table already exists or created');
    } else {
      console.log('✅ activity_logs table created');
    }

    // Create customers table
    console.log('Creating customers table...');
    const { error: customersError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS customers (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id TEXT NOT NULL,
          name TEXT NOT NULL,
          fee_agreement TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (customersError) {
      console.log('✅ customers table already exists or created');
    } else {
      console.log('✅ customers table created');
    }

    // Create indexes
    console.log('Creating indexes...');
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_user_credentials_user_id ON user_credentials(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_app_configurations_user_id ON app_configurations(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_customers_user_id ON customers(user_id);'
    ];

    for (const indexSql of indexes) {
      await supabase.rpc('exec_sql', { sql: indexSql });
    }

    console.log('✅ Indexes created');
    console.log('\n🎉 Database initialization complete!');
    console.log('You can now run: npm run dev');

  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    console.log('\nTroubleshooting:');
    console.log('1. Check your Supabase URL and service role key');
    console.log('2. Make sure your Supabase project is active');
    console.log('3. Verify your .env.local file is configured correctly');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  initializeDatabase();
}

module.exports = { initializeDatabase };
