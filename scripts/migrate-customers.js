#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function migrateCustomersTable() {
  console.log('🔄 Migrating customers table to simplified structure...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase configuration in .env.local');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // First, check if the table exists and what columns it has
    console.log('Checking current table structure...');
    
    // Drop and recreate the table with the new structure
    console.log('Recreating customers table with simplified structure...');
    const { error: dropError } = await supabase.rpc('exec_sql', {
      sql: `
        DROP TABLE IF EXISTS customers;
        
        CREATE TABLE customers (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id TEXT NOT NULL,
          name TEXT NOT NULL,
          fee_agreement TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_customers_user_id ON customers(user_id);
      `
    });

    if (dropError) {
      console.error('❌ Migration failed:', dropError.message);
      process.exit(1);
    }

    console.log('✅ Customers table migrated successfully!');
    console.log('\n📋 New table structure:');
    console.log('- id (UUID, Primary Key)');
    console.log('- user_id (TEXT, Required)');
    console.log('- name (TEXT, Required)');
    console.log('- fee_agreement (TEXT, Optional)');
    console.log('- created_at (TIMESTAMP)');
    console.log('- updated_at (TIMESTAMP)');
    console.log('\n🎉 Migration complete! You can now use the simplified customer manager.');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  migrateCustomersTable();
}

module.exports = { migrateCustomersTable };
