import CryptoJS from 'crypto-js'

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-key-change-in-production'

export interface EncryptedCredentials {
  google?: {
    access_token: string
    refresh_token: string
    scope: string
    token_type: string
    expiry_date: number
  }
  slack?: {
    access_token: string
    team_id: string
    team_name: string
    user_id: string
    scope: string
  }
}

export const encryptCredentials = (credentials: any): string => {
  try {
    const jsonString = JSON.stringify(credentials)
    const encrypted = CryptoJS.AES.encrypt(jsonString, ENCRYPTION_KEY).toString()
    return encrypted
  } catch (error) {
    console.error('Encryption error:', error)
    throw new Error('Failed to encrypt credentials')
  }
}

export const decryptCredentials = (encryptedData: string): any => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY)
    const decryptedString = bytes.toString(CryptoJS.enc.Utf8)
    return JSON.parse(decryptedString)
  } catch (error) {
    console.error('Decryption error:', error)
    throw new Error('Failed to decrypt credentials')
  }
}

export const hashPassword = (password: string): string => {
  return CryptoJS.SHA256(password).toString()
}

export const validateCredentials = (credentials: any, service: 'google' | 'slack'): boolean => {
  if (service === 'google') {
    return !!(
      credentials.access_token &&
      credentials.refresh_token &&
      credentials.scope &&
      credentials.token_type
    )
  }
  
  if (service === 'slack') {
    return !!(
      credentials.access_token &&
      credentials.team_id &&
      credentials.user_id &&
      credentials.scope
    )
  }
  
  return false
}
