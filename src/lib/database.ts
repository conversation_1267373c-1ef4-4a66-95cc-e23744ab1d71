import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Database schema types
export interface UserCredentials {
  id: string
  user_id: string
  service: 'google' | 'slack'
  encrypted_credentials: string
  created_at: string
  updated_at: string
}

export interface AppConfiguration {
  id: string
  user_id: string
  tool_name: string
  configuration: any
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface ActivityLog {
  id: string
  user_id: string
  tool_name: string
  action: string
  status: 'success' | 'error'
  details: any
  created_at: string
}

export interface Customer {
  id: string
  user_id: string
  name: string
  fee_agreement?: string
  aka?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

// Database initialization SQL
export const initializeDatabase = async () => {
  const { error } = await supabase.rpc('create_tables', {
    sql: `
      -- User credentials table
      CREATE TABLE IF NOT EXISTS user_credentials (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id TEXT NOT NULL,
        service TEXT NOT NULL CHECK (service IN ('google', 'slack')),
        encrypted_credentials TEXT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, service)
      );

      -- App configurations table
      CREATE TABLE IF NOT EXISTS app_configurations (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id TEXT NOT NULL,
        tool_name TEXT NOT NULL,
        configuration JSONB NOT NULL DEFAULT '{}',
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Activity logs table
      CREATE TABLE IF NOT EXISTS activity_logs (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id TEXT NOT NULL,
        tool_name TEXT NOT NULL,
        action TEXT NOT NULL,
        status TEXT NOT NULL CHECK (status IN ('success', 'error')),
        details JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Customers table
      CREATE TABLE IF NOT EXISTS customers (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        fee_agreement TEXT,
        aka TEXT,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Indexes
      CREATE INDEX IF NOT EXISTS idx_user_credentials_user_id ON user_credentials(user_id);
      CREATE INDEX IF NOT EXISTS idx_app_configurations_user_id ON app_configurations(user_id);
      CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);
      CREATE INDEX IF NOT EXISTS idx_customers_user_id ON customers(user_id);
    `
  })

  if (error) {
    console.error('Database initialization error:', error)
    throw error
  }
}
