'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Mail, FileSpreadsheet, Download, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface EmailData {
  id: string
  subject: string
  from: string
  date: string
  snippet: string
}

export default function GmailToSheets() {
  const { data: session } = useSession()
  const [emails, setEmails] = useState<EmailData[]>([])
  const [loading, setLoading] = useState(false)
  const [query, setQuery] = useState('')

  const fetchEmails = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/tools/gmail/emails?q=${encodeURIComponent(query)}`)
      const data = await response.json()
      if (data.emails) {
        setEmails(data.emails)
      }
    } catch (error) {
      console.error('Error fetching emails:', error)
    } finally {
      setLoading(false)
    }
  }

  const downloadToSheets = async () => {
    if (emails.length === 0) return

    setLoading(true)
    try {
      const response = await fetch('/api/tools/gmail/download-to-sheets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ emails })
      })
      
      const data = await response.json()
      if (data.success) {
        alert('Emails downloaded to spreadsheet successfully!')
      }
    } catch (error) {
      console.error('Error downloading emails:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="mr-4 p-2 text-gray-400 hover:text-gray-600">
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <Mail className="w-8 h-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">Gmail to Sheets</h1>
            </div>
            <button
              onClick={downloadToSheets}
              disabled={loading || emails.length === 0}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center disabled:opacity-50"
            >
              <Download className="w-4 h-4 mr-2" />
              Download to Sheets
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Search Gmail</h2>
          <div className="flex space-x-4">
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Enter search query (e.g., from:<EMAIL>, subject:invoice)"
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={fetchEmails}
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              {loading ? 'Searching...' : 'Search'}
            </button>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            Use Gmail search operators like "from:", "subject:", "has:attachment", etc.
          </p>
        </div>

        {/* Results */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              Email Results ({emails.length})
            </h2>
          </div>
          
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            </div>
          ) : emails.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              {query ? 'No emails found matching your search.' : 'Enter a search query to find emails.'}
            </div>
          ) : (
            <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
              {emails.map((email) => (
                <div key={email.id} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="text-sm font-medium text-gray-900">{email.subject}</h3>
                      <p className="text-sm text-gray-600 mt-1">From: {email.from}</p>
                      <p className="text-sm text-gray-500 mt-1">{email.date}</p>
                      <p className="text-sm text-gray-500 mt-2">{email.snippet}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-2">How to use Gmail to Sheets</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Use Gmail search operators to find specific emails</li>
            <li>• Click "Search" to fetch matching emails</li>
            <li>• Review the results and click "Download to Sheets" to export</li>
            <li>• A new spreadsheet will be created with email data</li>
          </ul>
        </div>
      </main>
    </div>
  )
}
