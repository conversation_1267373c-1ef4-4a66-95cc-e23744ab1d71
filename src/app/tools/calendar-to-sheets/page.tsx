'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Calendar, FileSpreadsheet, Plus, Settings, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface CalendarEvent {
  id: string
  summary: string
  start: { dateTime: string }
  end: { dateTime: string }
  description?: string
}

interface SpreadsheetConfig {
  id: string
  name: string
  url: string
  sheetName: string
  isActive: boolean
}

export default function CalendarToSheets() {
  const { data: session } = useSession()
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [spreadsheets, setSpreadsheets] = useState<SpreadsheetConfig[]>([])
  const [loading, setLoading] = useState(false)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newSpreadsheet, setNewSpreadsheet] = useState({
    name: '',
    sheetName: 'Calendar Events'
  })

  const fetchCalendarEvents = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/tools/calendar/events')
      const data = await response.json()
      if (data.events) {
        setEvents(data.events)
      }
    } catch (error) {
      console.error('Error fetching calendar events:', error)
    } finally {
      setLoading(false)
    }
  }

  const createSpreadsheet = async () => {
    if (!newSpreadsheet.name) return

    setLoading(true)
    try {
      const response = await fetch('/api/tools/calendar/create-spreadsheet', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newSpreadsheet)
      })
      
      const data = await response.json()
      if (data.success) {
        setSpreadsheets([...spreadsheets, data.spreadsheet])
        setNewSpreadsheet({ name: '', sheetName: 'Calendar Events' })
        setShowCreateForm(false)
      }
    } catch (error) {
      console.error('Error creating spreadsheet:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateSpreadsheet = async (spreadsheetId: string) => {
    setLoading(true)
    try {
      const response = await fetch('/api/tools/calendar/update-spreadsheet', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ spreadsheetId, events })
      })
      
      const data = await response.json()
      if (data.success) {
        alert('Spreadsheet updated successfully!')
      }
    } catch (error) {
      console.error('Error updating spreadsheet:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (session) {
      fetchCalendarEvents()
    }
  }, [session])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="mr-4 p-2 text-gray-400 hover:text-gray-600">
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <Calendar className="w-8 h-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">Calendar to Sheets</h1>
            </div>
            <button
              onClick={() => setShowCreateForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Spreadsheet
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Calendar Events */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-gray-900">Recent Calendar Events</h2>
              <button
                onClick={fetchCalendarEvents}
                disabled={loading}
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                {loading ? 'Loading...' : 'Refresh'}
              </button>
            </div>
            
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {events.length === 0 ? (
                <p className="text-gray-500 text-center py-8">
                  No calendar events found. Make sure your Google Calendar is connected.
                </p>
              ) : (
                events.map((event) => (
                  <div key={event.id} className="border border-gray-200 rounded-lg p-4">
                    <h3 className="font-medium text-gray-900">{event.summary}</h3>
                    <p className="text-sm text-gray-600 mt-1">
                      {new Date(event.start.dateTime).toLocaleString()} - 
                      {new Date(event.end.dateTime).toLocaleString()}
                    </p>
                    {event.description && (
                      <p className="text-sm text-gray-500 mt-2">{event.description}</p>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Spreadsheets */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-6">Connected Spreadsheets</h2>
            
            <div className="space-y-4">
              {spreadsheets.length === 0 ? (
                <p className="text-gray-500 text-center py-8">
                  No spreadsheets created yet. Create one to start syncing calendar events.
                </p>
              ) : (
                spreadsheets.map((sheet) => (
                  <div key={sheet.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">{sheet.name}</h3>
                        <p className="text-sm text-gray-600">Sheet: {sheet.sheetName}</p>
                        <div className="flex items-center mt-1">
                          <div className={`w-2 h-2 rounded-full mr-2 ${sheet.isActive ? 'bg-green-400' : 'bg-gray-300'}`} />
                          <span className={`text-xs ${sheet.isActive ? 'text-green-600' : 'text-gray-500'}`}>
                            {sheet.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => updateSpreadsheet(sheet.id)}
                          disabled={loading}
                          className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
                        >
                          Update
                        </button>
                        <a
                          href={sheet.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <FileSpreadsheet className="w-4 h-4" />
                        </a>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Create Spreadsheet Modal */}
        {showCreateForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Create New Spreadsheet</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Spreadsheet Name
                  </label>
                  <input
                    type="text"
                    value={newSpreadsheet.name}
                    onChange={(e) => setNewSpreadsheet({ ...newSpreadsheet, name: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="My Calendar Events"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Sheet Name
                  </label>
                  <input
                    type="text"
                    value={newSpreadsheet.sheetName}
                    onChange={(e) => setNewSpreadsheet({ ...newSpreadsheet, sheetName: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Calendar Events"
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowCreateForm(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={createSpreadsheet}
                  disabled={loading || !newSpreadsheet.name}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                  {loading ? 'Creating...' : 'Create'}
                </button>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
