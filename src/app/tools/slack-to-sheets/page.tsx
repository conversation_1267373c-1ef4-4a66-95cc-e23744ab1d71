'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { MessageSquare, FileSpreadsheet, Plus, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface SlackChannel {
  id: string
  name: string
  is_member: boolean
}

interface ChannelConnection {
  id: string
  channel_id: string
  channel_name: string
  spreadsheet_url: string
  is_active: boolean
}

export default function SlackToSheets() {
  const { data: session } = useSession()
  const [channels, setChannels] = useState<SlackChannel[]>([])
  const [connections, setConnections] = useState<ChannelConnection[]>([])
  const [loading, setLoading] = useState(false)
  const [showConnectForm, setShowConnectForm] = useState(false)
  const [selectedChannel, setSelectedChannel] = useState('')

  const fetchChannels = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/tools/slack/channels')
      const data = await response.json()
      if (data.channels) {
        setChannels(data.channels)
      }
    } catch (error) {
      console.error('Error fetching channels:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchConnections = async () => {
    try {
      const response = await fetch('/api/tools/slack/connections')
      const data = await response.json()
      if (data.connections) {
        setConnections(data.connections)
      }
    } catch (error) {
      console.error('Error fetching connections:', error)
    }
  }

  const connectChannel = async () => {
    if (!selectedChannel) return

    setLoading(true)
    try {
      const response = await fetch('/api/tools/slack/connect-channel', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ channelId: selectedChannel })
      })
      
      const data = await response.json()
      if (data.success) {
        await fetchConnections()
        setSelectedChannel('')
        setShowConnectForm(false)
      }
    } catch (error) {
      console.error('Error connecting channel:', error)
    } finally {
      setLoading(false)
    }
  }

  const connectSlack = async () => {
    // Redirect to Slack OAuth
    window.location.href = '/api/auth/slack'
  }

  useEffect(() => {
    if (session) {
      fetchChannels()
      fetchConnections()
    }
  }, [session])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="mr-4 p-2 text-gray-400 hover:text-gray-600">
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <MessageSquare className="w-8 h-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">Slack to Sheets</h1>
            </div>
            <button
              onClick={() => setShowConnectForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Connect Channel
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {channels.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <MessageSquare className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-medium text-gray-900 mb-2">Connect to Slack</h2>
            <p className="text-gray-600 mb-6">
              Connect your Slack workspace to start syncing channel messages to spreadsheets.
            </p>
            <button
              onClick={connectSlack}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
            >
              Connect Slack Workspace
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Available Channels */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Available Channels</h2>
              
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {channels.map((channel) => (
                  <div key={channel.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div>
                      <h3 className="font-medium text-gray-900">#{channel.name}</h3>
                      <p className="text-sm text-gray-600">
                        {channel.is_member ? 'Member' : 'Not a member'}
                      </p>
                    </div>
                    <div className="flex items-center">
                      {connections.find(c => c.channel_id === channel.id) ? (
                        <span className="text-green-600 text-sm font-medium">Connected</span>
                      ) : (
                        <span className="text-gray-400 text-sm">Not connected</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Connected Channels */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Connected Channels</h2>
              
              {connections.length === 0 ? (
                <p className="text-gray-500 text-center py-8">
                  No channels connected yet. Connect a channel to start syncing messages.
                </p>
              ) : (
                <div className="space-y-4">
                  {connections.map((connection) => (
                    <div key={connection.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-medium text-gray-900">#{connection.channel_name}</h3>
                          <div className="flex items-center mt-1">
                            <div className={`w-2 h-2 rounded-full mr-2 ${connection.is_active ? 'bg-green-400' : 'bg-gray-300'}`} />
                            <span className={`text-xs ${connection.is_active ? 'text-green-600' : 'text-gray-500'}`}>
                              {connection.is_active ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                        </div>
                        <a
                          href={connection.spreadsheet_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <FileSpreadsheet className="w-4 h-4" />
                        </a>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Connect Channel Modal */}
        {showConnectForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Connect Channel to Spreadsheet</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Select Channel
                  </label>
                  <select
                    value={selectedChannel}
                    onChange={(e) => setSelectedChannel(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Choose a channel...</option>
                    {channels
                      .filter(channel => !connections.find(c => c.channel_id === channel.id))
                      .map((channel) => (
                        <option key={channel.id} value={channel.id}>
                          #{channel.name}
                        </option>
                      ))}
                  </select>
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowConnectForm(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={connectChannel}
                  disabled={loading || !selectedChannel}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                  {loading ? 'Connecting...' : 'Connect'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-2">How to use Slack to Sheets</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Connect your Slack workspace to access channels</li>
            <li>• Select a channel to connect to a new spreadsheet</li>
            <li>• Messages from the channel will be automatically synced</li>
            <li>• View and manage your data in the connected spreadsheet</li>
          </ul>
        </div>
      </main>
    </div>
  )
}
