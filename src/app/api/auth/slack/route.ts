import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.email) {
      return NextResponse.redirect(new URL('/auth/error?error=unauthorized', request.url))
    }

    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const error = searchParams.get('error')

    if (error) {
      console.error('Slack OAuth error:', error)
      return NextResponse.redirect(new URL('/settings?error=slack_auth_failed', request.url))
    }

    if (!code) {
      // Redirect to Slack OAuth
      const slackAuthUrl = new URL('https://slack.com/oauth/v2/authorize')
      slackAuthUrl.searchParams.set('client_id', process.env.SLACK_CLIENT_ID!)
      slackAuthUrl.searchParams.set('scope', 'channels:read,chat:write,users:read')
      slackAuthUrl.searchParams.set('redirect_uri', `${process.env.NEXTAUTH_URL}/api/auth/slack`)
      slackAuthUrl.searchParams.set('state', session.user.email)

      return NextResponse.redirect(slackAuthUrl.toString())
    }

    // Exchange code for access token
    const tokenResponse = await fetch('https://slack.com/api/oauth.v2.access', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.SLACK_CLIENT_ID!,
        client_secret: process.env.SLACK_CLIENT_SECRET!,
        code,
        redirect_uri: `${process.env.NEXTAUTH_URL}/api/auth/slack`,
      }),
    })

    const tokenData = await tokenResponse.json()

    if (!tokenData.ok) {
      console.error('Slack token exchange error:', tokenData.error)
      return NextResponse.redirect(new URL('/settings?error=slack_token_failed', request.url))
    }

    // Store encrypted credentials
    const credentials = {
      access_token: tokenData.access_token,
      team_id: tokenData.team.id,
      team_name: tokenData.team.name,
      user_id: tokenData.authed_user.id,
      scope: tokenData.scope,
    }

    // Save to database (we'll need to import the encryption and database functions)
    const { supabase } = await import('@/lib/database')
    const { encryptCredentials } = await import('@/lib/encryption')

    const encryptedCredentials = encryptCredentials(credentials)

    await supabase
      .from('user_credentials')
      .upsert({
        user_id: session.user.email,
        service: 'slack',
        encrypted_credentials: encryptedCredentials,
        updated_at: new Date().toISOString(),
      })

    // Log activity
    await supabase
      .from('activity_logs')
      .insert({
        user_id: session.user.email,
        tool_name: 'slack-integration',
        action: 'connect_slack',
        status: 'success',
        details: { team_name: tokenData.team.name, team_id: tokenData.team.id }
      })

    return NextResponse.redirect(new URL('/settings?success=slack_connected', request.url))
  } catch (error) {
    console.error('Slack OAuth error:', error)
    return NextResponse.redirect(new URL('/settings?error=slack_connection_failed', request.url))
  }
}
