import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { google } from 'googleapis'
import { supabase } from '@/lib/database'
import { decryptCredentials } from '@/lib/encryption'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { channelId } = await request.json()

    if (!channelId) {
      return NextResponse.json({ error: 'Channel ID is required' }, { status: 400 })
    }

    // Get Slack credentials
    const { data: slackCredentialsData, error: slackCredentialsError } = await supabase
      .from('user_credentials')
      .select('encrypted_credentials')
      .eq('user_id', session.user.email)
      .eq('service', 'slack')
      .single()

    if (slackCredentialsError || !slackCredentialsData) {
      return NextResponse.json({ error: 'Slack credentials not found' }, { status: 404 })
    }

    const slackCredentials = decryptCredentials(slackCredentialsData.encrypted_credentials)

    // Get channel info from Slack
    const channelInfoResponse = await fetch(`https://slack.com/api/conversations.info?channel=${channelId}`, {
      headers: {
        'Authorization': `Bearer ${slackCredentials.access_token}`,
        'Content-Type': 'application/json',
      },
    })

    const channelInfoData = await channelInfoResponse.json()

    if (!channelInfoData.ok) {
      console.error('Slack API error:', channelInfoData.error)
      return NextResponse.json({ error: 'Failed to get channel info' }, { status: 500 })
    }

    const channelName = channelInfoData.channel.name

    // Get Google credentials
    const { data: googleCredentialsData, error: googleCredentialsError } = await supabase
      .from('user_credentials')
      .select('encrypted_credentials')
      .eq('user_id', session.user.email)
      .eq('service', 'google')
      .single()

    if (googleCredentialsError || !googleCredentialsData) {
      return NextResponse.json({ error: 'Google credentials not found' }, { status: 404 })
    }

    const googleCredentials = decryptCredentials(googleCredentialsData.encrypted_credentials)

    // Set up Google Sheets API
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.NEXTAUTH_URL + '/api/auth/callback/google'
    )

    oauth2Client.setCredentials({
      access_token: googleCredentials.access_token,
      refresh_token: googleCredentials.refresh_token,
    })

    const sheets = google.sheets({ version: 'v4', auth: oauth2Client })

    // Create new spreadsheet for this Slack channel
    const spreadsheetName = `Slack #${channelName} Messages`
    const createResponse = await sheets.spreadsheets.create({
      requestBody: {
        properties: {
          title: spreadsheetName,
        },
        sheets: [
          {
            properties: {
              title: 'Messages',
            },
          },
        ],
      },
    })

    const spreadsheetId = createResponse.data.spreadsheetId!
    const spreadsheetUrl = `https://docs.google.com/spreadsheets/d/${spreadsheetId}`

    // Add headers to the sheet
    await sheets.spreadsheets.values.update({
      spreadsheetId,
      range: 'Messages!A1:E1',
      valueInputOption: 'RAW',
      requestBody: {
        values: [
          ['Timestamp', 'User', 'Message', 'Thread', 'Reactions']
        ],
      },
    })

    // Format headers
    await sheets.spreadsheets.batchUpdate({
      spreadsheetId,
      requestBody: {
        requests: [
          {
            repeatCell: {
              range: {
                sheetId: 0,
                startRowIndex: 0,
                endRowIndex: 1,
                startColumnIndex: 0,
                endColumnIndex: 5,
              },
              cell: {
                userEnteredFormat: {
                  backgroundColor: { red: 0.9, green: 0.9, blue: 0.9 },
                  textFormat: { bold: true },
                },
              },
              fields: 'userEnteredFormat(backgroundColor,textFormat)',
            },
          },
        ],
      },
    })

    // Save configuration to database
    const { data: configData, error: configError } = await supabase
      .from('app_configurations')
      .insert({
        user_id: session.user.email,
        tool_name: 'slack-to-sheets',
        configuration: {
          channel_id: channelId,
          channel_name: channelName,
          spreadsheet_id: spreadsheetId,
          spreadsheet_url: spreadsheetUrl,
          team_id: slackCredentials.team_id,
          team_name: slackCredentials.team_name,
        },
        is_active: true,
      })
      .select()
      .single()

    if (configError) {
      console.error('Database error:', configError)
      return NextResponse.json({ error: 'Failed to save configuration' }, { status: 500 })
    }

    // Log activity
    await supabase
      .from('activity_logs')
      .insert({
        user_id: session.user.email,
        tool_name: 'slack-to-sheets',
        action: 'connect_channel',
        status: 'success',
        details: { 
          channel_id: channelId,
          channel_name: channelName,
          spreadsheet_id: spreadsheetId 
        }
      })

    return NextResponse.json({ 
      success: true, 
      connection: {
        id: configData.id,
        channel_id: channelId,
        channel_name: channelName,
        spreadsheet_url: spreadsheetUrl,
        is_active: true,
      }
    })
  } catch (error) {
    console.error('Slack connect channel error:', error)
    
    // Log error
    const session = await getServerSession()
    if (session?.user?.email) {
      await supabase
        .from('activity_logs')
        .insert({
          user_id: session.user.email,
          tool_name: 'slack-to-sheets',
          action: 'connect_channel',
          status: 'error',
          details: { error: error instanceof Error ? error.message : 'Unknown error' }
        })
    }

    return NextResponse.json({ error: 'Failed to connect channel' }, { status: 500 })
  }
}
