import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { supabase } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get Slack channel connections from database
    const { data, error } = await supabase
      .from('app_configurations')
      .select('*')
      .eq('user_id', session.user.email)
      .eq('tool_name', 'slack-to-sheets')
      .eq('is_active', true)

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }

    // Format connections for frontend
    const connections = data?.map(config => ({
      id: config.id,
      channel_id: config.configuration.channel_id,
      channel_name: config.configuration.channel_name,
      spreadsheet_url: config.configuration.spreadsheet_url,
      spreadsheet_id: config.configuration.spreadsheet_id,
      is_active: config.is_active,
      created_at: config.created_at,
    })) || []

    return NextResponse.json({ connections })
  } catch (error) {
    console.error('Slack connections API error:', error)
    return NextResponse.json({ error: 'Failed to fetch connections' }, { status: 500 })
  }
}
