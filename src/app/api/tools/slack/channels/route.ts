import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { supabase } from '@/lib/database'
import { decryptCredentials } from '@/lib/encryption'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get Slack credentials from database
    const { data: credentialsData, error: credentialsError } = await supabase
      .from('user_credentials')
      .select('encrypted_credentials')
      .eq('user_id', session.user.email)
      .eq('service', 'slack')
      .single()

    if (credentialsError || !credentialsData) {
      return NextResponse.json({ error: 'Slack credentials not found' }, { status: 404 })
    }

    const credentials = decryptCredentials(credentialsData.encrypted_credentials)

    // Fetch channels from Slack API
    const channelsResponse = await fetch('https://slack.com/api/conversations.list', {
      headers: {
        'Authorization': `Bear<PERSON> ${credentials.access_token}`,
        'Content-Type': 'application/json',
      },
    })

    const channelsData = await channelsResponse.json()

    if (!channelsData.ok) {
      console.error('Slack API error:', channelsData.error)
      return NextResponse.json({ error: 'Failed to fetch Slack channels' }, { status: 500 })
    }

    // Filter and format channels
    const channels = channelsData.channels
      .filter((channel: any) => !channel.is_archived && channel.is_channel)
      .map((channel: any) => ({
        id: channel.id,
        name: channel.name,
        is_member: channel.is_member,
        is_private: channel.is_private,
        num_members: channel.num_members,
      }))

    // Log activity
    await supabase
      .from('activity_logs')
      .insert({
        user_id: session.user.email,
        tool_name: 'slack-to-sheets',
        action: 'fetch_channels',
        status: 'success',
        details: { channels_count: channels.length }
      })

    return NextResponse.json({ channels })
  } catch (error) {
    console.error('Slack channels API error:', error)
    
    // Log error
    const session = await getServerSession()
    if (session?.user?.email) {
      await supabase
        .from('activity_logs')
        .insert({
          user_id: session.user.email,
          tool_name: 'slack-to-sheets',
          action: 'fetch_channels',
          status: 'error',
          details: { error: error instanceof Error ? error.message : 'Unknown error' }
        })
    }

    return NextResponse.json({ error: 'Failed to fetch channels' }, { status: 500 })
  }
}
