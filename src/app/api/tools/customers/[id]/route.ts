import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { supabase } from '@/lib/database'

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { name, fee_agreement, aka, is_active } = await request.json()

    // Handle status-only updates (when toggling active/inactive)
    if (is_active !== undefined && !name) {
      const { data, error } = await supabase
        .from('customers')
        .update({
          is_active,
          updated_at: new Date().toISOString(),
        })
        .eq('id', params.id)
        .eq('user_id', session.user.email)
        .select()
        .single()

      if (error) {
        console.error('Database error:', error)
        return NextResponse.json({ error: 'Database error' }, { status: 500 })
      }

      if (!data) {
        return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
      }

      return NextResponse.json({ success: true, customer: data })
    }

    // Handle full updates
    if (!name || !name.trim()) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 })
    }

    const { data, error } = await supabase
      .from('customers')
      .update({
        name: name.trim(),
        fee_agreement: fee_agreement || null,
        aka: aka || null,
        is_active: is_active !== undefined ? is_active : true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', params.id)
      .eq('user_id', session.user.email)
      .select()
      .single()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }

    if (!data) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    // Log activity
    await supabase
      .from('activity_logs')
      .insert({
        user_id: session.user.email,
        tool_name: 'customer-manager',
        action: 'update_customer',
        status: 'success',
        details: { customer_id: params.id, name }
      })

    return NextResponse.json({ success: true, customer: data })
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // First get the customer to log the name
    const { data: customer } = await supabase
      .from('customers')
      .select('name')
      .eq('id', params.id)
      .eq('user_id', session.user.email)
      .single()

    const { error } = await supabase
      .from('customers')
      .delete()
      .eq('id', params.id)
      .eq('user_id', session.user.email)

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }

    // Log activity
    await supabase
      .from('activity_logs')
      .insert({
        user_id: session.user.email,
        tool_name: 'customer-manager',
        action: 'delete_customer',
        status: 'success',
        details: { customer_id: params.id, name: customer?.name || 'Unknown' }
      })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
