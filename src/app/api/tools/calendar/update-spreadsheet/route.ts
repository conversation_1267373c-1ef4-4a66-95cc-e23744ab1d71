import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { google } from 'googleapis'
import { supabase } from '@/lib/database'
import { decryptCredentials } from '@/lib/encryption'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { spreadsheetId, events } = await request.json()

    if (!spreadsheetId || !events) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Get Google credentials from database
    const { data: credentialsData, error: credentialsError } = await supabase
      .from('user_credentials')
      .select('encrypted_credentials')
      .eq('user_id', session.user.email)
      .eq('service', 'google')
      .single()

    if (credentialsError || !credentialsData) {
      return NextResponse.json({ error: 'Google credentials not found' }, { status: 404 })
    }

    const credentials = decryptCredentials(credentialsData.encrypted_credentials)

    // Get spreadsheet configuration
    const { data: configData, error: configError } = await supabase
      .from('app_configurations')
      .select('configuration')
      .eq('user_id', session.user.email)
      .eq('tool_name', 'calendar-to-sheets')
      .eq('id', spreadsheetId)
      .single()

    if (configError || !configData) {
      return NextResponse.json({ error: 'Spreadsheet configuration not found' }, { status: 404 })
    }

    const config = configData.configuration
    const actualSpreadsheetId = config.spreadsheet_id
    const sheetName = config.sheet_name

    // Set up Google Sheets API
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.NEXTAUTH_URL + '/api/auth/callback/google'
    )

    oauth2Client.setCredentials({
      access_token: credentials.access_token,
      refresh_token: credentials.refresh_token,
    })

    const sheets = google.sheets({ version: 'v4', auth: oauth2Client })

    // Clear existing data (except headers)
    await sheets.spreadsheets.values.clear({
      spreadsheetId: actualSpreadsheetId,
      range: `${sheetName}!A2:G`,
    })

    // Prepare event data for spreadsheet
    const eventRows = events.map((event: any) => {
      const startDate = event.start?.dateTime ? new Date(event.start.dateTime) : null
      const endDate = event.end?.dateTime ? new Date(event.end.dateTime) : null

      return [
        event.summary || 'No title',
        startDate ? startDate.toLocaleDateString() : '',
        startDate ? startDate.toLocaleTimeString() : '',
        endDate ? endDate.toLocaleDateString() : '',
        endDate ? endDate.toLocaleTimeString() : '',
        event.description || '',
        event.location || '',
      ]
    })

    // Update spreadsheet with new data
    if (eventRows.length > 0) {
      await sheets.spreadsheets.values.update({
        spreadsheetId: actualSpreadsheetId,
        range: `${sheetName}!A2:G${eventRows.length + 1}`,
        valueInputOption: 'RAW',
        requestBody: {
          values: eventRows,
        },
      })
    }

    // Log activity
    await supabase
      .from('activity_logs')
      .insert({
        user_id: session.user.email,
        tool_name: 'calendar-to-sheets',
        action: 'update_spreadsheet',
        status: 'success',
        details: { 
          spreadsheet_id: actualSpreadsheetId, 
          events_count: eventRows.length 
        }
      })

    return NextResponse.json({ 
      success: true, 
      message: `Updated spreadsheet with ${eventRows.length} events` 
    })
  } catch (error) {
    console.error('Spreadsheet update error:', error)
    
    // Log error
    const session = await getServerSession()
    if (session?.user?.email) {
      await supabase
        .from('activity_logs')
        .insert({
          user_id: session.user.email,
          tool_name: 'calendar-to-sheets',
          action: 'update_spreadsheet',
          status: 'error',
          details: { error: error instanceof Error ? error.message : 'Unknown error' }
        })
    }

    return NextResponse.json({ error: 'Failed to update spreadsheet' }, { status: 500 })
  }
}
