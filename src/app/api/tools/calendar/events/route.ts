import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { google } from 'googleapis'
import { supabase } from '@/lib/database'
import { decryptCredentials } from '@/lib/encryption'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get Google credentials from database
    const { data: credentialsData, error: credentialsError } = await supabase
      .from('user_credentials')
      .select('encrypted_credentials')
      .eq('user_id', session.user.email)
      .eq('service', 'google')
      .single()

    if (credentialsError || !credentialsData) {
      return NextResponse.json({ error: 'Google credentials not found' }, { status: 404 })
    }

    const credentials = decryptCredentials(credentialsData.encrypted_credentials)

    // Set up Google Calendar API
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.NEXTAUTH_URL + '/api/auth/callback/google'
    )

    oauth2Client.setCredentials({
      access_token: credentials.access_token,
      refresh_token: credentials.refresh_token,
    })

    const calendar = google.calendar({ version: 'v3', auth: oauth2Client })

    // Get calendar events from the last 30 days
    const timeMin = new Date()
    timeMin.setDate(timeMin.getDate() - 30)
    
    const timeMax = new Date()
    timeMax.setDate(timeMax.getDate() + 30)

    const response = await calendar.events.list({
      calendarId: 'primary',
      timeMin: timeMin.toISOString(),
      timeMax: timeMax.toISOString(),
      maxResults: 50,
      singleEvents: true,
      orderBy: 'startTime',
    })

    const events = response.data.items?.map(event => ({
      id: event.id,
      summary: event.summary || 'No title',
      start: event.start,
      end: event.end,
      description: event.description,
      location: event.location,
      attendees: event.attendees?.map(attendee => attendee.email) || [],
    })) || []

    // Log activity
    await supabase
      .from('activity_logs')
      .insert({
        user_id: session.user.email,
        tool_name: 'calendar-to-sheets',
        action: 'fetch_events',
        status: 'success',
        details: { event_count: events.length }
      })

    return NextResponse.json({ events })
  } catch (error) {
    console.error('Calendar API error:', error)
    
    // Log error
    const session = await getServerSession()
    if (session?.user?.email) {
      await supabase
        .from('activity_logs')
        .insert({
          user_id: session.user.email,
          tool_name: 'calendar-to-sheets',
          action: 'fetch_events',
          status: 'error',
          details: { error: error instanceof Error ? error.message : 'Unknown error' }
        })
    }

    return NextResponse.json({ error: 'Failed to fetch calendar events' }, { status: 500 })
  }
}
