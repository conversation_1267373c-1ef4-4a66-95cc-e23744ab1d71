import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { google } from 'googleapis'
import { supabase } from '@/lib/database'
import { decryptCredentials } from '@/lib/encryption'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { name, sheetName } = await request.json()

    if (!name || !sheetName) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Get Google credentials from database
    const { data: credentialsData, error: credentialsError } = await supabase
      .from('user_credentials')
      .select('encrypted_credentials')
      .eq('user_id', session.user.email)
      .eq('service', 'google')
      .single()

    if (credentialsError || !credentialsData) {
      return NextResponse.json({ error: 'Google credentials not found' }, { status: 404 })
    }

    const credentials = decryptCredentials(credentialsData.encrypted_credentials)

    // Set up Google Sheets API
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.NEXTAUTH_URL + '/api/auth/callback/google'
    )

    oauth2Client.setCredentials({
      access_token: credentials.access_token,
      refresh_token: credentials.refresh_token,
    })

    const sheets = google.sheets({ version: 'v4', auth: oauth2Client })

    // Create new spreadsheet
    const createResponse = await sheets.spreadsheets.create({
      requestBody: {
        properties: {
          title: name,
        },
        sheets: [
          {
            properties: {
              title: sheetName,
            },
          },
        ],
      },
    })

    const spreadsheetId = createResponse.data.spreadsheetId!
    const spreadsheetUrl = `https://docs.google.com/spreadsheets/d/${spreadsheetId}`

    // Add headers to the sheet
    await sheets.spreadsheets.values.update({
      spreadsheetId,
      range: `${sheetName}!A1:G1`,
      valueInputOption: 'RAW',
      requestBody: {
        values: [
          ['Title', 'Start Date', 'Start Time', 'End Date', 'End Time', 'Description', 'Location']
        ],
      },
    })

    // Format headers
    await sheets.spreadsheets.batchUpdate({
      spreadsheetId,
      requestBody: {
        requests: [
          {
            repeatCell: {
              range: {
                sheetId: 0,
                startRowIndex: 0,
                endRowIndex: 1,
                startColumnIndex: 0,
                endColumnIndex: 7,
              },
              cell: {
                userEnteredFormat: {
                  backgroundColor: { red: 0.9, green: 0.9, blue: 0.9 },
                  textFormat: { bold: true },
                },
              },
              fields: 'userEnteredFormat(backgroundColor,textFormat)',
            },
          },
        ],
      },
    })

    // Save configuration to database
    const { data: configData, error: configError } = await supabase
      .from('app_configurations')
      .insert({
        user_id: session.user.email,
        tool_name: 'calendar-to-sheets',
        configuration: {
          spreadsheet_id: spreadsheetId,
          spreadsheet_name: name,
          sheet_name: sheetName,
          spreadsheet_url: spreadsheetUrl,
        },
        is_active: true,
      })
      .select()
      .single()

    if (configError) {
      console.error('Database error:', configError)
      return NextResponse.json({ error: 'Failed to save configuration' }, { status: 500 })
    }

    // Log activity
    await supabase
      .from('activity_logs')
      .insert({
        user_id: session.user.email,
        tool_name: 'calendar-to-sheets',
        action: 'create_spreadsheet',
        status: 'success',
        details: { spreadsheet_id: spreadsheetId, name }
      })

    const spreadsheetConfig = {
      id: configData.id,
      name,
      url: spreadsheetUrl,
      sheetName,
      isActive: true,
    }

    return NextResponse.json({ success: true, spreadsheet: spreadsheetConfig })
  } catch (error) {
    console.error('Spreadsheet creation error:', error)
    
    // Log error
    const session = await getServerSession()
    if (session?.user?.email) {
      await supabase
        .from('activity_logs')
        .insert({
          user_id: session.user.email,
          tool_name: 'calendar-to-sheets',
          action: 'create_spreadsheet',
          status: 'error',
          details: { error: error instanceof Error ? error.message : 'Unknown error' }
        })
    }

    return NextResponse.json({ error: 'Failed to create spreadsheet' }, { status: 500 })
  }
}
