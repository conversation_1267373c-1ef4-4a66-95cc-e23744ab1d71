import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { supabase } from '@/lib/database'
import { encryptCredentials, decryptCredentials } from '@/lib/encryption'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const service = searchParams.get('service')

    let query = supabase
      .from('user_credentials')
      .select('*')
      .eq('user_id', session.user.email)

    if (service) {
      query = query.eq('service', service)
    }

    const { data, error } = await query

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }

    // Decrypt credentials before sending
    const decryptedData = data?.map(item => ({
      ...item,
      credentials: decryptCredentials(item.encrypted_credentials)
    }))

    return NextResponse.json({ credentials: decryptedData })
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { service, credentials } = await request.json()

    if (!service || !credentials) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    const encryptedCredentials = encryptCredentials(credentials)

    const { data, error } = await supabase
      .from('user_credentials')
      .upsert({
        user_id: session.user.email,
        service,
        encrypted_credentials: encryptedCredentials,
        updated_at: new Date().toISOString()
      })
      .select()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }

    return NextResponse.json({ success: true, data })
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const service = searchParams.get('service')

    if (!service) {
      return NextResponse.json({ error: 'Service parameter required' }, { status: 400 })
    }

    const { error } = await supabase
      .from('user_credentials')
      .delete()
      .eq('user_id', session.user.email)
      .eq('service', service)

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
