'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Settings, Shield, CheckCircle, XCircle, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface ServiceStatus {
  service: string
  connected: boolean
  lastUpdated?: string
  scopes?: string[]
}

export default function SettingsPage() {
  const { data: session } = useSession()
  const [services, setServices] = useState<ServiceStatus[]>([])
  const [loading, setLoading] = useState(false)

  const fetchServiceStatus = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/credentials')
      const data = await response.json()
      
      const serviceStatuses: ServiceStatus[] = [
        {
          service: 'Google',
          connected: data.credentials?.some((c: any) => c.service === 'google') || false,
          lastUpdated: data.credentials?.find((c: any) => c.service === 'google')?.updated_at,
          scopes: ['Calendar', 'Gmail', 'Sheets']
        },
        {
          service: 'Slack',
          connected: data.credentials?.some((c: any) => c.service === 'slack') || false,
          lastUpdated: data.credentials?.find((c: any) => c.service === 'slack')?.updated_at,
          scopes: ['Channels', 'Messages', 'Users']
        }
      ]
      
      setServices(serviceStatuses)
    } catch (error) {
      console.error('Error fetching service status:', error)
    } finally {
      setLoading(false)
    }
  }

  const disconnectService = async (service: string) => {
    if (!confirm(`Are you sure you want to disconnect ${service}? This will stop all related integrations.`)) {
      return
    }

    setLoading(true)
    try {
      const response = await fetch(`/api/credentials?service=${service.toLowerCase()}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        await fetchServiceStatus()
        alert(`${service} disconnected successfully`)
      }
    } catch (error) {
      console.error('Error disconnecting service:', error)
    } finally {
      setLoading(false)
    }
  }

  const connectSlack = () => {
    window.location.href = '/api/auth/slack'
  }

  // Check for URL parameters to show success/error messages
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const success = urlParams.get('success')
    const error = urlParams.get('error')

    if (success === 'slack_connected') {
      alert('Slack connected successfully!')
      // Remove the parameter from URL
      window.history.replaceState({}, document.title, window.location.pathname)
      fetchServiceStatus()
    } else if (error) {
      let errorMessage = 'Failed to connect to Slack'
      if (error === 'slack_auth_failed') errorMessage = 'Slack authorization failed'
      if (error === 'slack_token_failed') errorMessage = 'Failed to get Slack access token'
      if (error === 'slack_connection_failed') errorMessage = 'Slack connection failed'

      alert(errorMessage)
      // Remove the parameter from URL
      window.history.replaceState({}, document.title, window.location.pathname)
    }
  }, [])

  useEffect(() => {
    if (session) {
      fetchServiceStatus()
    }
  }, [session])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link href="/" className="mr-4 p-2 text-gray-400 hover:text-gray-600">
              <ArrowLeft className="w-5 h-5" />
            </Link>
            <Settings className="w-8 h-8 text-blue-600 mr-3" />
            <h1 className="text-xl font-semibold text-gray-900">Settings</h1>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Account Information */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Account Information</h2>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-700">Name</label>
              <p className="text-gray-900">{session?.user?.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Email</label>
              <p className="text-gray-900">{session?.user?.email}</p>
            </div>
          </div>
        </div>

        {/* Connected Services */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-medium text-gray-900">Connected Services</h2>
            <button
              onClick={fetchServiceStatus}
              disabled={loading}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              {loading ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>

          <div className="space-y-4">
            {services.map((service) => (
              <div key={service.service} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex items-center mr-4">
                      {service.connected ? (
                        <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                      ) : (
                        <XCircle className="w-5 h-5 text-red-500 mr-2" />
                      )}
                      <h3 className="text-lg font-medium text-gray-900">{service.service}</h3>
                    </div>
                    <div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        service.connected 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {service.connected ? 'Connected' : 'Disconnected'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    {service.connected ? (
                      <button
                        onClick={() => disconnectService(service.service)}
                        disabled={loading}
                        className="px-3 py-1 text-sm text-red-600 border border-red-300 rounded hover:bg-red-50 transition-colors"
                      >
                        Disconnect
                      </button>
                    ) : (
                      service.service === 'Slack' && (
                        <button
                          onClick={connectSlack}
                          className="px-3 py-1 text-sm text-blue-600 border border-blue-300 rounded hover:bg-blue-50 transition-colors"
                        >
                          Connect
                        </button>
                      )
                    )}
                  </div>
                </div>
                
                {service.connected && (
                  <div className="mt-3 text-sm text-gray-600">
                    <p>Last updated: {service.lastUpdated ? new Date(service.lastUpdated).toLocaleString() : 'Unknown'}</p>
                    <p>Permissions: {service.scopes?.join(', ')}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Security Information */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <div className="flex items-start">
            <Shield className="w-6 h-6 text-blue-600 mr-3 mt-0.5" />
            <div>
              <h3 className="text-lg font-medium text-blue-900 mb-2">Security & Privacy</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• All credentials are encrypted using AES-256 encryption</li>
                <li>• API tokens are stored securely and never exposed in logs</li>
                <li>• You can disconnect services at any time</li>
                <li>• Data is only accessed when you explicitly trigger an action</li>
                <li>• No data is shared with third parties</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
