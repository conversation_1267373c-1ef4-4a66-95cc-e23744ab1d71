'use client'

import { useSession, signIn, signOut } from 'next-auth/react'
import { useEffect, useState } from 'react'
import { Calendar, Mail, MessageSquare, Users, Settings, LogOut, Shield, Plus, X } from 'lucide-react'
import Link from 'next/link'

interface Tool {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  path: string
  isActive: boolean
}

interface Feature {
  text: string
  completed: boolean
}

export default function Home() {
  const { data: session, status } = useSession()
  const [tools, setTools] = useState<Tool[]>([
    {
      id: 'calendar-to-sheets',
      name: 'Calendar to Sheets',
      description: 'Create and update spreadsheets from calendar events',
      icon: <Calendar className="w-6 h-6" />,
      path: '/tools/calendar-to-sheets',
      isActive: false
    },
    {
      id: 'gmail-to-sheets',
      name: 'Gmail to Sheets',
      description: 'Download and update email data to spreadsheets',
      icon: <Mail className="w-6 h-6" />,
      path: '/tools/gmail-to-sheets',
      isActive: false
    },
    {
      id: 'slack-to-sheets',
      name: 'Slack to She<PERSON>',
      description: 'Connect Slack channels to spreadsheets',
      icon: <MessageSquare className="w-6 h-6" />,
      path: '/tools/slack-to-sheets',
      isActive: false
    },
    {
      id: 'customer-manager',
      name: 'Customer Manager',
      description: 'Manage and edit customer information',
      icon: <Users className="w-6 h-6" />,
      path: '/tools/customer-manager',
      isActive: false
    }
  ])

  // Features to build state with localStorage persistence
  const [featureList, setFeatureList] = useState<Feature[]>([])
  const [showFeatureForm, setShowFeatureForm] = useState(false)
  const [newFeatureText, setNewFeatureText] = useState('')

  // Load features from localStorage on component mount
  useEffect(() => {
    const savedFeatures = localStorage.getItem('featureList')
    if (savedFeatures) {
      setFeatureList(JSON.parse(savedFeatures))
    } else {
      // Default features if none saved
      const defaultFeatures = [
        { text: "Fix Google Status Connection", completed: false },
        { text: "Add Slack OAuth Integration", completed: false },
        { text: "Implement Gmail Search Filters", completed: false },
        { text: "Add Export to CSV Feature", completed: false }
      ]
      setFeatureList(defaultFeatures)
      localStorage.setItem('featureList', JSON.stringify(defaultFeatures))
    }
  }, [])

  // Save features to localStorage whenever the list changes
  useEffect(() => {
    if (featureList.length > 0) {
      localStorage.setItem('featureList', JSON.stringify(featureList))
    }
  }, [featureList])

  // Feature management functions
  const addFeature = () => {
    if (newFeatureText.trim()) {
      setFeatureList([...featureList, { text: newFeatureText.trim(), completed: false }])
      setNewFeatureText('')
      setShowFeatureForm(false)
    }
  }

  const toggleFeature = (index: number) => {
    const updated = featureList.map((feature, i) =>
      i === index ? { ...feature, completed: !feature.completed } : feature
    )
    setFeatureList(updated)
  }

  const removeFeature = (index: number) => {
    setFeatureList(featureList.filter((_, i) => i !== index))
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
          <div className="text-center">
            <Shield className="w-16 h-16 text-blue-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">API Integration Hub</h1>
            <p className="text-gray-600 mb-8">
              Securely connect and manage your Google, Slack, and other API integrations
            </p>
            <button
              onClick={() => signIn('google')}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Sign in with Google
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Shield className="w-8 h-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">API Integration Hub</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">Welcome, {session.user?.name}</span>
              <Link href="/settings" className="p-2 text-gray-400 hover:text-gray-600">
                <Settings className="w-5 h-5" />
              </Link>
              <button
                onClick={() => signOut()}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Integration Tools</h2>
          <p className="text-gray-600">
            Manage your API integrations and automate data workflows
          </p>
        </div>

        {/* Tools Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {tools.map((tool) => (
            <Link
              key={tool.id}
              href={tool.path}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center mb-4">
                <div className="p-2 bg-blue-100 rounded-lg mr-3">
                  {tool.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-gray-900">{tool.name}</h3>
                  {tool.id !== 'customer-manager' && (
                    <div className="flex items-center mt-1">
                      <div className={`w-2 h-2 rounded-full mr-2 ${tool.isActive ? 'bg-green-400' : 'bg-gray-300'}`} />
                      <span className={`text-xs ${tool.isActive ? 'text-green-600' : 'text-gray-500'}`}>
                        {tool.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  )}
                </div>
              </div>
              <p className="text-sm text-gray-600">{tool.description}</p>
            </Link>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* 1st: Connected Services */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Connected Services</h3>
            <div className="text-3xl font-bold text-blue-600">2</div>
            <p className="text-sm text-gray-600">Google, Slack</p>
          </div>

          {/* 2nd: Active Integrations */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Active Integrations</h3>
            <div className="text-3xl font-bold text-green-600">0</div>
            <p className="text-sm text-gray-600">Running workflows</p>
          </div>

          {/* 3rd: Last Activity */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Last Activity</h3>
            <div className="text-lg font-medium text-gray-900">Just now</div>
            <p className="text-sm text-gray-600">User login</p>
          </div>

          {/* 4th: Features To Build Box */}
          <div className="bg-yellow-50 rounded-lg shadow-sm border border-yellow-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Features To Build</h3>
              <button
                onClick={() => setShowFeatureForm(true)}
                className="text-yellow-600 hover:text-yellow-700"
              >
                <Plus className="w-4 h-4" />
              </button>
            </div>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {featureList.map((feature, index) => (
                <div key={index} className="flex items-center justify-between group">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={feature.completed}
                      onChange={() => toggleFeature(index)}
                      className="mr-2 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
                    />
                    <span className={`text-sm ${feature.completed ? 'line-through text-gray-500' : 'text-gray-700'}`}>
                      {feature.text}
                    </span>
                  </div>
                  <button
                    onClick={() => removeFeature(index)}
                    className="opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-700 ml-2"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
              {featureList.length === 0 && (
                <p className="text-sm text-gray-500 italic">No features added yet</p>
              )}
            </div>
          </div>
        </div>

        {/* Add Feature Modal */}
        {showFeatureForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Feature</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Feature Description
                  </label>
                  <input
                    type="text"
                    value={newFeatureText}
                    onChange={(e) => setNewFeatureText(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addFeature()}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                    placeholder="e.g., Add dark mode support"
                    autoFocus
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowFeatureForm(false)
                    setNewFeatureText('')
                  }}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={addFeature}
                  disabled={!newFeatureText.trim()}
                  className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors disabled:opacity-50"
                >
                  Add Feature
                </button>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}


