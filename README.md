# API Integration Hub

A secure web application for managing API integrations with Google Services (Calendar, Gmail, Sheets) and Slack. Store credentials securely and automate data workflows between different services.

## Features

### 🔐 Secure Authentication
- Google OAuth integration with NextAuth.js
- Encrypted credential storage using AES-256
- Secure session management

### 🛠️ Integration Tools

#### 1. Calendar to Sheets
- Fetch calendar events from Google Calendar
- Create and update Google Sheets with event data
- Automated spreadsheet formatting

#### 2. Gmail to Sheets
- Search Gmail using advanced operators
- Export email data to spreadsheets
- Bulk email processing

#### 3. Slack to Sheets
- Connect Slack channels to spreadsheets
- Sync channel messages automatically
- Real-time data updates

#### 4. Customer Manager
- Add, edit, and delete customer information
- Search and filter customers
- Secure data storage

## Tech Stack

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **Authentication**: NextAuth.js
- **Database**: Supabase
- **APIs**: Google APIs (Calendar, Gmail, Sheets), Slack Web API
- **Security**: AES-256 encryption for credentials

## Setup Instructions

### 1. Environment Variables

Create a `.env.local` file with the following variables:

```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Slack OAuth
SLACK_CLIENT_ID=your-slack-client-id
SLACK_CLIENT_SECRET=your-slack-client-secret

# Encryption Key for storing credentials
ENCRYPTION_KEY=your-32-character-encryption-key
```

### 2. Google Cloud Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the following APIs:
   - Google Calendar API
   - Gmail API
   - Google Sheets API
4. Create OAuth 2.0 credentials:
   - Application type: Web application
   - Authorized redirect URIs: `http://localhost:3000/api/auth/callback/google`
5. Copy Client ID and Client Secret to your `.env.local`

### 3. Supabase Setup

1. Create a new project at [Supabase](https://supabase.com/)
2. Get your project URL and anon key from Settings > API
3. Get your service role key (keep this secret!)
4. The database tables will be created automatically when you first run the app

### 4. Slack App Setup (Optional)

1. Go to [Slack API](https://api.slack.com/apps)
2. Create a new app
3. Add OAuth scopes:
   - `channels:read`
   - `chat:write`
   - `users:read`
4. Set redirect URL: `http://localhost:3000/api/auth/callback/slack`
5. Copy Client ID and Client Secret to your `.env.local`

### 5. Installation

```bash
# Install dependencies
npm install

# Run the development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## Usage

1. **Sign in** with your Google account
2. **Connect services** in the Settings page
3. **Use the tools**:
   - Calendar to Sheets: Create spreadsheets from calendar events
   - Gmail to Sheets: Export email data to spreadsheets
   - Slack to Sheets: Sync Slack channels to spreadsheets
   - Customer Manager: Manage customer information

## Security Features

- All API credentials are encrypted using AES-256 before storage
- Secure session management with NextAuth.js
- No credentials are exposed in client-side code
- Activity logging for audit trails
- User data isolation

## Development

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
