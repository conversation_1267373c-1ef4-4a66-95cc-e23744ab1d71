/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { addressvalidation_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof addressvalidation_v1.Addressvalidation;
};
export declare function addressvalidation(version: 'v1'): addressvalidation_v1.Addressvalidation;
export declare function addressvalidation(options: addressvalidation_v1.Options): addressvalidation_v1.Addressvalidation;
declare const auth: AuthPlus;
export { auth };
export { addressvalidation_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
