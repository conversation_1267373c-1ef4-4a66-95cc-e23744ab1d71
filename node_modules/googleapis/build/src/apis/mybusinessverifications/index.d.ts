/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { mybusinessverifications_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof mybusinessverifications_v1.Mybusinessverifications;
};
export declare function mybusinessverifications(version: 'v1'): mybusinessverifications_v1.Mybusinessverifications;
export declare function mybusinessverifications(options: mybusinessverifications_v1.Options): mybusinessverifications_v1.Mybusinessverifications;
declare const auth: AuthPlus;
export { auth };
export { mybusinessverifications_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
