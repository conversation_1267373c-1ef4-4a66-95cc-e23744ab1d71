/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { bigquerydatatransfer_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof bigquerydatatransfer_v1.Bigquerydatatransfer;
};
export declare function bigquerydatatransfer(version: 'v1'): bigquerydatatransfer_v1.Bigquerydatatransfer;
export declare function bigquerydatatransfer(options: bigquerydatatransfer_v1.Options): bigquerydatatransfer_v1.Bigquerydatatransfer;
declare const auth: AuthPlus;
export { auth };
export { bigquerydatatransfer_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
