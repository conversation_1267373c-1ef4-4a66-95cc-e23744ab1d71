/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { contactcenteraiplatform_v1alpha1 } from './v1alpha1';
export declare const VERSIONS: {
    v1alpha1: typeof contactcenteraiplatform_v1alpha1.Contactcenteraiplatform;
};
export declare function contactcenteraiplatform(version: 'v1alpha1'): contactcenteraiplatform_v1alpha1.Contactcenteraiplatform;
export declare function contactcenteraiplatform(options: contactcenteraiplatform_v1alpha1.Options): contactcenteraiplatform_v1alpha1.Contactcenteraiplatform;
declare const auth: AuthPlus;
export { auth };
export { contactcenteraiplatform_v1alpha1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
