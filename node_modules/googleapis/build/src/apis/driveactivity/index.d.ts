/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { driveactivity_v2 } from './v2';
export declare const VERSIONS: {
    v2: typeof driveactivity_v2.Driveactivity;
};
export declare function driveactivity(version: 'v2'): driveactivity_v2.Driveactivity;
export declare function driveactivity(options: driveactivity_v2.Options): driveactivity_v2.Driveactivity;
declare const auth: AuthPlus;
export { auth };
export { driveactivity_v2 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
