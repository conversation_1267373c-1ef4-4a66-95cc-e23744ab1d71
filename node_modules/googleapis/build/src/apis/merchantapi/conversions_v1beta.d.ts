/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace merchantapi_conversions_v1beta {
    export interface Options extends GlobalOptions {
        version: 'conversions_v1beta';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Merchant API
     *
     * Programmatically manage your Merchant Center Accounts.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const merchantapi = google.merchantapi('conversions_v1beta');
     * ```
     */
    export class Merchantapi {
        context: APIRequestContext;
        accounts: Resource$Accounts;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Represents attribution settings for conversion sources receiving pre-attribution data.
     */
    export interface Schema$AttributionSettings {
        /**
         * Required. Lookback window (in days) used for attribution in this source. Supported values are `7`, `30` & `40`.
         */
        attributionLookbackWindowDays?: number | null;
        /**
         * Required. Attribution model.
         */
        attributionModel?: string | null;
        /**
         * Immutable. Unordered list. List of different conversion types a conversion event can be classified as. A standard "purchase" type will be automatically created if this list is empty at creation time.
         */
        conversionType?: Schema$ConversionType[];
    }
    /**
     * Represents a conversion source owned by a Merchant account. A merchant account can have up to 200 conversion sources.
     */
    export interface Schema$ConversionSource {
        /**
         * Output only. Controller of the conversion source.
         */
        controller?: string | null;
        /**
         * Output only. The time when an archived conversion source becomes permanently deleted and is no longer available to undelete.
         */
        expireTime?: string | null;
        /**
         * Immutable. Conversion Source of type "Link to Google Analytics Property".
         */
        googleAnalyticsLink?: Schema$GoogleAnalyticsLink;
        /**
         * Conversion Source of type "Merchant Center Tag Destination".
         */
        merchantCenterDestination?: Schema$MerchantCenterDestination;
        /**
         * Output only. Identifier. Generated by the Content API upon creation of a new `ConversionSource`. Format: `[a-z]{4\}:.+` The four characters before the colon represent the type of conversion source. Content after the colon represents the ID of the conversion source within that type. The ID of two different conversion sources might be the same across different types. The following type prefixes are supported: * `galk`: For GoogleAnalyticsLink sources. * `mcdn`: For MerchantCenterDestination sources.
         */
        name?: string | null;
        /**
         * Output only. Current state of this conversion source. Can't be edited through the API.
         */
        state?: string | null;
    }
    /**
     * Message representing the type of a conversion event.
     */
    export interface Schema$ConversionType {
        /**
         * Output only. Conversion event name, as it'll be reported by the client.
         */
        name?: string | null;
        /**
         * Output only. Option indicating if the type should be included in Merchant Center reporting.
         */
        report?: boolean | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * "Google Analytics Link" sources can be used to get conversion data from an existing Google Analytics property into the linked Merchant Center account.
     */
    export interface Schema$GoogleAnalyticsLink {
        /**
         * Output only. Attribution settings for the linked Google Analytics property.
         */
        attributionSettings?: Schema$AttributionSettings;
        /**
         * Output only. Name of the Google Analytics property the merchant is linked to.
         */
        property?: string | null;
        /**
         * Required. Immutable. ID of the Google Analytics property the merchant is linked to.
         */
        propertyId?: string | null;
    }
    /**
     * Response message for the ListConversionSources method.
     */
    export interface Schema$ListConversionSourcesResponse {
        /**
         * List of conversion sources.
         */
        conversionSources?: Schema$ConversionSource[];
        /**
         * Token to be used to fetch the next results page.
         */
        nextPageToken?: string | null;
    }
    /**
     * "Merchant Center Destination" sources can be used to send conversion events from an online store using a Google tag directly to a Merchant Center account where the source is created.
     */
    export interface Schema$MerchantCenterDestination {
        /**
         * Required. Attribution settings used for the Merchant Center Destination.
         */
        attributionSettings?: Schema$AttributionSettings;
        /**
         * Required. Three-letter currency code (ISO 4217). The currency code defines in which currency the conversions sent to this destination will be reported in Merchant Center.
         */
        currencyCode?: string | null;
        /**
         * Output only. Merchant Center Destination ID.
         */
        destination?: string | null;
        /**
         * Required. Merchant-specified display name for the destination. This is the name that identifies the conversion source within the Merchant Center UI. The maximum length is 64 characters.
         */
        displayName?: string | null;
    }
    /**
     * The change that happened to the product including old value, new value, country code as the region code and reporting context.
     */
    export interface Schema$ProductChange {
        /**
         * The new value of the changed resource or attribute. If empty, it means that the product was deleted. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)
         */
        newValue?: string | null;
        /**
         * The old value of the changed resource or attribute. If empty, it means that the product was created. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)
         */
        oldValue?: string | null;
        /**
         * Countries that have the change (if applicable). Represented in the ISO 3166 format.
         */
        regionCode?: string | null;
        /**
         * Reporting contexts that have the change (if applicable). Currently this field supports only (`SHOPPING_ADS`, `LOCAL_INVENTORY_ADS`, `YOUTUBE_SHOPPING`, `YOUTUBE_CHECKOUT`, `YOUTUBE_AFFILIATE`) from the enum value [ReportingContextEnum](/merchant/api/reference/rest/Shared.Types/ReportingContextEnum)
         */
        reportingContext?: string | null;
    }
    /**
     * The message that the merchant will receive to notify about product status change event
     */
    export interface Schema$ProductStatusChangeMessage {
        /**
         * The target account that owns the entity that changed. Format : `accounts/{merchant_id\}`
         */
        account?: string | null;
        /**
         * The attribute in the resource that changed, in this case it will be always `Status`.
         */
        attribute?: string | null;
        /**
         * A message to describe the change that happened to the product
         */
        changes?: Schema$ProductChange[];
        /**
         * The time at which the event was generated. If you want to order the notification messages you receive you should rely on this field not on the order of receiving the notifications.
         */
        eventTime?: string | null;
        /**
         * Optional. The product expiration time. This field will not bet set if the notification is sent for a product deletion event.
         */
        expirationTime?: string | null;
        /**
         * The account that manages the merchant's account. can be the same as merchant id if it is standalone account. Format : `accounts/{service_provider_id\}`
         */
        managingAccount?: string | null;
        /**
         * The product name. Format: `accounts/{account\}/products/{product\}`
         */
        resource?: string | null;
        /**
         * The product id.
         */
        resourceId?: string | null;
        /**
         * The resource that changed, in this case it will always be `Product`.
         */
        resourceType?: string | null;
    }
    /**
     * Request message for the UndeleteConversionSource method.
     */
    export interface Schema$UndeleteConversionSourceRequest {
    }
    export class Resource$Accounts {
        context: APIRequestContext;
        conversionSources: Resource$Accounts$Conversionsources;
        constructor(context: APIRequestContext);
    }
    export class Resource$Accounts$Conversionsources {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new conversion source.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Accounts$Conversionsources$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Accounts$Conversionsources$Create, options?: MethodOptions): GaxiosPromise<Schema$ConversionSource>;
        create(params: Params$Resource$Accounts$Conversionsources$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Accounts$Conversionsources$Create, options: MethodOptions | BodyResponseCallback<Schema$ConversionSource>, callback: BodyResponseCallback<Schema$ConversionSource>): void;
        create(params: Params$Resource$Accounts$Conversionsources$Create, callback: BodyResponseCallback<Schema$ConversionSource>): void;
        create(callback: BodyResponseCallback<Schema$ConversionSource>): void;
        /**
         * Archives an existing conversion source. If the conversion source is a Merchant Center Destination, it will be recoverable for 30 days. If the conversion source is a Google Analytics Link, it will be deleted immediately and can be restored by creating a new one.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Accounts$Conversionsources$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Accounts$Conversionsources$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Accounts$Conversionsources$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Accounts$Conversionsources$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Accounts$Conversionsources$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Fetches a conversion source.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Accounts$Conversionsources$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Accounts$Conversionsources$Get, options?: MethodOptions): GaxiosPromise<Schema$ConversionSource>;
        get(params: Params$Resource$Accounts$Conversionsources$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Accounts$Conversionsources$Get, options: MethodOptions | BodyResponseCallback<Schema$ConversionSource>, callback: BodyResponseCallback<Schema$ConversionSource>): void;
        get(params: Params$Resource$Accounts$Conversionsources$Get, callback: BodyResponseCallback<Schema$ConversionSource>): void;
        get(callback: BodyResponseCallback<Schema$ConversionSource>): void;
        /**
         * Retrieves the list of conversion sources the caller has access to.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Accounts$Conversionsources$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Accounts$Conversionsources$List, options?: MethodOptions): GaxiosPromise<Schema$ListConversionSourcesResponse>;
        list(params: Params$Resource$Accounts$Conversionsources$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Accounts$Conversionsources$List, options: MethodOptions | BodyResponseCallback<Schema$ListConversionSourcesResponse>, callback: BodyResponseCallback<Schema$ListConversionSourcesResponse>): void;
        list(params: Params$Resource$Accounts$Conversionsources$List, callback: BodyResponseCallback<Schema$ListConversionSourcesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListConversionSourcesResponse>): void;
        /**
         * Updates information of an existing conversion source. Available only for Merchant Center Destination conversion sources.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Accounts$Conversionsources$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Accounts$Conversionsources$Patch, options?: MethodOptions): GaxiosPromise<Schema$ConversionSource>;
        patch(params: Params$Resource$Accounts$Conversionsources$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Accounts$Conversionsources$Patch, options: MethodOptions | BodyResponseCallback<Schema$ConversionSource>, callback: BodyResponseCallback<Schema$ConversionSource>): void;
        patch(params: Params$Resource$Accounts$Conversionsources$Patch, callback: BodyResponseCallback<Schema$ConversionSource>): void;
        patch(callback: BodyResponseCallback<Schema$ConversionSource>): void;
        /**
         * Re-enables an archived conversion source. Only Available for Merchant Center Destination conversion sources.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        undelete(params: Params$Resource$Accounts$Conversionsources$Undelete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        undelete(params?: Params$Resource$Accounts$Conversionsources$Undelete, options?: MethodOptions): GaxiosPromise<Schema$ConversionSource>;
        undelete(params: Params$Resource$Accounts$Conversionsources$Undelete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        undelete(params: Params$Resource$Accounts$Conversionsources$Undelete, options: MethodOptions | BodyResponseCallback<Schema$ConversionSource>, callback: BodyResponseCallback<Schema$ConversionSource>): void;
        undelete(params: Params$Resource$Accounts$Conversionsources$Undelete, callback: BodyResponseCallback<Schema$ConversionSource>): void;
        undelete(callback: BodyResponseCallback<Schema$ConversionSource>): void;
    }
    export interface Params$Resource$Accounts$Conversionsources$Create extends StandardParameters {
        /**
         * Required. The merchant account that will own the new conversion source. Format: `accounts/{account\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ConversionSource;
    }
    export interface Params$Resource$Accounts$Conversionsources$Delete extends StandardParameters {
        /**
         * Required. The name of the conversion source to be deleted. Format: `accounts/{account\}/conversionSources/{conversion_source\}`
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$Conversionsources$Get extends StandardParameters {
        /**
         * Required. The name of the conversion source to be fetched. Format: `accounts/{account\}/conversionSources/{conversion_source\}`
         */
        name?: string;
    }
    export interface Params$Resource$Accounts$Conversionsources$List extends StandardParameters {
        /**
         * Optional. The maximum number of conversion sources to return in a page. If no `page_size` is specified, `100` is used as the default value. The maximum value is `200`. Values above `200` will be coerced to `200`. Regardless of pagination, at most `200` conversion sources are returned in total.
         */
        pageSize?: number;
        /**
         * Optional. Page token.
         */
        pageToken?: string;
        /**
         * Required. The merchant account who owns the collection of conversion sources. Format: `accounts/{account\}`
         */
        parent?: string;
        /**
         * Optional. Show deleted (archived) conversion sources. By default, deleted conversion sources are not returned.
         */
        showDeleted?: boolean;
    }
    export interface Params$Resource$Accounts$Conversionsources$Patch extends StandardParameters {
        /**
         * Output only. Identifier. Generated by the Content API upon creation of a new `ConversionSource`. Format: `[a-z]{4\}:.+` The four characters before the colon represent the type of conversion source. Content after the colon represents the ID of the conversion source within that type. The ID of two different conversion sources might be the same across different types. The following type prefixes are supported: * `galk`: For GoogleAnalyticsLink sources. * `mcdn`: For MerchantCenterDestination sources.
         */
        name?: string;
        /**
         * Optional. List of fields being updated.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ConversionSource;
    }
    export interface Params$Resource$Accounts$Conversionsources$Undelete extends StandardParameters {
        /**
         * Required. The name of the conversion source to be undeleted. Format: `accounts/{account\}/conversionSources/{conversion_source\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UndeleteConversionSourceRequest;
    }
    export {};
}
