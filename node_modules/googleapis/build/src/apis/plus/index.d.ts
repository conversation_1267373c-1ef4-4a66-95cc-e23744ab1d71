/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { plus_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof plus_v1.Plus;
};
export declare function plus(version: 'v1'): plus_v1.Plus;
export declare function plus(options: plus_v1.Options): plus_v1.Plus;
declare const auth: AuthPlus;
export { auth };
export { plus_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
