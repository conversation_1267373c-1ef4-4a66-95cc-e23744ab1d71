/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { mybusinessnotifications_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof mybusinessnotifications_v1.Mybusinessnotifications;
};
export declare function mybusinessnotifications(version: 'v1'): mybusinessnotifications_v1.Mybusinessnotifications;
export declare function mybusinessnotifications(options: mybusinessnotifications_v1.Options): mybusinessnotifications_v1.Mybusinessnotifications;
declare const auth: AuthPlus;
export { auth };
export { mybusinessnotifications_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
