/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { manufacturers_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof manufacturers_v1.Manufacturers;
};
export declare function manufacturers(version: 'v1'): manufacturers_v1.Manufacturers;
export declare function manufacturers(options: manufacturers_v1.Options): manufacturers_v1.Manufacturers;
declare const auth: AuthPlus;
export { auth };
export { manufacturers_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
