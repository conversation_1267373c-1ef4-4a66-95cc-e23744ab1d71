/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace texttospeech_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Cloud Text-to-Speech API
     *
     * Synthesizes natural-sounding speech by applying powerful neural network models.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const texttospeech = google.texttospeech('v1');
     * ```
     */
    export class Texttospeech {
        context: APIRequestContext;
        operations: Resource$Operations;
        projects: Resource$Projects;
        text: Resource$Text;
        voices: Resource$Voices;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Used for advanced voice options.
     */
    export interface Schema$AdvancedVoiceOptions {
        /**
         * Only for Journey voices. If false, the synthesis is context aware and has a higher latency.
         */
        lowLatencyJourneySynthesis?: boolean | null;
    }
    /**
     * Description of audio data to be synthesized.
     */
    export interface Schema$AudioConfig {
        /**
         * Required. The format of the audio byte stream.
         */
        audioEncoding?: string | null;
        /**
         * Optional. Input only. An identifier which selects 'audio effects' profiles that are applied on (post synthesized) text to speech. Effects are applied on top of each other in the order they are given. See [audio profiles](https://cloud.google.com/text-to-speech/docs/audio-profiles) for current supported profile ids.
         */
        effectsProfileId?: string[] | null;
        /**
         * Optional. Input only. Speaking pitch, in the range [-20.0, 20.0]. 20 means increase 20 semitones from the original pitch. -20 means decrease 20 semitones from the original pitch.
         */
        pitch?: number | null;
        /**
         * Optional. The synthesis sample rate (in hertz) for this audio. When this is specified in SynthesizeSpeechRequest, if this is different from the voice's natural sample rate, then the synthesizer will honor this request by converting to the desired sample rate (which might result in worse audio quality), unless the specified sample rate is not supported for the encoding chosen, in which case it will fail the request and return google.rpc.Code.INVALID_ARGUMENT.
         */
        sampleRateHertz?: number | null;
        /**
         * Optional. Input only. Speaking rate/speed, in the range [0.25, 2.0]. 1.0 is the normal native speed supported by the specific voice. 2.0 is twice as fast, and 0.5 is half as fast. If unset(0.0), defaults to the native 1.0 speed. Any other values < 0.25 or \> 2.0 will return an error.
         */
        speakingRate?: number | null;
        /**
         * Optional. Input only. Volume gain (in dB) of the normal native volume supported by the specific voice, in the range [-96.0, 16.0]. If unset, or set to a value of 0.0 (dB), will play at normal native signal amplitude. A value of -6.0 (dB) will play at approximately half the amplitude of the normal native signal amplitude. A value of **** (dB) will play at approximately twice the amplitude of the normal native signal amplitude. Strongly recommend not to exceed +10 (dB) as there's usually no effective increase in loudness for any value greater than that.
         */
        volumeGainDb?: number | null;
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$CancelOperationRequest {
    }
    /**
     * Pronunciation customization for a phrase.
     */
    export interface Schema$CustomPronunciationParams {
        /**
         * The phonetic encoding of the phrase.
         */
        phoneticEncoding?: string | null;
        /**
         * The phrase to which the customization is applied. The phrase can be multiple words, such as proper nouns, but shouldn't span the length of the sentence.
         */
        phrase?: string | null;
        /**
         * The pronunciation of the phrase. This must be in the phonetic encoding specified above.
         */
        pronunciation?: string | null;
    }
    /**
     * A collection of pronunciation customizations.
     */
    export interface Schema$CustomPronunciations {
        /**
         * The pronunciation customizations are applied.
         */
        pronunciations?: Schema$CustomPronunciationParams[];
    }
    /**
     * Description of the custom voice to be synthesized.
     */
    export interface Schema$CustomVoiceParams {
        /**
         * Required. The name of the AutoML model that synthesizes the custom voice.
         */
        model?: string | null;
        /**
         * Optional. Deprecated. The usage of the synthesized audio to be reported.
         */
        reportedUsage?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Metadata for response returned by the `SynthesizeLongAudio` method.
     */
    export interface Schema$GoogleCloudTexttospeechV1SynthesizeLongAudioMetadata {
        /**
         * Deprecated. Do not use.
         */
        lastUpdateTime?: string | null;
        /**
         * The progress of the most recent processing update in percentage, ie. 70.0%.
         */
        progressPercentage?: number | null;
        /**
         * Time when the request was received.
         */
        startTime?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * The message returned to the client by the `ListVoices` method.
     */
    export interface Schema$ListVoicesResponse {
        /**
         * The list of voices.
         */
        voices?: Schema$Voice[];
    }
    /**
     * A collection of turns for multi-speaker synthesis.
     */
    export interface Schema$MultiSpeakerMarkup {
        /**
         * Required. Speaker turns.
         */
        turns?: Schema$Turn[];
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Contains text input to be synthesized. Either `text` or `ssml` must be supplied. Supplying both or neither returns google.rpc.Code.INVALID_ARGUMENT. The input size is limited to 5000 bytes.
     */
    export interface Schema$SynthesisInput {
        /**
         * Optional. The pronunciation customizations are applied to the input. If this is set, the input is synthesized using the given pronunciation customizations. The initial support is for en-us, with plans to expand to other locales in the future. Instant Clone voices aren't supported. In order to customize the pronunciation of a phrase, there must be an exact match of the phrase in the input types. If using SSML, the phrase must not be inside a phoneme tag.
         */
        customPronunciations?: Schema$CustomPronunciations;
        /**
         * The multi-speaker input to be synthesized. Only applicable for multi-speaker synthesis.
         */
        multiSpeakerMarkup?: Schema$MultiSpeakerMarkup;
        /**
         * The SSML document to be synthesized. The SSML document must be valid and well-formed. Otherwise the RPC will fail and return google.rpc.Code.INVALID_ARGUMENT. For more information, see [SSML](https://cloud.google.com/text-to-speech/docs/ssml).
         */
        ssml?: string | null;
        /**
         * The raw text to be synthesized.
         */
        text?: string | null;
    }
    /**
     * Metadata for response returned by the `SynthesizeLongAudio` method.
     */
    export interface Schema$SynthesizeLongAudioMetadata {
        /**
         * Deprecated. Do not use.
         */
        lastUpdateTime?: string | null;
        /**
         * The progress of the most recent processing update in percentage, ie. 70.0%.
         */
        progressPercentage?: number | null;
        /**
         * Time when the request was received.
         */
        startTime?: string | null;
    }
    /**
     * The top-level message sent by the client for the `SynthesizeLongAudio` method.
     */
    export interface Schema$SynthesizeLongAudioRequest {
        /**
         * Required. The configuration of the synthesized audio.
         */
        audioConfig?: Schema$AudioConfig;
        /**
         * Required. The Synthesizer requires either plain text or SSML as input.
         */
        input?: Schema$SynthesisInput;
        /**
         * Required. Specifies a Cloud Storage URI for the synthesis results. Must be specified in the format: `gs://bucket_name/object_name`, and the bucket must already exist.
         */
        outputGcsUri?: string | null;
        /**
         * Required. The desired voice of the synthesized audio.
         */
        voice?: Schema$VoiceSelectionParams;
    }
    /**
     * The top-level message sent by the client for the `SynthesizeSpeech` method.
     */
    export interface Schema$SynthesizeSpeechRequest {
        /**
         * Advanced voice options.
         */
        advancedVoiceOptions?: Schema$AdvancedVoiceOptions;
        /**
         * Required. The configuration of the synthesized audio.
         */
        audioConfig?: Schema$AudioConfig;
        /**
         * Required. The Synthesizer requires either plain text or SSML as input.
         */
        input?: Schema$SynthesisInput;
        /**
         * Required. The desired voice of the synthesized audio.
         */
        voice?: Schema$VoiceSelectionParams;
    }
    /**
     * The message returned to the client by the `SynthesizeSpeech` method.
     */
    export interface Schema$SynthesizeSpeechResponse {
        /**
         * The audio data bytes encoded as specified in the request, including the header for encodings that are wrapped in containers (e.g. MP3, OGG_OPUS). For LINEAR16 audio, we include the WAV header. Note: as with all bytes fields, protobuffers use a pure binary representation, whereas JSON representations use base64.
         */
        audioContent?: string | null;
    }
    /**
     * A multi-speaker turn.
     */
    export interface Schema$Turn {
        /**
         * Required. The speaker of the turn, for example, 'O' or 'Q'. Please refer to documentation for available speakers.
         */
        speaker?: string | null;
        /**
         * Required. The text to speak.
         */
        text?: string | null;
    }
    /**
     * Description of a voice supported by the TTS service.
     */
    export interface Schema$Voice {
        /**
         * The languages that this voice supports, expressed as [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt) language tags (e.g. "en-US", "es-419", "cmn-tw").
         */
        languageCodes?: string[] | null;
        /**
         * The name of this voice. Each distinct voice has a unique name.
         */
        name?: string | null;
        /**
         * The natural sample rate (in hertz) for this voice.
         */
        naturalSampleRateHertz?: number | null;
        /**
         * The gender of this voice.
         */
        ssmlGender?: string | null;
    }
    /**
     * The configuration of Voice Clone feature.
     */
    export interface Schema$VoiceCloneParams {
        /**
         * Required. Created by GenerateVoiceCloningKey.
         */
        voiceCloningKey?: string | null;
    }
    /**
     * Description of which voice to use for a synthesis request.
     */
    export interface Schema$VoiceSelectionParams {
        /**
         * The configuration for a custom voice. If [CustomVoiceParams.model] is set, the service will choose the custom voice matching the specified configuration.
         */
        customVoice?: Schema$CustomVoiceParams;
        /**
         * Required. The language (and potentially also the region) of the voice expressed as a [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt) language tag, e.g. "en-US". This should not include a script tag (e.g. use "cmn-cn" rather than "cmn-Hant-cn"), because the script will be inferred from the input provided in the SynthesisInput. The TTS service will use this parameter to help choose an appropriate voice. Note that the TTS service may choose a voice with a slightly different language code than the one selected; it may substitute a different region (e.g. using en-US rather than en-CA if there isn't a Canadian voice available), or even a different language, e.g. using "nb" (Norwegian Bokmal) instead of "no" (Norwegian)".
         */
        languageCode?: string | null;
        /**
         * The name of the voice. If both the name and the gender are not set, the service will choose a voice based on the other parameters such as language_code.
         */
        name?: string | null;
        /**
         * The preferred gender of the voice. If not set, the service will choose a voice based on the other parameters such as language_code and name. Note that this is only a preference, not requirement; if a voice of the appropriate gender is not available, the synthesizer should substitute a voice with a different gender rather than failing the request.
         */
        ssmlGender?: string | null;
        /**
         * Optional. The configuration for a voice clone. If [VoiceCloneParams.voice_clone_key] is set, the service chooses the voice clone matching the specified configuration.
         */
        voiceClone?: Schema$VoiceCloneParams;
    }
    export class Resource$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Operations$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Operations$Cancel, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        cancel(params: Params$Resource$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Operations$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Operations$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
    }
    export interface Params$Resource$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelOperationRequest;
    }
    export interface Params$Resource$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        operations: Resource$Projects$Locations$Operations;
        constructor(context: APIRequestContext);
        /**
         * Synthesizes long form text asynchronously.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        synthesizeLongAudio(params: Params$Resource$Projects$Locations$Synthesizelongaudio, options: StreamMethodOptions): GaxiosPromise<Readable>;
        synthesizeLongAudio(params?: Params$Resource$Projects$Locations$Synthesizelongaudio, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        synthesizeLongAudio(params: Params$Resource$Projects$Locations$Synthesizelongaudio, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        synthesizeLongAudio(params: Params$Resource$Projects$Locations$Synthesizelongaudio, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        synthesizeLongAudio(params: Params$Resource$Projects$Locations$Synthesizelongaudio, callback: BodyResponseCallback<Schema$Operation>): void;
        synthesizeLongAudio(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Synthesizelongaudio extends StandardParameters {
        /**
         * The resource states of the request in the form of `projects/x/locations/x`.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SynthesizeLongAudioRequest;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$ListOperationsResponse>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Text {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Synthesizes speech synchronously: receive results after all text input has been processed.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        synthesize(params: Params$Resource$Text$Synthesize, options: StreamMethodOptions): GaxiosPromise<Readable>;
        synthesize(params?: Params$Resource$Text$Synthesize, options?: MethodOptions): GaxiosPromise<Schema$SynthesizeSpeechResponse>;
        synthesize(params: Params$Resource$Text$Synthesize, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        synthesize(params: Params$Resource$Text$Synthesize, options: MethodOptions | BodyResponseCallback<Schema$SynthesizeSpeechResponse>, callback: BodyResponseCallback<Schema$SynthesizeSpeechResponse>): void;
        synthesize(params: Params$Resource$Text$Synthesize, callback: BodyResponseCallback<Schema$SynthesizeSpeechResponse>): void;
        synthesize(callback: BodyResponseCallback<Schema$SynthesizeSpeechResponse>): void;
    }
    export interface Params$Resource$Text$Synthesize extends StandardParameters {
        /**
         * Request body metadata
         */
        requestBody?: Schema$SynthesizeSpeechRequest;
    }
    export class Resource$Voices {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Returns a list of Voice supported for synthesis.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Voices$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Voices$List, options?: MethodOptions): GaxiosPromise<Schema$ListVoicesResponse>;
        list(params: Params$Resource$Voices$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Voices$List, options: MethodOptions | BodyResponseCallback<Schema$ListVoicesResponse>, callback: BodyResponseCallback<Schema$ListVoicesResponse>): void;
        list(params: Params$Resource$Voices$List, callback: BodyResponseCallback<Schema$ListVoicesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListVoicesResponse>): void;
    }
    export interface Params$Resource$Voices$List extends StandardParameters {
        /**
         * Optional. Recommended. [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt) language tag. If not specified, the API will return all supported voices. If specified, the ListVoices call will only return voices that can be used to synthesize this language_code. For example, if you specify `"en-NZ"`, all `"en-NZ"` voices will be returned. If you specify `"no"`, both `"no-\*"` (Norwegian) and `"nb-\*"` (Norwegian Bokmal) voices will be returned.
         */
        languageCode?: string;
    }
    export {};
}
