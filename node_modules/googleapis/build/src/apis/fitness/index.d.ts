/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { fitness_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof fitness_v1.Fitness;
};
export declare function fitness(version: 'v1'): fitness_v1.Fitness;
export declare function fitness(options: fitness_v1.Options): fitness_v1.Fitness;
declare const auth: AuthPlus;
export { auth };
export { fitness_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
