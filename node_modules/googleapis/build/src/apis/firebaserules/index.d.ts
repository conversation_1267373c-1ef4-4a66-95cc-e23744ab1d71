/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { firebaserules_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof firebaserules_v1.Firebaserules;
};
export declare function firebaserules(version: 'v1'): firebaserules_v1.Firebaserules;
export declare function firebaserules(options: firebaserules_v1.Options): firebaserules_v1.Firebaserules;
declare const auth: AuthPlus;
export { auth };
export { firebaserules_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
