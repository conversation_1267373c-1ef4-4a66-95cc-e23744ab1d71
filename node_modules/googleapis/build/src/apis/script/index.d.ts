/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { script_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof script_v1.Script;
};
export declare function script(version: 'v1'): script_v1.Script;
export declare function script(options: script_v1.Options): script_v1.Script;
declare const auth: AuthPlus;
export { auth };
export { script_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
