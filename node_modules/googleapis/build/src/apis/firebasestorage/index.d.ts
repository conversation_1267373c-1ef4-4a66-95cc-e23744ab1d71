/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { firebasestorage_v1beta } from './v1beta';
export declare const VERSIONS: {
    v1beta: typeof firebasestorage_v1beta.Firebasestorage;
};
export declare function firebasestorage(version: 'v1beta'): firebasestorage_v1beta.Firebasestorage;
export declare function firebasestorage(options: firebasestorage_v1beta.Options): firebasestorage_v1beta.Firebasestorage;
declare const auth: AuthPlus;
export { auth };
export { firebasestorage_v1beta };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
