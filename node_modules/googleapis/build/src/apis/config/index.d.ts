/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { config_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof config_v1.Config;
};
export declare function config(version: 'v1'): config_v1.Config;
export declare function config(options: config_v1.Options): config_v1.Config;
declare const auth: AuthPlus;
export { auth };
export { config_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
