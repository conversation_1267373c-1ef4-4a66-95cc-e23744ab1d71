/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { mybusinessqanda_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof mybusinessqanda_v1.Mybusinessqanda;
};
export declare function mybusinessqanda(version: 'v1'): mybusinessqanda_v1.Mybusinessqanda;
export declare function mybusinessqanda(options: mybusinessqanda_v1.Options): mybusinessqanda_v1.Mybusinessqanda;
declare const auth: AuthPlus;
export { auth };
export { mybusinessqanda_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
