/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { reseller_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof reseller_v1.Reseller;
};
export declare function reseller(version: 'v1'): reseller_v1.Reseller;
export declare function reseller(options: reseller_v1.Options): reseller_v1.Reseller;
declare const auth: AuthPlus;
export { auth };
export { reseller_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
