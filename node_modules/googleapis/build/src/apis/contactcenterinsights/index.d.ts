/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { contactcenterinsights_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof contactcenterinsights_v1.Contactcenterinsights;
};
export declare function contactcenterinsights(version: 'v1'): contactcenterinsights_v1.Contactcenterinsights;
export declare function contactcenterinsights(options: contactcenterinsights_v1.Options): contactcenterinsights_v1.Contactcenterinsights;
declare const auth: AuthPlus;
export { auth };
export { contactcenterinsights_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
