/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { marketingplatformadmin_v1alpha } from './v1alpha';
export declare const VERSIONS: {
    v1alpha: typeof marketingplatformadmin_v1alpha.Marketingplatformadmin;
};
export declare function marketingplatformadmin(version: 'v1alpha'): marketingplatformadmin_v1alpha.Marketingplatformadmin;
export declare function marketingplatformadmin(options: marketingplatformadmin_v1alpha.Options): marketingplatformadmin_v1alpha.Marketingplatformadmin;
declare const auth: AuthPlus;
export { auth };
export { marketingplatformadmin_v1alpha };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
