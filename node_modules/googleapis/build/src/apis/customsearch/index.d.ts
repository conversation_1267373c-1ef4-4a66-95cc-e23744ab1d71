/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { customsearch_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof customsearch_v1.Customsearch;
};
export declare function customsearch(version: 'v1'): customsearch_v1.Customsearch;
export declare function customsearch(options: customsearch_v1.Options): customsearch_v1.Customsearch;
declare const auth: AuthPlus;
export { auth };
export { customsearch_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
