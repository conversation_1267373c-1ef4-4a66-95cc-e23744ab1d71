/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { checks_v1alpha } from './v1alpha';
export declare const VERSIONS: {
    v1alpha: typeof checks_v1alpha.Checks;
};
export declare function checks(version: 'v1alpha'): checks_v1alpha.Checks;
export declare function checks(options: checks_v1alpha.Options): checks_v1alpha.Checks;
declare const auth: AuthPlus;
export { auth };
export { checks_v1alpha };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
