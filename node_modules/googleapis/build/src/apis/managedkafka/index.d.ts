/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { managedkafka_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof managedkafka_v1.Managedkafka;
};
export declare function managedkafka(version: 'v1'): managedkafka_v1.Managedkafka;
export declare function managedkafka(options: managedkafka_v1.Options): managedkafka_v1.Managedkafka;
declare const auth: AuthPlus;
export { auth };
export { managedkafka_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
