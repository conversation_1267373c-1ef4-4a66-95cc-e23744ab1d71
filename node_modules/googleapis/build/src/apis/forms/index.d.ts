/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { forms_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof forms_v1.Forms;
};
export declare function forms(version: 'v1'): forms_v1.Forms;
export declare function forms(options: forms_v1.Options): forms_v1.Forms;
declare const auth: AuthPlus;
export { auth };
export { forms_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
