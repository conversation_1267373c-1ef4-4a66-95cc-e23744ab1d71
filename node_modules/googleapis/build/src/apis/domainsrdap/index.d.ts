/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { domainsrdap_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof domainsrdap_v1.Domainsrdap;
};
export declare function domainsrdap(version: 'v1'): domainsrdap_v1.Domainsrdap;
export declare function domainsrdap(options: domainsrdap_v1.Options): domainsrdap_v1.Domainsrdap;
declare const auth: AuthPlus;
export { auth };
export { domainsrdap_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
