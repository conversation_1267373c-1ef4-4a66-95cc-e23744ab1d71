/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { mybusinesslodging_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof mybusinesslodging_v1.Mybusinesslodging;
};
export declare function mybusinesslodging(version: 'v1'): mybusinesslodging_v1.Mybusinesslodging;
export declare function mybusinesslodging(options: mybusinesslodging_v1.Options): mybusinesslodging_v1.Mybusinesslodging;
declare const auth: AuthPlus;
export { auth };
export { mybusinesslodging_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
