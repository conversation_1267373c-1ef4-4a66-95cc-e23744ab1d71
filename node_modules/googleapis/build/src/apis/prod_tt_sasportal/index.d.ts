/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { prod_tt_sasportal_v1alpha1 } from './v1alpha1';
export declare const VERSIONS: {
    v1alpha1: typeof prod_tt_sasportal_v1alpha1.Prod_tt_sasportal;
};
export declare function prod_tt_sasportal(version: 'v1alpha1'): prod_tt_sasportal_v1alpha1.Prod_tt_sasportal;
export declare function prod_tt_sasportal(options: prod_tt_sasportal_v1alpha1.Options): prod_tt_sasportal_v1alpha1.Prod_tt_sasportal;
declare const auth: AuthPlus;
export { auth };
export { prod_tt_sasportal_v1alpha1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
