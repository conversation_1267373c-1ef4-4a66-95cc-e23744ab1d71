/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { webfonts_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof webfonts_v1.Webfonts;
};
export declare function webfonts(version: 'v1'): webfonts_v1.Webfonts;
export declare function webfonts(options: webfonts_v1.Options): webfonts_v1.Webfonts;
declare const auth: AuthPlus;
export { auth };
export { webfonts_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
