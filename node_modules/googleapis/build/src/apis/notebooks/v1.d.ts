/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace notebooks_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Notebooks API
     *
     * Notebooks API is used to manage notebook resources in Google Cloud.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const notebooks = google.notebooks('v1');
     * ```
     */
    export class Notebooks {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Definition of a hardware accelerator. Note that not all combinations of `type` and `core_count` are valid. See [GPUs on Compute Engine](https://cloud.google.com/compute/docs/gpus/#gpus-list) to find a valid combination. TPUs are not supported.
     */
    export interface Schema$AcceleratorConfig {
        /**
         * Count of cores of this accelerator.
         */
        coreCount?: string | null;
        /**
         * Type of this accelerator.
         */
        type?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$Expr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/group/{group_id\}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/x`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/group/{group_id\}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/x`: All identities in a workload identity pool. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).
         */
        role?: string | null;
    }
    /**
     * Definition of the boot image used by the Runtime. Used to facilitate runtime upgradeability.
     */
    export interface Schema$BootImage {
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$CancelOperationRequest {
    }
    /**
     * Definition of a container image for starting a notebook instance with the environment installed in a container.
     */
    export interface Schema$ContainerImage {
        /**
         * Required. The path to the container image repository. For example: `gcr.io/{project_id\}/{image_name\}`
         */
        repository?: string | null;
        /**
         * The tag of the container image. If not specified, this defaults to the latest tag.
         */
        tag?: string | null;
    }
    /**
     * Parameters used in Dataproc JobType executions.
     */
    export interface Schema$DataprocParameters {
        /**
         * URI for cluster used to run Dataproc execution. Format: `projects/{PROJECT_ID\}/regions/{REGION\}/clusters/{CLUSTER_NAME\}`
         */
        cluster?: string | null;
    }
    /**
     * Request for creating a notebook instance diagnostic file.
     */
    export interface Schema$DiagnoseInstanceRequest {
        /**
         * Required. Defines flags that are used to run the diagnostic tool
         */
        diagnosticConfig?: Schema$DiagnosticConfig;
        /**
         * Optional. Maximum amount of time in minutes before the operation times out.
         */
        timeoutMinutes?: number | null;
    }
    /**
     * Request for creating a notebook instance diagnostic file.
     */
    export interface Schema$DiagnoseRuntimeRequest {
        /**
         * Required. Defines flags that are used to run the diagnostic tool
         */
        diagnosticConfig?: Schema$DiagnosticConfig;
        /**
         * Optional. Maximum amount of time in minutes before the operation times out.
         */
        timeoutMinutes?: number | null;
    }
    /**
     * Defines flags that are used to run the diagnostic tool
     */
    export interface Schema$DiagnosticConfig {
        /**
         * Optional. Enables flag to copy all `/home/<USER>
         */
        copyHomeFilesFlagEnabled?: boolean | null;
        /**
         * Required. User Cloud Storage bucket location (REQUIRED). Must be formatted with path prefix (`gs://$GCS_BUCKET`). Permissions: User Managed Notebooks: - storage.buckets.writer: Must be given to the project's service account attached to VM. Google Managed Notebooks: - storage.buckets.writer: Must be given to the project's service account or user credentials attached to VM depending on authentication mode. Cloud Storage bucket Log file will be written to `gs://$GCS_BUCKET/$RELATIVE_PATH/$VM_DATE_$TIME.tar.gz`
         */
        gcsBucket?: string | null;
        /**
         * Optional. Enables flag to capture packets from the instance for 30 seconds
         */
        packetCaptureFlagEnabled?: boolean | null;
        /**
         * Optional. Defines the relative storage path in the Cloud Storage bucket where the diagnostic logs will be written: Default path will be the root directory of the Cloud Storage bucket (`gs://$GCS_BUCKET/$DATE_$TIME.tar.gz`) Example of full path where Log file will be written: `gs://$GCS_BUCKET/$RELATIVE_PATH/`
         */
        relativePath?: string | null;
        /**
         * Optional. Enables flag to repair service for instance
         */
        repairFlagEnabled?: boolean | null;
    }
    /**
     * An instance-attached disk resource.
     */
    export interface Schema$Disk {
        /**
         * Indicates whether the disk will be auto-deleted when the instance is deleted (but not when the disk is detached from the instance).
         */
        autoDelete?: boolean | null;
        /**
         * Indicates that this is a boot disk. The virtual machine will use the first partition of the disk for its root filesystem.
         */
        boot?: boolean | null;
        /**
         * Indicates a unique device name of your choice that is reflected into the `/dev/disk/by-id/google-*` tree of a Linux operating system running within the instance. This name can be used to reference the device for mounting, resizing, and so on, from within the instance. If not specified, the server chooses a default device name to apply to this disk, in the form persistent-disk-x, where x is a number assigned by Google Compute Engine.This field is only applicable for persistent disks.
         */
        deviceName?: string | null;
        /**
         * Indicates the size of the disk in base-2 GB.
         */
        diskSizeGb?: string | null;
        /**
         * Indicates a list of features to enable on the guest operating system. Applicable only for bootable images. Read Enabling guest operating system features to see a list of available options.
         */
        guestOsFeatures?: Schema$GuestOsFeature[];
        /**
         * A zero-based index to this disk, where 0 is reserved for the boot disk. If you have many disks attached to an instance, each disk would have a unique index number.
         */
        index?: string | null;
        /**
         * Indicates the disk interface to use for attaching this disk, which is either SCSI or NVME. The default is SCSI. Persistent disks must always use SCSI and the request will fail if you attempt to attach a persistent disk in any other format than SCSI. Local SSDs can use either NVME or SCSI. For performance characteristics of SCSI over NVMe, see Local SSD performance. Valid values: * `NVME` * `SCSI`
         */
        interface?: string | null;
        /**
         * Type of the resource. Always compute#attachedDisk for attached disks.
         */
        kind?: string | null;
        /**
         * A list of publicly visible licenses. Reserved for Google's use. A License represents billing and aggregate usage data for public and marketplace images.
         */
        licenses?: string[] | null;
        /**
         * The mode in which to attach this disk, either `READ_WRITE` or `READ_ONLY`. If not specified, the default is to attach the disk in `READ_WRITE` mode. Valid values: * `READ_ONLY` * `READ_WRITE`
         */
        mode?: string | null;
        /**
         * Indicates a valid partial or full URL to an existing Persistent Disk resource.
         */
        source?: string | null;
        /**
         * Indicates the type of the disk, either `SCRATCH` or `PERSISTENT`. Valid values: * `PERSISTENT` * `SCRATCH`
         */
        type?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Represents a custom encryption key configuration that can be applied to a resource. This will encrypt all disks in Virtual Machine.
     */
    export interface Schema$EncryptionConfig {
        /**
         * The Cloud KMS resource identifier of the customer-managed encryption key used to protect a resource, such as a disks. It has the following format: `projects/{PROJECT_ID\}/locations/{REGION\}/keyRings/{KEY_RING_NAME\}/cryptoKeys/{KEY_NAME\}`
         */
        kmsKey?: string | null;
    }
    /**
     * Definition of a software environment that is used to start a notebook instance.
     */
    export interface Schema$Environment {
        /**
         * Use a container image to start the notebook instance.
         */
        containerImage?: Schema$ContainerImage;
        /**
         * Output only. The time at which this environment was created.
         */
        createTime?: string | null;
        /**
         * A brief description of this environment.
         */
        description?: string | null;
        /**
         * Display name of this environment for the UI.
         */
        displayName?: string | null;
        /**
         * Output only. Name of this environment. Format: `projects/{project_id\}/locations/{location\}/environments/{environment_id\}`
         */
        name?: string | null;
        /**
         * Path to a Bash script that automatically runs after a notebook instance fully boots up. The path must be a URL or Cloud Storage path. Example: `"gs://path-to-file/file-name"`
         */
        postStartupScript?: string | null;
        /**
         * Use a Compute Engine VM image to start the notebook instance.
         */
        vmImage?: Schema$VmImage;
    }
    /**
     * The definition of an Event for a managed / semi-managed notebook instance.
     */
    export interface Schema$Event {
        /**
         * Optional. Event details. This field is used to pass event information.
         */
        details?: {
            [key: string]: string;
        } | null;
        /**
         * Event report time.
         */
        reportTime?: string | null;
        /**
         * Event type.
         */
        type?: string | null;
    }
    /**
     * The definition of a single executed notebook.
     */
    export interface Schema$Execution {
        /**
         * Output only. Time the Execution was instantiated.
         */
        createTime?: string | null;
        /**
         * A brief description of this execution.
         */
        description?: string | null;
        /**
         * Output only. Name used for UI purposes. Name can only contain alphanumeric characters and underscores '_'.
         */
        displayName?: string | null;
        /**
         * execute metadata including name, hardware spec, region, labels, etc.
         */
        executionTemplate?: Schema$ExecutionTemplate;
        /**
         * Output only. The URI of the external job used to execute the notebook.
         */
        jobUri?: string | null;
        /**
         * Output only. The resource name of the execute. Format: `projects/{project_id\}/locations/{location\}/executions/{execution_id\}`
         */
        name?: string | null;
        /**
         * Output notebook file generated by this execution
         */
        outputNotebookFile?: string | null;
        /**
         * Output only. State of the underlying AI Platform job.
         */
        state?: string | null;
        /**
         * Output only. Time the Execution was last updated.
         */
        updateTime?: string | null;
    }
    /**
     * The description a notebook execution workload.
     */
    export interface Schema$ExecutionTemplate {
        /**
         * Configuration (count and accelerator type) for hardware running notebook execution.
         */
        acceleratorConfig?: Schema$SchedulerAcceleratorConfig;
        /**
         * Container Image URI to a DLVM Example: 'gcr.io/deeplearning-platform-release/base-cu100' More examples can be found at: https://cloud.google.com/ai-platform/deep-learning-containers/docs/choosing-container
         */
        containerImageUri?: string | null;
        /**
         * Parameters used in Dataproc JobType executions.
         */
        dataprocParameters?: Schema$DataprocParameters;
        /**
         * Path to the notebook file to execute. Must be in a Google Cloud Storage bucket. Format: `gs://{bucket_name\}/{folder\}/{notebook_file_name\}` Ex: `gs://notebook_user/scheduled_notebooks/sentiment_notebook.ipynb`
         */
        inputNotebookFile?: string | null;
        /**
         * The type of Job to be used on this execution.
         */
        jobType?: string | null;
        /**
         * Name of the kernel spec to use. This must be specified if the kernel spec name on the execution target does not match the name in the input notebook file.
         */
        kernelSpec?: string | null;
        /**
         * Labels for execution. If execution is scheduled, a field included will be 'nbs-scheduled'. Otherwise, it is an immediate execution, and an included field will be 'nbs-immediate'. Use fields to efficiently index between various types of executions.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Specifies the type of virtual machine to use for your training job's master worker. You must specify this field when `scaleTier` is set to `CUSTOM`. You can use certain Compute Engine machine types directly in this field. The following types are supported: - `n1-standard-4` - `n1-standard-8` - `n1-standard-16` - `n1-standard-32` - `n1-standard-64` - `n1-standard-96` - `n1-highmem-2` - `n1-highmem-4` - `n1-highmem-8` - `n1-highmem-16` - `n1-highmem-32` - `n1-highmem-64` - `n1-highmem-96` - `n1-highcpu-16` - `n1-highcpu-32` - `n1-highcpu-64` - `n1-highcpu-96` Alternatively, you can use the following legacy machine types: - `standard` - `large_model` - `complex_model_s` - `complex_model_m` - `complex_model_l` - `standard_gpu` - `complex_model_m_gpu` - `complex_model_l_gpu` - `standard_p100` - `complex_model_m_p100` - `standard_v100` - `large_model_v100` - `complex_model_m_v100` - `complex_model_l_v100` Finally, if you want to use a TPU for training, specify `cloud_tpu` in this field. Learn more about the [special configuration options for training with TPU](https://cloud.google.com/ai-platform/training/docs/using-tpus#configuring_a_custom_tpu_machine).
         */
        masterType?: string | null;
        /**
         * Path to the notebook folder to write to. Must be in a Google Cloud Storage bucket path. Format: `gs://{bucket_name\}/{folder\}` Ex: `gs://notebook_user/scheduled_notebooks`
         */
        outputNotebookFolder?: string | null;
        /**
         * Parameters used within the 'input_notebook_file' notebook.
         */
        parameters?: string | null;
        /**
         * Parameters to be overridden in the notebook during execution. Ref https://papermill.readthedocs.io/en/latest/usage-parameterize.html on how to specifying parameters in the input notebook and pass them here in an YAML file. Ex: `gs://notebook_user/scheduled_notebooks/sentiment_notebook_params.yaml`
         */
        paramsYamlFile?: string | null;
        /**
         * Required. Scale tier of the hardware used for notebook execution. DEPRECATED Will be discontinued. As right now only CUSTOM is supported.
         */
        scaleTier?: string | null;
        /**
         * The email address of a service account to use when running the execution. You must have the `iam.serviceAccounts.actAs` permission for the specified service account.
         */
        serviceAccount?: string | null;
        /**
         * The name of a Vertex AI [Tensorboard] resource to which this execution will upload Tensorboard logs. Format: `projects/{project\}/locations/{location\}/tensorboards/{tensorboard\}`
         */
        tensorboard?: string | null;
        /**
         * Parameters used in Vertex AI JobType executions.
         */
        vertexAiParameters?: Schema$VertexAIParameters;
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$Expr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    /**
     * Response for checking if a notebook instance is healthy.
     */
    export interface Schema$GetInstanceHealthResponse {
        /**
         * Output only. Additional information about instance health. Example: healthInfo": { "docker_proxy_agent_status": "1", "docker_status": "1", "jupyterlab_api_status": "-1", "jupyterlab_status": "-1", "updated": "2020-10-18 09:40:03.573409" \}
         */
        healthInfo?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Runtime health_state.
         */
        healthState?: string | null;
    }
    /**
     * Guest OS features for boot disk.
     */
    export interface Schema$GuestOsFeature {
        /**
         * The ID of a supported feature. Read Enabling guest operating system features to see a list of available options. Valid values: * `FEATURE_TYPE_UNSPECIFIED` * `MULTI_IP_SUBNET` * `SECURE_BOOT` * `UEFI_COMPATIBLE` * `VIRTIO_SCSI_MULTIQUEUE` * `WINDOWS`
         */
        type?: string | null;
    }
    /**
     * The definition of a notebook instance.
     */
    export interface Schema$Instance {
        /**
         * The hardware accelerator used on this instance. If you use accelerators, make sure that your configuration has [enough vCPUs and memory to support the `machine_type` you have selected](https://cloud.google.com/compute/docs/gpus/#gpus-list).
         */
        acceleratorConfig?: Schema$AcceleratorConfig;
        /**
         * Input only. The size of the boot disk in GB attached to this instance, up to a maximum of 64000 GB (64 TB). The minimum recommended value is 100 GB. If not specified, this defaults to 100.
         */
        bootDiskSizeGb?: string | null;
        /**
         * Input only. The type of the boot disk attached to this instance, defaults to standard persistent disk (`PD_STANDARD`).
         */
        bootDiskType?: string | null;
        /**
         * Optional. Flag to enable ip forwarding or not, default false/off. https://cloud.google.com/vpc/docs/using-routes#canipforward
         */
        canIpForward?: boolean | null;
        /**
         * Use a container image to start the notebook instance.
         */
        containerImage?: Schema$ContainerImage;
        /**
         * Output only. Instance creation time.
         */
        createTime?: string | null;
        /**
         * Output only. Email address of entity that sent original CreateInstance request.
         */
        creator?: string | null;
        /**
         * Specify a custom Cloud Storage path where the GPU driver is stored. If not specified, we'll automatically choose from official GPU drivers.
         */
        customGpuDriverPath?: string | null;
        /**
         * Input only. The size of the data disk in GB attached to this instance, up to a maximum of 64000 GB (64 TB). You can choose the size of the data disk based on how big your notebooks and data are. If not specified, this defaults to 100.
         */
        dataDiskSizeGb?: string | null;
        /**
         * Input only. The type of the data disk attached to this instance, defaults to standard persistent disk (`PD_STANDARD`).
         */
        dataDiskType?: string | null;
        /**
         * Input only. Disk encryption method used on the boot and data disks, defaults to GMEK.
         */
        diskEncryption?: string | null;
        /**
         * Output only. Attached disks to notebook instance.
         */
        disks?: Schema$Disk[];
        /**
         * Whether the end user authorizes Google Cloud to install GPU driver on this instance. If this field is empty or set to false, the GPU driver won't be installed. Only applicable to instances with GPUs.
         */
        installGpuDriver?: boolean | null;
        /**
         * Output only. Checks how feasible a migration from UmN to WbI is.
         */
        instanceMigrationEligibility?: Schema$InstanceMigrationEligibility;
        /**
         * Input only. The owner of this instance after creation. Format: `<EMAIL>` Currently supports one owner only. If not specified, all of the service account users of your VM instance's service account can use the instance.
         */
        instanceOwners?: string[] | null;
        /**
         * Input only. The KMS key used to encrypt the disks, only applicable if disk_encryption is CMEK. Format: `projects/{project_id\}/locations/{location\}/keyRings/{key_ring_id\}/cryptoKeys/{key_id\}` Learn more about [using your own encryption keys](/kms/docs/quickstart).
         */
        kmsKey?: string | null;
        /**
         * Labels to apply to this instance. These can be later modified by the setLabels method.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. The [Compute Engine machine type](https://cloud.google.com/compute/docs/machine-resource) of this instance.
         */
        machineType?: string | null;
        /**
         * Custom metadata to apply to this instance. For example, to specify a Cloud Storage bucket for automatic backup, you can use the `gcs-data-bucket` metadata tag. Format: `"--metadata=gcs-data-bucket=BUCKET"`.
         */
        metadata?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Bool indicating whether this notebook has been migrated to a Workbench Instance
         */
        migrated?: boolean | null;
        /**
         * Output only. The name of this notebook instance. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string | null;
        /**
         * The name of the VPC that this instance is in. Format: `projects/{project_id\}/global/networks/{network_id\}`
         */
        network?: string | null;
        /**
         * Optional. The type of vNIC to be used on this interface. This may be gVNIC or VirtioNet.
         */
        nicType?: string | null;
        /**
         * If true, the notebook instance will not register with the proxy.
         */
        noProxyAccess?: boolean | null;
        /**
         * If true, no external IP will be assigned to this instance.
         */
        noPublicIp?: boolean | null;
        /**
         * Input only. If true, the data disk will not be auto deleted when deleting the instance.
         */
        noRemoveDataDisk?: boolean | null;
        /**
         * Path to a Bash script that automatically runs after a notebook instance fully boots up. The path must be a URL or Cloud Storage path (`gs://path-to-file/file-name`).
         */
        postStartupScript?: string | null;
        /**
         * Output only. The proxy endpoint that is used to access the Jupyter notebook.
         */
        proxyUri?: string | null;
        /**
         * Optional. The optional reservation affinity. Setting this field will apply the specified [Zonal Compute Reservation](https://cloud.google.com/compute/docs/instances/reserving-zonal-resources) to this notebook instance.
         */
        reservationAffinity?: Schema$ReservationAffinity;
        /**
         * The service account on this instance, giving access to other Google Cloud services. You can use any service account within the same project, but you must have the service account user permission to use the instance. If not specified, the [Compute Engine default service account](https://cloud.google.com/compute/docs/access/service-accounts#default_service_account) is used.
         */
        serviceAccount?: string | null;
        /**
         * Optional. The URIs of service account scopes to be included in Compute Engine instances. If not specified, the following [scopes](https://cloud.google.com/compute/docs/access/service-accounts#accesscopesiam) are defined: - https://www.googleapis.com/auth/cloud-platform - https://www.googleapis.com/auth/userinfo.email If not using default scopes, you need at least: https://www.googleapis.com/auth/compute
         */
        serviceAccountScopes?: string[] | null;
        /**
         * Optional. Shielded VM configuration. [Images using supported Shielded VM features](https://cloud.google.com/compute/docs/instances/modifying-shielded-vm).
         */
        shieldedInstanceConfig?: Schema$ShieldedInstanceConfig;
        /**
         * Output only. The state of this instance.
         */
        state?: string | null;
        /**
         * The name of the subnet that this instance is in. Format: `projects/{project_id\}/regions/{region\}/subnetworks/{subnetwork_id\}`
         */
        subnet?: string | null;
        /**
         * Optional. The Compute Engine network tags to add to runtime (see [Add network tags](https://cloud.google.com/vpc/docs/add-remove-network-tags)).
         */
        tags?: string[] | null;
        /**
         * Output only. Instance update time.
         */
        updateTime?: string | null;
        /**
         * The upgrade history of this instance.
         */
        upgradeHistory?: Schema$UpgradeHistoryEntry[];
        /**
         * Use a Compute Engine VM image to start the notebook instance.
         */
        vmImage?: Schema$VmImage;
    }
    /**
     * Notebook instance configurations that can be updated.
     */
    export interface Schema$InstanceConfig {
        /**
         * Verifies core internal services are running.
         */
        enableHealthMonitoring?: boolean | null;
        /**
         * Cron expression in UTC timezone, used to schedule instance auto upgrade. Please follow the [cron format](https://en.wikipedia.org/wiki/Cron).
         */
        notebookUpgradeSchedule?: string | null;
    }
    /**
     * InstanceMigrationEligibility represents the feasibility information of a migration from UmN to WbI.
     */
    export interface Schema$InstanceMigrationEligibility {
        /**
         * Output only. Certain configurations make the UmN ineligible for an automatic migration. A manual migration is required.
         */
        errors?: string[] | null;
        /**
         * Output only. Certain configurations will be defaulted during the migration.
         */
        warnings?: string[] | null;
    }
    /**
     * Response for checking if a notebook instance is upgradeable.
     */
    export interface Schema$IsInstanceUpgradeableResponse {
        /**
         * If an instance is upgradeable.
         */
        upgradeable?: boolean | null;
        /**
         * The new image self link this instance will be upgraded to if calling the upgrade endpoint. This field will only be populated if field upgradeable is true.
         */
        upgradeImage?: string | null;
        /**
         * Additional information about upgrade.
         */
        upgradeInfo?: string | null;
        /**
         * The version this instance will be upgraded to if calling the upgrade endpoint. This field will only be populated if field upgradeable is true.
         */
        upgradeVersion?: string | null;
    }
    /**
     * Response for listing environments.
     */
    export interface Schema$ListEnvironmentsResponse {
        /**
         * A list of returned environments.
         */
        environments?: Schema$Environment[];
        /**
         * A page token that can be used to continue listing from the last result in the next list call.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * Response for listing scheduled notebook executions
     */
    export interface Schema$ListExecutionsResponse {
        /**
         * A list of returned instances.
         */
        executions?: Schema$Execution[];
        /**
         * Page token that can be used to continue listing from the last result in the next list call.
         */
        nextPageToken?: string | null;
        /**
         * Executions IDs that could not be reached. For example: ['projects/{project_id\}/location/{location\}/executions/imagenet_test1', 'projects/{project_id\}/location/{location\}/executions/classifier_train1']
         */
        unreachable?: string[] | null;
    }
    /**
     * Response for listing notebook instances.
     */
    export interface Schema$ListInstancesResponse {
        /**
         * A list of returned instances.
         */
        instances?: Schema$Instance[];
        /**
         * Page token that can be used to continue listing from the last result in the next list call.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached. For example, `['us-west1-a', 'us-central1-b']`. A ListInstancesResponse will only contain either instances or unreachables,
         */
        unreachable?: string[] | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * Response for listing Managed Notebook Runtimes.
     */
    export interface Schema$ListRuntimesResponse {
        /**
         * Page token that can be used to continue listing from the last result in the next list call.
         */
        nextPageToken?: string | null;
        /**
         * A list of returned Runtimes.
         */
        runtimes?: Schema$Runtime[];
        /**
         * Locations that could not be reached. For example, `['us-west1', 'us-central1']`. A ListRuntimesResponse will only contain either runtimes or unreachables,
         */
        unreachable?: string[] | null;
    }
    /**
     * Response for listing scheduled notebook job.
     */
    export interface Schema$ListSchedulesResponse {
        /**
         * Page token that can be used to continue listing from the last result in the next list call.
         */
        nextPageToken?: string | null;
        /**
         * A list of returned instances.
         */
        schedules?: Schema$Schedule[];
        /**
         * Schedules that could not be reached. For example: ['projects/{project_id\}/location/{location\}/schedules/monthly_digest', 'projects/{project_id\}/location/{location\}/schedules/weekly_sentiment']
         */
        unreachable?: string[] | null;
    }
    /**
     * A Local attached disk resource.
     */
    export interface Schema$LocalDisk {
        /**
         * Optional. Output only. Specifies whether the disk will be auto-deleted when the instance is deleted (but not when the disk is detached from the instance).
         */
        autoDelete?: boolean | null;
        /**
         * Optional. Output only. Indicates that this is a boot disk. The virtual machine will use the first partition of the disk for its root filesystem.
         */
        boot?: boolean | null;
        /**
         * Optional. Output only. Specifies a unique device name of your choice that is reflected into the `/dev/disk/by-id/google-*` tree of a Linux operating system running within the instance. This name can be used to reference the device for mounting, resizing, and so on, from within the instance. If not specified, the server chooses a default device name to apply to this disk, in the form persistent-disk-x, where x is a number assigned by Google Compute Engine. This field is only applicable for persistent disks.
         */
        deviceName?: string | null;
        /**
         * Output only. Indicates a list of features to enable on the guest operating system. Applicable only for bootable images. Read Enabling guest operating system features to see a list of available options.
         */
        guestOsFeatures?: Schema$RuntimeGuestOsFeature[];
        /**
         * Output only. A zero-based index to this disk, where 0 is reserved for the boot disk. If you have many disks attached to an instance, each disk would have a unique index number.
         */
        index?: number | null;
        /**
         * Input only. Specifies the parameters for a new disk that will be created alongside the new instance. Use initialization parameters to create boot disks or local SSDs attached to the new instance. This property is mutually exclusive with the source property; you can only define one or the other, but not both.
         */
        initializeParams?: Schema$LocalDiskInitializeParams;
        /**
         * Specifies the disk interface to use for attaching this disk, which is either SCSI or NVME. The default is SCSI. Persistent disks must always use SCSI and the request will fail if you attempt to attach a persistent disk in any other format than SCSI. Local SSDs can use either NVME or SCSI. For performance characteristics of SCSI over NVMe, see Local SSD performance. Valid values: * `NVME` * `SCSI`
         */
        interface?: string | null;
        /**
         * Output only. Type of the resource. Always compute#attachedDisk for attached disks.
         */
        kind?: string | null;
        /**
         * Output only. Any valid publicly visible licenses.
         */
        licenses?: string[] | null;
        /**
         * The mode in which to attach this disk, either `READ_WRITE` or `READ_ONLY`. If not specified, the default is to attach the disk in `READ_WRITE` mode. Valid values: * `READ_ONLY` * `READ_WRITE`
         */
        mode?: string | null;
        /**
         * Specifies a valid partial or full URL to an existing Persistent Disk resource.
         */
        source?: string | null;
        /**
         * Specifies the type of the disk, either `SCRATCH` or `PERSISTENT`. If not specified, the default is `PERSISTENT`. Valid values: * `PERSISTENT` * `SCRATCH`
         */
        type?: string | null;
    }
    /**
     * Input only. Specifies the parameters for a new disk that will be created alongside the new instance. Use initialization parameters to create boot disks or local SSDs attached to the new runtime. This property is mutually exclusive with the source property; you can only define one or the other, but not both.
     */
    export interface Schema$LocalDiskInitializeParams {
        /**
         * Optional. Provide this property when creating the disk.
         */
        description?: string | null;
        /**
         * Optional. Specifies the disk name. If not specified, the default is to use the name of the instance. If the disk with the instance name exists already in the given zone/region, a new name will be automatically generated.
         */
        diskName?: string | null;
        /**
         * Optional. Specifies the size of the disk in base-2 GB. If not specified, the disk will be the same size as the image (usually 10GB). If specified, the size must be equal to or larger than 10GB. Default 100 GB.
         */
        diskSizeGb?: string | null;
        /**
         * Input only. The type of the boot disk attached to this instance, defaults to standard persistent disk (`PD_STANDARD`).
         */
        diskType?: string | null;
        /**
         * Optional. Labels to apply to this disk. These can be later modified by the disks.setLabels method. This field is only applicable for persistent disks.
         */
        labels?: {
            [key: string]: string;
        } | null;
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * Request for migrating a User-Managed Notebook to Workbench Instances.
     */
    export interface Schema$MigrateInstanceRequest {
        /**
         * Optional. Specifies the behavior of post startup script during migration.
         */
        postStartupScriptOption?: string | null;
    }
    /**
     * Request for migrating a Runtime to a Workbench Instance.
     */
    export interface Schema$MigrateRuntimeRequest {
        /**
         * Optional. Name of the VPC that the new Instance is in. This is required if the Runtime uses google-managed network. If the Runtime uses customer-owned network, it will reuse the same VPC, and this field must be empty. Format: `projects/{project_id\}/global/networks/{network_id\}`
         */
        network?: string | null;
        /**
         * Optional. Specifies the behavior of post startup script during migration.
         */
        postStartupScriptOption?: string | null;
        /**
         * Optional. Idempotent request UUID.
         */
        requestId?: string | null;
        /**
         * Optional. The service account to be included in the Compute Engine instance of the new Workbench Instance when the Runtime uses "single user only" mode for permission. If not specified, the [Compute Engine default service account](https://cloud.google.com/compute/docs/access/service-accounts#default_service_account) is used. When the Runtime uses service account mode for permission, it will reuse the same service account, and this field must be empty.
         */
        serviceAccount?: string | null;
        /**
         * Optional. Name of the subnet that the new Instance is in. This is required if the Runtime uses google-managed network. If the Runtime uses customer-owned network, it will reuse the same subnet, and this field must be empty. Format: `projects/{project_id\}/regions/{region\}/subnetworks/{subnetwork_id\}`
         */
        subnet?: string | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$OperationMetadata {
        /**
         * API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * The time the operation was created.
         */
        createTime?: string | null;
        /**
         * API endpoint name of this operation.
         */
        endpoint?: string | null;
        /**
         * The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$Policy {
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * Request for getting a new access token.
     */
    export interface Schema$RefreshRuntimeTokenInternalRequest {
        /**
         * Required. The VM hardware token for authenticating the VM. https://cloud.google.com/compute/docs/instances/verifying-instance-identity
         */
        vmId?: string | null;
    }
    /**
     * Response with a new access token.
     */
    export interface Schema$RefreshRuntimeTokenInternalResponse {
        /**
         * The OAuth 2.0 access token.
         */
        accessToken?: string | null;
        /**
         * Output only. Token expiration time.
         */
        expireTime?: string | null;
    }
    /**
     * Request for registering a notebook instance.
     */
    export interface Schema$RegisterInstanceRequest {
        /**
         * Required. User defined unique ID of this instance. The `instance_id` must be 1 to 63 characters long and contain only lowercase letters, numeric characters, and dashes. The first character must be a lowercase letter and the last character cannot be a dash.
         */
        instanceId?: string | null;
    }
    /**
     * Request for reporting a Managed Notebook Event.
     */
    export interface Schema$ReportInstanceEventRequest {
        /**
         * Required. The Event to be reported.
         */
        event?: Schema$Event;
        /**
         * Required. The VM hardware token for authenticating the VM. https://cloud.google.com/compute/docs/instances/verifying-instance-identity
         */
        vmId?: string | null;
    }
    /**
     * Request for notebook instances to report information to Notebooks API.
     */
    export interface Schema$ReportInstanceInfoRequest {
        /**
         * The metadata reported to Notebooks API. This will be merged to the instance metadata store
         */
        metadata?: {
            [key: string]: string;
        } | null;
        /**
         * Required. The VM hardware token for authenticating the VM. https://cloud.google.com/compute/docs/instances/verifying-instance-identity
         */
        vmId?: string | null;
    }
    /**
     * Request for reporting a Managed Notebook Event.
     */
    export interface Schema$ReportRuntimeEventRequest {
        /**
         * Required. The Event to be reported.
         */
        event?: Schema$Event;
        /**
         * Required. The VM hardware token for authenticating the VM. https://cloud.google.com/compute/docs/instances/verifying-instance-identity
         */
        vmId?: string | null;
    }
    /**
     * Reservation Affinity for consuming Zonal reservation.
     */
    export interface Schema$ReservationAffinity {
        /**
         * Optional. Type of reservation to consume
         */
        consumeReservationType?: string | null;
        /**
         * Optional. Corresponds to the label key of reservation resource.
         */
        key?: string | null;
        /**
         * Optional. Corresponds to the label values of reservation resource.
         */
        values?: string[] | null;
    }
    /**
     * Request for resetting a notebook instance
     */
    export interface Schema$ResetInstanceRequest {
    }
    /**
     * Request for resetting a Managed Notebook Runtime.
     */
    export interface Schema$ResetRuntimeRequest {
        /**
         * Idempotent request UUID.
         */
        requestId?: string | null;
    }
    /**
     * Request for rollbacking a notebook instance
     */
    export interface Schema$RollbackInstanceRequest {
        /**
         * Required. The snapshot for rollback. Example: `projects/test-project/global/snapshots/krwlzipynril`.
         */
        targetSnapshot?: string | null;
    }
    /**
     * The definition of a Runtime for a managed notebook instance.
     */
    export interface Schema$Runtime {
        /**
         * The config settings for accessing runtime.
         */
        accessConfig?: Schema$RuntimeAccessConfig;
        /**
         * Output only. Runtime creation time.
         */
        createTime?: string | null;
        /**
         * Output only. Runtime health_state.
         */
        healthState?: string | null;
        /**
         * Optional. The labels to associate with this Managed Notebook or Runtime. Label **keys** must contain 1 to 63 characters, and must conform to [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt). Label **values** may be empty, but, if present, must contain 1 to 63 characters, and must conform to [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt). No more than 32 labels can be associated with a cluster.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Contains Runtime daemon metrics such as Service status and JupyterLab stats.
         */
        metrics?: Schema$RuntimeMetrics;
        /**
         * Output only. Bool indicating whether this notebook has been migrated to a Workbench Instance
         */
        migrated?: boolean | null;
        /**
         * Output only. The resource name of the runtime. Format: `projects/{project\}/locations/{location\}/runtimes/{runtimeId\}`
         */
        name?: string | null;
        /**
         * Output only. Checks how feasible a migration from GmN to WbI is.
         */
        runtimeMigrationEligibility?: Schema$RuntimeMigrationEligibility;
        /**
         * The config settings for software inside the runtime.
         */
        softwareConfig?: Schema$RuntimeSoftwareConfig;
        /**
         * Output only. Runtime state.
         */
        state?: string | null;
        /**
         * Output only. Runtime update time.
         */
        updateTime?: string | null;
        /**
         * Use a Compute Engine VM image to start the managed notebook instance.
         */
        virtualMachine?: Schema$VirtualMachine;
    }
    /**
     * Definition of the types of hardware accelerators that can be used. See [Compute Engine AcceleratorTypes](https://cloud.google.com/compute/docs/reference/beta/acceleratorTypes). Examples: * `nvidia-tesla-k80` * `nvidia-tesla-p100` * `nvidia-tesla-v100` * `nvidia-tesla-p4` * `nvidia-tesla-t4` * `nvidia-tesla-a100`
     */
    export interface Schema$RuntimeAcceleratorConfig {
        /**
         * Count of cores of this accelerator.
         */
        coreCount?: string | null;
        /**
         * Accelerator model.
         */
        type?: string | null;
    }
    /**
     * Specifies the login configuration for Runtime
     */
    export interface Schema$RuntimeAccessConfig {
        /**
         * The type of access mode this instance.
         */
        accessType?: string | null;
        /**
         * Output only. The proxy endpoint that is used to access the runtime.
         */
        proxyUri?: string | null;
        /**
         * The owner of this runtime after creation. Format: `<EMAIL>` Currently supports one owner only.
         */
        runtimeOwner?: string | null;
    }
    /**
     * Optional. A list of features to enable on the guest operating system. Applicable only for bootable images. Read [Enabling guest operating system features](https://cloud.google.com/compute/docs/images/create-delete-deprecate-private-images#guest-os-features) to see a list of available options. Guest OS features for boot disk.
     */
    export interface Schema$RuntimeGuestOsFeature {
        /**
         * The ID of a supported feature. Read [Enabling guest operating system features](https://cloud.google.com/compute/docs/images/create-delete-deprecate-private-images#guest-os-features) to see a list of available options. Valid values: * `FEATURE_TYPE_UNSPECIFIED` * `MULTI_IP_SUBNET` * `SECURE_BOOT` * `UEFI_COMPATIBLE` * `VIRTIO_SCSI_MULTIQUEUE` * `WINDOWS`
         */
        type?: string | null;
    }
    /**
     * Contains runtime daemon metrics, such as OS and kernels and sessions stats.
     */
    export interface Schema$RuntimeMetrics {
        /**
         * Output only. The system metrics.
         */
        systemMetrics?: {
            [key: string]: string;
        } | null;
    }
    /**
     * RuntimeMigrationEligibility represents the feasibility information of a migration from GmN to WbI.
     */
    export interface Schema$RuntimeMigrationEligibility {
        /**
         * Output only. Certain configurations make the GmN ineligible for an automatic migration. A manual migration is required.
         */
        errors?: string[] | null;
        /**
         * Output only. Certain configurations will be defaulted during the migration.
         */
        warnings?: string[] | null;
    }
    /**
     * A set of Shielded Instance options. See [Images using supported Shielded VM features](https://cloud.google.com/compute/docs/instances/modifying-shielded-vm). Not all combinations are valid.
     */
    export interface Schema$RuntimeShieldedInstanceConfig {
        /**
         * Defines whether the instance has integrity monitoring enabled. Enables monitoring and attestation of the boot integrity of the instance. The attestation is performed against the integrity policy baseline. This baseline is initially derived from the implicitly trusted boot image when the instance is created. Enabled by default.
         */
        enableIntegrityMonitoring?: boolean | null;
        /**
         * Defines whether the instance has Secure Boot enabled. Secure Boot helps ensure that the system only runs authentic software by verifying the digital signature of all boot components, and halting the boot process if signature verification fails. Disabled by default.
         */
        enableSecureBoot?: boolean | null;
        /**
         * Defines whether the instance has the vTPM enabled. Enabled by default.
         */
        enableVtpm?: boolean | null;
    }
    /**
     * Specifies the selection and configuration of software inside the runtime. The properties to set on runtime. Properties keys are specified in `key:value` format, for example: * `idle_shutdown: true` * `idle_shutdown_timeout: 180` * `enable_health_monitoring: true`
     */
    export interface Schema$RuntimeSoftwareConfig {
        /**
         * Specify a custom Cloud Storage path where the GPU driver is stored. If not specified, we'll automatically choose from official GPU drivers.
         */
        customGpuDriverPath?: string | null;
        /**
         * Bool indicating whether JupyterLab terminal will be available or not. Default: False
         */
        disableTerminal?: boolean | null;
        /**
         * Verifies core internal services are running. Default: True
         */
        enableHealthMonitoring?: boolean | null;
        /**
         * Runtime will automatically shutdown after idle_shutdown_time. Default: True
         */
        idleShutdown?: boolean | null;
        /**
         * Time in minutes to wait before shutting down runtime. Default: 180 minutes
         */
        idleShutdownTimeout?: number | null;
        /**
         * Install Nvidia Driver automatically. Default: True
         */
        installGpuDriver?: boolean | null;
        /**
         * Optional. Use a list of container images to use as Kernels in the notebook instance.
         */
        kernels?: Schema$ContainerImage[];
        /**
         * Bool indicating whether mixer client should be disabled. Default: False
         */
        mixerDisabled?: boolean | null;
        /**
         * Cron expression in UTC timezone, used to schedule instance auto upgrade. Please follow the [cron format](https://en.wikipedia.org/wiki/Cron).
         */
        notebookUpgradeSchedule?: string | null;
        /**
         * Path to a Bash script that automatically runs after a notebook instance fully boots up. The path must be a URL or Cloud Storage path (`gs://path-to-file/file-name`).
         */
        postStartupScript?: string | null;
        /**
         * Behavior for the post startup script.
         */
        postStartupScriptBehavior?: string | null;
        /**
         * Output only. Bool indicating whether an newer image is available in an image family.
         */
        upgradeable?: boolean | null;
        /**
         * Output only. version of boot image such as M100, from release label of the image.
         */
        version?: string | null;
    }
    /**
     * The definition of a schedule.
     */
    export interface Schema$Schedule {
        /**
         * Output only. Time the schedule was created.
         */
        createTime?: string | null;
        /**
         * Cron-tab formatted schedule by which the job will execute. Format: minute, hour, day of month, month, day of week, e.g. `0 0 * * WED` = every Wednesday More examples: https://crontab.guru/examples.html
         */
        cronSchedule?: string | null;
        /**
         * A brief description of this environment.
         */
        description?: string | null;
        /**
         * Output only. Display name used for UI purposes. Name can only contain alphanumeric characters, hyphens `-`, and underscores `_`.
         */
        displayName?: string | null;
        /**
         * Notebook Execution Template corresponding to this schedule.
         */
        executionTemplate?: Schema$ExecutionTemplate;
        /**
         * Output only. The name of this schedule. Format: `projects/{project_id\}/locations/{location\}/schedules/{schedule_id\}`
         */
        name?: string | null;
        /**
         * Output only. The most recent execution names triggered from this schedule and their corresponding states.
         */
        recentExecutions?: Schema$Execution[];
        state?: string | null;
        /**
         * Timezone on which the cron_schedule. The value of this field must be a time zone name from the tz database. TZ Database: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones Note that some time zones include a provision for daylight savings time. The rules for daylight saving time are determined by the chosen tz. For UTC use the string "utc". If a time zone is not specified, the default will be in UTC (also known as GMT).
         */
        timeZone?: string | null;
        /**
         * Output only. Time the schedule was last updated.
         */
        updateTime?: string | null;
    }
    /**
     * Definition of a hardware accelerator. Note that not all combinations of `type` and `core_count` are valid. See [GPUs on Compute Engine](https://cloud.google.com/compute/docs/gpus) to find a valid combination. TPUs are not supported.
     */
    export interface Schema$SchedulerAcceleratorConfig {
        /**
         * Count of cores of this accelerator.
         */
        coreCount?: string | null;
        /**
         * Type of this accelerator.
         */
        type?: string | null;
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$SetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$Policy;
    }
    /**
     * Request for setting instance accelerator.
     */
    export interface Schema$SetInstanceAcceleratorRequest {
        /**
         * Required. Count of cores of this accelerator. Note that not all combinations of `type` and `core_count` are valid. See [GPUs on Compute Engine](https://cloud.google.com/compute/docs/gpus/#gpus-list) to find a valid combination. TPUs are not supported.
         */
        coreCount?: string | null;
        /**
         * Required. Type of this accelerator.
         */
        type?: string | null;
    }
    /**
     * Request for setting instance labels.
     */
    export interface Schema$SetInstanceLabelsRequest {
        /**
         * Labels to apply to this instance. These can be later modified by the setLabels method
         */
        labels?: {
            [key: string]: string;
        } | null;
    }
    /**
     * Request for setting instance machine type.
     */
    export interface Schema$SetInstanceMachineTypeRequest {
        /**
         * Required. The [Compute Engine machine type](https://cloud.google.com/compute/docs/machine-resource).
         */
        machineType?: string | null;
    }
    /**
     * A set of Shielded Instance options. See [Images using supported Shielded VM features](https://cloud.google.com/compute/docs/instances/modifying-shielded-vm). Not all combinations are valid.
     */
    export interface Schema$ShieldedInstanceConfig {
        /**
         * Defines whether the instance has integrity monitoring enabled. Enables monitoring and attestation of the boot integrity of the instance. The attestation is performed against the integrity policy baseline. This baseline is initially derived from the implicitly trusted boot image when the instance is created. Enabled by default.
         */
        enableIntegrityMonitoring?: boolean | null;
        /**
         * Defines whether the instance has Secure Boot enabled. Secure Boot helps ensure that the system only runs authentic software by verifying the digital signature of all boot components, and halting the boot process if signature verification fails. Disabled by default.
         */
        enableSecureBoot?: boolean | null;
        /**
         * Defines whether the instance has the vTPM enabled. Enabled by default.
         */
        enableVtpm?: boolean | null;
    }
    /**
     * Request for starting a notebook instance
     */
    export interface Schema$StartInstanceRequest {
    }
    /**
     * Request for starting a Managed Notebook Runtime.
     */
    export interface Schema$StartRuntimeRequest {
        /**
         * Idempotent request UUID.
         */
        requestId?: string | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Request for stopping a notebook instance
     */
    export interface Schema$StopInstanceRequest {
    }
    /**
     * Request for stopping a Managed Notebook Runtime.
     */
    export interface Schema$StopRuntimeRequest {
        /**
         * Idempotent request UUID.
         */
        requestId?: string | null;
    }
    /**
     * Request for switching a Managed Notebook Runtime.
     */
    export interface Schema$SwitchRuntimeRequest {
        /**
         * accelerator config.
         */
        acceleratorConfig?: Schema$RuntimeAcceleratorConfig;
        /**
         * machine type.
         */
        machineType?: string | null;
        /**
         * Idempotent request UUID.
         */
        requestId?: string | null;
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * Request for created scheduled notebooks
     */
    export interface Schema$TriggerScheduleRequest {
    }
    /**
     * Request for updating instance configurations.
     */
    export interface Schema$UpdateInstanceConfigRequest {
        /**
         * The instance configurations to be updated.
         */
        config?: Schema$InstanceConfig;
    }
    /**
     * Request for adding/changing metadata items for an instance.
     */
    export interface Schema$UpdateInstanceMetadataItemsRequest {
        /**
         * Metadata items to add/update for the instance.
         */
        items?: {
            [key: string]: string;
        } | null;
    }
    /**
     * Response for adding/changing metadata items for an instance.
     */
    export interface Schema$UpdateInstanceMetadataItemsResponse {
        /**
         * Map of items that were added/updated to/in the metadata.
         */
        items?: {
            [key: string]: string;
        } | null;
    }
    /**
     * Request for updating the Shielded Instance config for a notebook instance. You can only use this method on a stopped instance
     */
    export interface Schema$UpdateShieldedInstanceConfigRequest {
        /**
         * ShieldedInstance configuration to be updated.
         */
        shieldedInstanceConfig?: Schema$ShieldedInstanceConfig;
    }
    /**
     * The entry of VM image upgrade history.
     */
    export interface Schema$UpgradeHistoryEntry {
        /**
         * Action. Rolloback or Upgrade.
         */
        action?: string | null;
        /**
         * The container image before this instance upgrade.
         */
        containerImage?: string | null;
        /**
         * The time that this instance upgrade history entry is created.
         */
        createTime?: string | null;
        /**
         * The framework of this notebook instance.
         */
        framework?: string | null;
        /**
         * The snapshot of the boot disk of this notebook instance before upgrade.
         */
        snapshot?: string | null;
        /**
         * The state of this instance upgrade history entry.
         */
        state?: string | null;
        /**
         * Target VM Image. Format: `ainotebooks-vm/project/image-name/name`.
         */
        targetImage?: string | null;
        /**
         * Target VM Version, like m63.
         */
        targetVersion?: string | null;
        /**
         * The version of the notebook instance before this upgrade.
         */
        version?: string | null;
        /**
         * The VM image before this instance upgrade.
         */
        vmImage?: string | null;
    }
    /**
     * Request for upgrading a notebook instance from within the VM
     */
    export interface Schema$UpgradeInstanceInternalRequest {
        /**
         * Optional. The optional UpgradeType. Setting this field will search for additional compute images to upgrade this instance.
         */
        type?: string | null;
        /**
         * Required. The VM hardware token for authenticating the VM. https://cloud.google.com/compute/docs/instances/verifying-instance-identity
         */
        vmId?: string | null;
    }
    /**
     * Request for upgrading a notebook instance
     */
    export interface Schema$UpgradeInstanceRequest {
        /**
         * Optional. The optional UpgradeType. Setting this field will search for additional compute images to upgrade this instance.
         */
        type?: string | null;
    }
    /**
     * Request for upgrading a Managed Notebook Runtime to the latest version. option (google.api.message_visibility).restriction = "TRUSTED_TESTER,SPECIAL_TESTER";
     */
    export interface Schema$UpgradeRuntimeRequest {
        /**
         * Idempotent request UUID.
         */
        requestId?: string | null;
    }
    /**
     * Parameters used in Vertex AI JobType executions.
     */
    export interface Schema$VertexAIParameters {
        /**
         * Environment variables. At most 100 environment variables can be specified and unique. Example: `GCP_BUCKET=gs://my-bucket/samples/`
         */
        env?: {
            [key: string]: string;
        } | null;
        /**
         * The full name of the Compute Engine [network](https://cloud.google.com/compute/docs/networks-and-firewalls#networks) to which the Job should be peered. For example, `projects/12345/global/networks/myVPC`. [Format](https://cloud.google.com/compute/docs/reference/rest/v1/networks/insert) is of the form `projects/{project\}/global/networks/{network\}`. Where `{project\}` is a project number, as in `12345`, and `{network\}` is a network name. Private services access must already be configured for the network. If left unspecified, the job is not peered with any network.
         */
        network?: string | null;
    }
    /**
     * Runtime using Virtual Machine for computing.
     */
    export interface Schema$VirtualMachine {
        /**
         * Output only. The unique identifier of the Managed Compute Engine instance.
         */
        instanceId?: string | null;
        /**
         * Output only. The user-friendly name of the Managed Compute Engine instance.
         */
        instanceName?: string | null;
        /**
         * Virtual Machine configuration settings.
         */
        virtualMachineConfig?: Schema$VirtualMachineConfig;
    }
    /**
     * The config settings for virtual machine.
     */
    export interface Schema$VirtualMachineConfig {
        /**
         * Optional. The Compute Engine accelerator configuration for this runtime.
         */
        acceleratorConfig?: Schema$RuntimeAcceleratorConfig;
        /**
         * Optional. Boot image metadata used for runtime upgradeability.
         */
        bootImage?: Schema$BootImage;
        /**
         * Optional. Use a list of container images to use as Kernels in the notebook instance.
         */
        containerImages?: Schema$ContainerImage[];
        /**
         * Required. Data disk option configuration settings.
         */
        dataDisk?: Schema$LocalDisk;
        /**
         * Optional. Encryption settings for virtual machine data disk.
         */
        encryptionConfig?: Schema$EncryptionConfig;
        /**
         * Output only. The Compute Engine guest attributes. (see [Project and instance guest attributes](https://cloud.google.com/compute/docs/storing-retrieving-metadata#guest_attributes)).
         */
        guestAttributes?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. If true, runtime will only have internal IP addresses. By default, runtimes are not restricted to internal IP addresses, and will have ephemeral external IP addresses assigned to each vm. This `internal_ip_only` restriction can only be enabled for subnetwork enabled networks, and all dependencies must be configured to be accessible without external IP addresses.
         */
        internalIpOnly?: boolean | null;
        /**
         * Optional. The labels to associate with this runtime. Label **keys** must contain 1 to 63 characters, and must conform to [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt). Label **values** may be empty, but, if present, must contain 1 to 63 characters, and must conform to [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt). No more than 32 labels can be associated with a cluster.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. The Compute Engine machine type used for runtimes. Short name is valid. Examples: * `n1-standard-2` * `e2-standard-8`
         */
        machineType?: string | null;
        /**
         * Optional. The Compute Engine metadata entries to add to virtual machine. (see [Project and instance metadata](https://cloud.google.com/compute/docs/storing-retrieving-metadata#project_and_instance_metadata)).
         */
        metadata?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. The Compute Engine network to be used for machine communications. Cannot be specified with subnetwork. If neither `network` nor `subnet` is specified, the "default" network of the project is used, if it exists. A full URL or partial URI. Examples: * `https://www.googleapis.com/compute/v1/projects/[project_id]/global/networks/default` * `projects/[project_id]/global/networks/default` Runtimes are managed resources inside Google Infrastructure. Runtimes support the following network configurations: * Google Managed Network (Network & subnet are empty) * Consumer Project VPC (network & subnet are required). Requires configuring Private Service Access. * Shared VPC (network & subnet are required). Requires configuring Private Service Access.
         */
        network?: string | null;
        /**
         * Optional. The type of vNIC to be used on this interface. This may be gVNIC or VirtioNet.
         */
        nicType?: string | null;
        /**
         * Optional. Reserved IP Range name is used for VPC Peering. The subnetwork allocation will use the range *name* if it's assigned. Example: managed-notebooks-range-c PEERING_RANGE_NAME_3=managed-notebooks-range-c gcloud compute addresses create $PEERING_RANGE_NAME_3 \ --global \ --prefix-length=24 \ --description="Google Cloud Managed Notebooks Range 24 c" \ --network=$NETWORK \ --addresses=*********** \ --purpose=VPC_PEERING Field value will be: `managed-notebooks-range-c`
         */
        reservedIpRange?: string | null;
        /**
         * Optional. Shielded VM Instance configuration settings.
         */
        shieldedInstanceConfig?: Schema$RuntimeShieldedInstanceConfig;
        /**
         * Optional. The Compute Engine subnetwork to be used for machine communications. Cannot be specified with network. A full URL or partial URI are valid. Examples: * `https://www.googleapis.com/compute/v1/projects/[project_id]/regions/us-east1/subnetworks/sub0` * `projects/[project_id]/regions/us-east1/subnetworks/sub0`
         */
        subnet?: string | null;
        /**
         * Optional. The Compute Engine network tags to add to runtime (see [Add network tags](https://cloud.google.com/vpc/docs/add-remove-network-tags)).
         */
        tags?: string[] | null;
        /**
         * Output only. The zone where the virtual machine is located. If using regional request, the notebooks service will pick a location in the corresponding runtime region. On a get request, zone will always be present. Example: * `us-central1-b`
         */
        zone?: string | null;
    }
    /**
     * Definition of a custom Compute Engine virtual machine image for starting a notebook instance with the environment installed directly on the VM.
     */
    export interface Schema$VmImage {
        /**
         * Use this VM image family to find the image; the newest image in this family will be used.
         */
        imageFamily?: string | null;
        /**
         * Use VM image name to find the image.
         */
        imageName?: string | null;
        /**
         * Required. The name of the Google Cloud project that this VM image belongs to. Format: `{project_id\}`
         */
        project?: string | null;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        environments: Resource$Projects$Locations$Environments;
        executions: Resource$Projects$Locations$Executions;
        instances: Resource$Projects$Locations$Instances;
        operations: Resource$Projects$Locations$Operations;
        runtimes: Resource$Projects$Locations$Runtimes;
        schedules: Resource$Projects$Locations$Schedules;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): GaxiosPromise<Schema$Location>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): GaxiosPromise<Schema$ListLocationsResponse>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Environments {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new Environment.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Environments$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Environments$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Environments$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Environments$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Environments$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single Environment.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Environments$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Environments$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Environments$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Environments$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Environments$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single Environment.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Environments$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Environments$Get, options?: MethodOptions): GaxiosPromise<Schema$Environment>;
        get(params: Params$Resource$Projects$Locations$Environments$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Environments$Get, options: MethodOptions | BodyResponseCallback<Schema$Environment>, callback: BodyResponseCallback<Schema$Environment>): void;
        get(params: Params$Resource$Projects$Locations$Environments$Get, callback: BodyResponseCallback<Schema$Environment>): void;
        get(callback: BodyResponseCallback<Schema$Environment>): void;
        /**
         * Lists environments in a project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Environments$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Environments$List, options?: MethodOptions): GaxiosPromise<Schema$ListEnvironmentsResponse>;
        list(params: Params$Resource$Projects$Locations$Environments$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Environments$List, options: MethodOptions | BodyResponseCallback<Schema$ListEnvironmentsResponse>, callback: BodyResponseCallback<Schema$ListEnvironmentsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Environments$List, callback: BodyResponseCallback<Schema$ListEnvironmentsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListEnvironmentsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Environments$Create extends StandardParameters {
        /**
         * Required. User-defined unique ID of this environment. The `environment_id` must be 1 to 63 characters long and contain only lowercase letters, numeric characters, and dashes. The first character must be a lowercase letter and the last character cannot be a dash.
         */
        environmentId?: string;
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Environment;
    }
    export interface Params$Resource$Projects$Locations$Environments$Delete extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/environments/{environment_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Environments$Get extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/environments/{environment_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Environments$List extends StandardParameters {
        /**
         * Maximum return size of the list call.
         */
        pageSize?: number;
        /**
         * A previous returned page token that can be used to continue listing from the last result.
         */
        pageToken?: string;
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}`
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Executions {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new Execution in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Executions$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Executions$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Executions$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Executions$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Executions$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes execution
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Executions$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Executions$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Executions$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Executions$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Executions$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of executions
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Executions$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Executions$Get, options?: MethodOptions): GaxiosPromise<Schema$Execution>;
        get(params: Params$Resource$Projects$Locations$Executions$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Executions$Get, options: MethodOptions | BodyResponseCallback<Schema$Execution>, callback: BodyResponseCallback<Schema$Execution>): void;
        get(params: Params$Resource$Projects$Locations$Executions$Get, callback: BodyResponseCallback<Schema$Execution>): void;
        get(callback: BodyResponseCallback<Schema$Execution>): void;
        /**
         * Lists executions in a given project and location
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Executions$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Executions$List, options?: MethodOptions): GaxiosPromise<Schema$ListExecutionsResponse>;
        list(params: Params$Resource$Projects$Locations$Executions$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Executions$List, options: MethodOptions | BodyResponseCallback<Schema$ListExecutionsResponse>, callback: BodyResponseCallback<Schema$ListExecutionsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Executions$List, callback: BodyResponseCallback<Schema$ListExecutionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListExecutionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Executions$Create extends StandardParameters {
        /**
         * Required. User-defined unique ID of this execution.
         */
        executionId?: string;
        /**
         * Required. Format: `parent=projects/{project_id\}/locations/{location\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Execution;
    }
    export interface Params$Resource$Projects$Locations$Executions$Delete extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/executions/{execution_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Executions$Get extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/executions/{execution_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Executions$List extends StandardParameters {
        /**
         * Filter applied to resulting executions. Currently only supports filtering executions by a specified `schedule_id`. Format: `schedule_id=`
         */
        filter?: string;
        /**
         * Sort by field.
         */
        orderBy?: string;
        /**
         * Maximum return size of the list call.
         */
        pageSize?: number;
        /**
         * A previous returned page token that can be used to continue listing from the last result.
         */
        pageToken?: string;
        /**
         * Required. Format: `parent=projects/{project_id\}/locations/{location\}`
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Instances {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new Instance in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Instances$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Instances$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Instances$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Instances$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Instances$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single Instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Instances$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Creates a Diagnostic File and runs Diagnostic Tool given an Instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        diagnose(params: Params$Resource$Projects$Locations$Instances$Diagnose, options: StreamMethodOptions): GaxiosPromise<Readable>;
        diagnose(params?: Params$Resource$Projects$Locations$Instances$Diagnose, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        diagnose(params: Params$Resource$Projects$Locations$Instances$Diagnose, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        diagnose(params: Params$Resource$Projects$Locations$Instances$Diagnose, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        diagnose(params: Params$Resource$Projects$Locations$Instances$Diagnose, callback: BodyResponseCallback<Schema$Operation>): void;
        diagnose(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single Instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Instances$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Instances$Get, options?: MethodOptions): GaxiosPromise<Schema$Instance>;
        get(params: Params$Resource$Projects$Locations$Instances$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Instances$Get, options: MethodOptions | BodyResponseCallback<Schema$Instance>, callback: BodyResponseCallback<Schema$Instance>): void;
        get(params: Params$Resource$Projects$Locations$Instances$Get, callback: BodyResponseCallback<Schema$Instance>): void;
        get(callback: BodyResponseCallback<Schema$Instance>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Instances$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Instances$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Instances$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Instances$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Instances$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Checks whether a notebook instance is healthy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getInstanceHealth(params: Params$Resource$Projects$Locations$Instances$Getinstancehealth, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getInstanceHealth(params?: Params$Resource$Projects$Locations$Instances$Getinstancehealth, options?: MethodOptions): GaxiosPromise<Schema$GetInstanceHealthResponse>;
        getInstanceHealth(params: Params$Resource$Projects$Locations$Instances$Getinstancehealth, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getInstanceHealth(params: Params$Resource$Projects$Locations$Instances$Getinstancehealth, options: MethodOptions | BodyResponseCallback<Schema$GetInstanceHealthResponse>, callback: BodyResponseCallback<Schema$GetInstanceHealthResponse>): void;
        getInstanceHealth(params: Params$Resource$Projects$Locations$Instances$Getinstancehealth, callback: BodyResponseCallback<Schema$GetInstanceHealthResponse>): void;
        getInstanceHealth(callback: BodyResponseCallback<Schema$GetInstanceHealthResponse>): void;
        /**
         * Checks whether a notebook instance is upgradable.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        isUpgradeable(params: Params$Resource$Projects$Locations$Instances$Isupgradeable, options: StreamMethodOptions): GaxiosPromise<Readable>;
        isUpgradeable(params?: Params$Resource$Projects$Locations$Instances$Isupgradeable, options?: MethodOptions): GaxiosPromise<Schema$IsInstanceUpgradeableResponse>;
        isUpgradeable(params: Params$Resource$Projects$Locations$Instances$Isupgradeable, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        isUpgradeable(params: Params$Resource$Projects$Locations$Instances$Isupgradeable, options: MethodOptions | BodyResponseCallback<Schema$IsInstanceUpgradeableResponse>, callback: BodyResponseCallback<Schema$IsInstanceUpgradeableResponse>): void;
        isUpgradeable(params: Params$Resource$Projects$Locations$Instances$Isupgradeable, callback: BodyResponseCallback<Schema$IsInstanceUpgradeableResponse>): void;
        isUpgradeable(callback: BodyResponseCallback<Schema$IsInstanceUpgradeableResponse>): void;
        /**
         * Lists instances in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Instances$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Instances$List, options?: MethodOptions): GaxiosPromise<Schema$ListInstancesResponse>;
        list(params: Params$Resource$Projects$Locations$Instances$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Instances$List, options: MethodOptions | BodyResponseCallback<Schema$ListInstancesResponse>, callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Instances$List, callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        /**
         * Migrates an existing User-Managed Notebook to Workbench Instances.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        migrate(params: Params$Resource$Projects$Locations$Instances$Migrate, options: StreamMethodOptions): GaxiosPromise<Readable>;
        migrate(params?: Params$Resource$Projects$Locations$Instances$Migrate, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        migrate(params: Params$Resource$Projects$Locations$Instances$Migrate, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        migrate(params: Params$Resource$Projects$Locations$Instances$Migrate, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        migrate(params: Params$Resource$Projects$Locations$Instances$Migrate, callback: BodyResponseCallback<Schema$Operation>): void;
        migrate(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Registers an existing legacy notebook instance to the Notebooks API server. Legacy instances are instances created with the legacy Compute Engine calls. They are not manageable by the Notebooks API out of the box. This call makes these instances manageable by the Notebooks API.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        register(params: Params$Resource$Projects$Locations$Instances$Register, options: StreamMethodOptions): GaxiosPromise<Readable>;
        register(params?: Params$Resource$Projects$Locations$Instances$Register, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        register(params: Params$Resource$Projects$Locations$Instances$Register, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        register(params: Params$Resource$Projects$Locations$Instances$Register, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        register(params: Params$Resource$Projects$Locations$Instances$Register, callback: BodyResponseCallback<Schema$Operation>): void;
        register(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Allows notebook instances to report their latest instance information to the Notebooks API server. The server will merge the reported information to the instance metadata store. Do not use this method directly.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        report(params: Params$Resource$Projects$Locations$Instances$Report, options: StreamMethodOptions): GaxiosPromise<Readable>;
        report(params?: Params$Resource$Projects$Locations$Instances$Report, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        report(params: Params$Resource$Projects$Locations$Instances$Report, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        report(params: Params$Resource$Projects$Locations$Instances$Report, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        report(params: Params$Resource$Projects$Locations$Instances$Report, callback: BodyResponseCallback<Schema$Operation>): void;
        report(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Reports and processes an instance event.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        reportEvent(params: Params$Resource$Projects$Locations$Instances$Reportevent, options: StreamMethodOptions): GaxiosPromise<Readable>;
        reportEvent(params?: Params$Resource$Projects$Locations$Instances$Reportevent, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        reportEvent(params: Params$Resource$Projects$Locations$Instances$Reportevent, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        reportEvent(params: Params$Resource$Projects$Locations$Instances$Reportevent, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        reportEvent(params: Params$Resource$Projects$Locations$Instances$Reportevent, callback: BodyResponseCallback<Schema$Operation>): void;
        reportEvent(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Resets a notebook instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        reset(params: Params$Resource$Projects$Locations$Instances$Reset, options: StreamMethodOptions): GaxiosPromise<Readable>;
        reset(params?: Params$Resource$Projects$Locations$Instances$Reset, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        reset(params: Params$Resource$Projects$Locations$Instances$Reset, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        reset(params: Params$Resource$Projects$Locations$Instances$Reset, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        reset(params: Params$Resource$Projects$Locations$Instances$Reset, callback: BodyResponseCallback<Schema$Operation>): void;
        reset(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Rollbacks a notebook instance to the previous version.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        rollback(params: Params$Resource$Projects$Locations$Instances$Rollback, options: StreamMethodOptions): GaxiosPromise<Readable>;
        rollback(params?: Params$Resource$Projects$Locations$Instances$Rollback, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        rollback(params: Params$Resource$Projects$Locations$Instances$Rollback, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        rollback(params: Params$Resource$Projects$Locations$Instances$Rollback, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        rollback(params: Params$Resource$Projects$Locations$Instances$Rollback, callback: BodyResponseCallback<Schema$Operation>): void;
        rollback(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Updates the guest accelerators of a single Instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setAccelerator(params: Params$Resource$Projects$Locations$Instances$Setaccelerator, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setAccelerator(params?: Params$Resource$Projects$Locations$Instances$Setaccelerator, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        setAccelerator(params: Params$Resource$Projects$Locations$Instances$Setaccelerator, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setAccelerator(params: Params$Resource$Projects$Locations$Instances$Setaccelerator, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        setAccelerator(params: Params$Resource$Projects$Locations$Instances$Setaccelerator, callback: BodyResponseCallback<Schema$Operation>): void;
        setAccelerator(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Instances$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Instances$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Instances$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Instances$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Instances$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Replaces all the labels of an Instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setLabels(params: Params$Resource$Projects$Locations$Instances$Setlabels, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setLabels(params?: Params$Resource$Projects$Locations$Instances$Setlabels, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        setLabels(params: Params$Resource$Projects$Locations$Instances$Setlabels, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setLabels(params: Params$Resource$Projects$Locations$Instances$Setlabels, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        setLabels(params: Params$Resource$Projects$Locations$Instances$Setlabels, callback: BodyResponseCallback<Schema$Operation>): void;
        setLabels(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Updates the machine type of a single Instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setMachineType(params: Params$Resource$Projects$Locations$Instances$Setmachinetype, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setMachineType(params?: Params$Resource$Projects$Locations$Instances$Setmachinetype, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        setMachineType(params: Params$Resource$Projects$Locations$Instances$Setmachinetype, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setMachineType(params: Params$Resource$Projects$Locations$Instances$Setmachinetype, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        setMachineType(params: Params$Resource$Projects$Locations$Instances$Setmachinetype, callback: BodyResponseCallback<Schema$Operation>): void;
        setMachineType(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Starts a notebook instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        start(params: Params$Resource$Projects$Locations$Instances$Start, options: StreamMethodOptions): GaxiosPromise<Readable>;
        start(params?: Params$Resource$Projects$Locations$Instances$Start, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        start(params: Params$Resource$Projects$Locations$Instances$Start, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        start(params: Params$Resource$Projects$Locations$Instances$Start, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        start(params: Params$Resource$Projects$Locations$Instances$Start, callback: BodyResponseCallback<Schema$Operation>): void;
        start(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Stops a notebook instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        stop(params: Params$Resource$Projects$Locations$Instances$Stop, options: StreamMethodOptions): GaxiosPromise<Readable>;
        stop(params?: Params$Resource$Projects$Locations$Instances$Stop, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        stop(params: Params$Resource$Projects$Locations$Instances$Stop, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        stop(params: Params$Resource$Projects$Locations$Instances$Stop, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        stop(params: Params$Resource$Projects$Locations$Instances$Stop, callback: BodyResponseCallback<Schema$Operation>): void;
        stop(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Instances$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Instances$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$TestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Instances$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Instances$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Instances$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        /**
         * Update Notebook Instance configurations.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        updateConfig(params: Params$Resource$Projects$Locations$Instances$Updateconfig, options: StreamMethodOptions): GaxiosPromise<Readable>;
        updateConfig(params?: Params$Resource$Projects$Locations$Instances$Updateconfig, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        updateConfig(params: Params$Resource$Projects$Locations$Instances$Updateconfig, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        updateConfig(params: Params$Resource$Projects$Locations$Instances$Updateconfig, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        updateConfig(params: Params$Resource$Projects$Locations$Instances$Updateconfig, callback: BodyResponseCallback<Schema$Operation>): void;
        updateConfig(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Add/update metadata items for an instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        updateMetadataItems(params: Params$Resource$Projects$Locations$Instances$Updatemetadataitems, options: StreamMethodOptions): GaxiosPromise<Readable>;
        updateMetadataItems(params?: Params$Resource$Projects$Locations$Instances$Updatemetadataitems, options?: MethodOptions): GaxiosPromise<Schema$UpdateInstanceMetadataItemsResponse>;
        updateMetadataItems(params: Params$Resource$Projects$Locations$Instances$Updatemetadataitems, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        updateMetadataItems(params: Params$Resource$Projects$Locations$Instances$Updatemetadataitems, options: MethodOptions | BodyResponseCallback<Schema$UpdateInstanceMetadataItemsResponse>, callback: BodyResponseCallback<Schema$UpdateInstanceMetadataItemsResponse>): void;
        updateMetadataItems(params: Params$Resource$Projects$Locations$Instances$Updatemetadataitems, callback: BodyResponseCallback<Schema$UpdateInstanceMetadataItemsResponse>): void;
        updateMetadataItems(callback: BodyResponseCallback<Schema$UpdateInstanceMetadataItemsResponse>): void;
        /**
         * Updates the Shielded instance configuration of a single Instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        updateShieldedInstanceConfig(params: Params$Resource$Projects$Locations$Instances$Updateshieldedinstanceconfig, options: StreamMethodOptions): GaxiosPromise<Readable>;
        updateShieldedInstanceConfig(params?: Params$Resource$Projects$Locations$Instances$Updateshieldedinstanceconfig, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        updateShieldedInstanceConfig(params: Params$Resource$Projects$Locations$Instances$Updateshieldedinstanceconfig, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        updateShieldedInstanceConfig(params: Params$Resource$Projects$Locations$Instances$Updateshieldedinstanceconfig, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        updateShieldedInstanceConfig(params: Params$Resource$Projects$Locations$Instances$Updateshieldedinstanceconfig, callback: BodyResponseCallback<Schema$Operation>): void;
        updateShieldedInstanceConfig(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Upgrades a notebook instance to the latest version.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        upgrade(params: Params$Resource$Projects$Locations$Instances$Upgrade, options: StreamMethodOptions): GaxiosPromise<Readable>;
        upgrade(params?: Params$Resource$Projects$Locations$Instances$Upgrade, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        upgrade(params: Params$Resource$Projects$Locations$Instances$Upgrade, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        upgrade(params: Params$Resource$Projects$Locations$Instances$Upgrade, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        upgrade(params: Params$Resource$Projects$Locations$Instances$Upgrade, callback: BodyResponseCallback<Schema$Operation>): void;
        upgrade(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Allows notebook instances to call this endpoint to upgrade themselves. Do not use this method directly.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        upgradeInternal(params: Params$Resource$Projects$Locations$Instances$Upgradeinternal, options: StreamMethodOptions): GaxiosPromise<Readable>;
        upgradeInternal(params?: Params$Resource$Projects$Locations$Instances$Upgradeinternal, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        upgradeInternal(params: Params$Resource$Projects$Locations$Instances$Upgradeinternal, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        upgradeInternal(params: Params$Resource$Projects$Locations$Instances$Upgradeinternal, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        upgradeInternal(params: Params$Resource$Projects$Locations$Instances$Upgradeinternal, callback: BodyResponseCallback<Schema$Operation>): void;
        upgradeInternal(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Instances$Create extends StandardParameters {
        /**
         * Required. User-defined unique ID of this instance.
         */
        instanceId?: string;
        /**
         * Required. Format: `parent=projects/{project_id\}/locations/{location\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Instance;
    }
    export interface Params$Resource$Projects$Locations$Instances$Delete extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Diagnose extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DiagnoseInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Get extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Getinstancehealth extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Isupgradeable extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        notebookInstance?: string;
        /**
         * Optional. The optional UpgradeType. Setting this field will search for additional compute images to upgrade this instance.
         */
        type?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$List extends StandardParameters {
        /**
         * Optional. List filter.
         */
        filter?: string;
        /**
         * Optional. Sort results. Supported values are "name", "name desc" or "" (unsorted).
         */
        orderBy?: string;
        /**
         * Maximum return size of the list call.
         */
        pageSize?: number;
        /**
         * A previous returned page token that can be used to continue listing from the last result.
         */
        pageToken?: string;
        /**
         * Required. Format: `parent=projects/{project_id\}/locations/{location\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Migrate extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$MigrateInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Register extends StandardParameters {
        /**
         * Required. Format: `parent=projects/{project_id\}/locations/{location\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RegisterInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Report extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ReportInstanceInfoRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Reportevent extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ReportInstanceEventRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Reset extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ResetInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Rollback extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RollbackInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Setaccelerator extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetInstanceAcceleratorRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Setlabels extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetInstanceLabelsRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Setmachinetype extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetInstanceMachineTypeRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Start extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$StartInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Stop extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$StopInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Updateconfig extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UpdateInstanceConfigRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Updatemetadataitems extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UpdateInstanceMetadataItemsRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Updateshieldedinstanceconfig extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UpdateShieldedInstanceConfigRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Upgrade extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UpgradeInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Upgradeinternal extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UpgradeInstanceInternalRequest;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Projects$Locations$Operations$Cancel, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Operations$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$ListOperationsResponse>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelOperationRequest;
    }
    export interface Params$Resource$Projects$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Runtimes {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new Runtime in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Runtimes$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Runtimes$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Runtimes$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Runtimes$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Runtimes$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a single Runtime.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Runtimes$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Runtimes$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Runtimes$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Runtimes$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Runtimes$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Creates a Diagnostic File and runs Diagnostic Tool given a Runtime.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        diagnose(params: Params$Resource$Projects$Locations$Runtimes$Diagnose, options: StreamMethodOptions): GaxiosPromise<Readable>;
        diagnose(params?: Params$Resource$Projects$Locations$Runtimes$Diagnose, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        diagnose(params: Params$Resource$Projects$Locations$Runtimes$Diagnose, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        diagnose(params: Params$Resource$Projects$Locations$Runtimes$Diagnose, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        diagnose(params: Params$Resource$Projects$Locations$Runtimes$Diagnose, callback: BodyResponseCallback<Schema$Operation>): void;
        diagnose(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single Runtime. The location must be a regional endpoint rather than zonal.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Runtimes$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Runtimes$Get, options?: MethodOptions): GaxiosPromise<Schema$Runtime>;
        get(params: Params$Resource$Projects$Locations$Runtimes$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Runtimes$Get, options: MethodOptions | BodyResponseCallback<Schema$Runtime>, callback: BodyResponseCallback<Schema$Runtime>): void;
        get(params: Params$Resource$Projects$Locations$Runtimes$Get, callback: BodyResponseCallback<Schema$Runtime>): void;
        get(callback: BodyResponseCallback<Schema$Runtime>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Runtimes$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Runtimes$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Runtimes$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Runtimes$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Runtimes$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Lists Runtimes in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Runtimes$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Runtimes$List, options?: MethodOptions): GaxiosPromise<Schema$ListRuntimesResponse>;
        list(params: Params$Resource$Projects$Locations$Runtimes$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Runtimes$List, options: MethodOptions | BodyResponseCallback<Schema$ListRuntimesResponse>, callback: BodyResponseCallback<Schema$ListRuntimesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Runtimes$List, callback: BodyResponseCallback<Schema$ListRuntimesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListRuntimesResponse>): void;
        /**
         * Migrate an existing Runtime to a new Workbench Instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        migrate(params: Params$Resource$Projects$Locations$Runtimes$Migrate, options: StreamMethodOptions): GaxiosPromise<Readable>;
        migrate(params?: Params$Resource$Projects$Locations$Runtimes$Migrate, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        migrate(params: Params$Resource$Projects$Locations$Runtimes$Migrate, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        migrate(params: Params$Resource$Projects$Locations$Runtimes$Migrate, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        migrate(params: Params$Resource$Projects$Locations$Runtimes$Migrate, callback: BodyResponseCallback<Schema$Operation>): void;
        migrate(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Update Notebook Runtime configuration.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Runtimes$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Runtimes$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Runtimes$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Runtimes$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Runtimes$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets an access token for the consumer service account that the customer attached to the runtime. Only accessible from the tenant instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        refreshRuntimeTokenInternal(params: Params$Resource$Projects$Locations$Runtimes$Refreshruntimetokeninternal, options: StreamMethodOptions): GaxiosPromise<Readable>;
        refreshRuntimeTokenInternal(params?: Params$Resource$Projects$Locations$Runtimes$Refreshruntimetokeninternal, options?: MethodOptions): GaxiosPromise<Schema$RefreshRuntimeTokenInternalResponse>;
        refreshRuntimeTokenInternal(params: Params$Resource$Projects$Locations$Runtimes$Refreshruntimetokeninternal, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        refreshRuntimeTokenInternal(params: Params$Resource$Projects$Locations$Runtimes$Refreshruntimetokeninternal, options: MethodOptions | BodyResponseCallback<Schema$RefreshRuntimeTokenInternalResponse>, callback: BodyResponseCallback<Schema$RefreshRuntimeTokenInternalResponse>): void;
        refreshRuntimeTokenInternal(params: Params$Resource$Projects$Locations$Runtimes$Refreshruntimetokeninternal, callback: BodyResponseCallback<Schema$RefreshRuntimeTokenInternalResponse>): void;
        refreshRuntimeTokenInternal(callback: BodyResponseCallback<Schema$RefreshRuntimeTokenInternalResponse>): void;
        /**
         * Reports and processes a runtime event.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        reportEvent(params: Params$Resource$Projects$Locations$Runtimes$Reportevent, options: StreamMethodOptions): GaxiosPromise<Readable>;
        reportEvent(params?: Params$Resource$Projects$Locations$Runtimes$Reportevent, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        reportEvent(params: Params$Resource$Projects$Locations$Runtimes$Reportevent, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        reportEvent(params: Params$Resource$Projects$Locations$Runtimes$Reportevent, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        reportEvent(params: Params$Resource$Projects$Locations$Runtimes$Reportevent, callback: BodyResponseCallback<Schema$Operation>): void;
        reportEvent(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Resets a Managed Notebook Runtime.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        reset(params: Params$Resource$Projects$Locations$Runtimes$Reset, options: StreamMethodOptions): GaxiosPromise<Readable>;
        reset(params?: Params$Resource$Projects$Locations$Runtimes$Reset, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        reset(params: Params$Resource$Projects$Locations$Runtimes$Reset, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        reset(params: Params$Resource$Projects$Locations$Runtimes$Reset, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        reset(params: Params$Resource$Projects$Locations$Runtimes$Reset, callback: BodyResponseCallback<Schema$Operation>): void;
        reset(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Runtimes$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Runtimes$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$Policy>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Runtimes$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Runtimes$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Runtimes$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Starts a Managed Notebook Runtime. Perform "Start" on GPU instances; "Resume" on CPU instances See: https://cloud.google.com/compute/docs/instances/stop-start-instance https://cloud.google.com/compute/docs/instances/suspend-resume-instance
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        start(params: Params$Resource$Projects$Locations$Runtimes$Start, options: StreamMethodOptions): GaxiosPromise<Readable>;
        start(params?: Params$Resource$Projects$Locations$Runtimes$Start, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        start(params: Params$Resource$Projects$Locations$Runtimes$Start, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        start(params: Params$Resource$Projects$Locations$Runtimes$Start, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        start(params: Params$Resource$Projects$Locations$Runtimes$Start, callback: BodyResponseCallback<Schema$Operation>): void;
        start(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Stops a Managed Notebook Runtime. Perform "Stop" on GPU instances; "Suspend" on CPU instances See: https://cloud.google.com/compute/docs/instances/stop-start-instance https://cloud.google.com/compute/docs/instances/suspend-resume-instance
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        stop(params: Params$Resource$Projects$Locations$Runtimes$Stop, options: StreamMethodOptions): GaxiosPromise<Readable>;
        stop(params?: Params$Resource$Projects$Locations$Runtimes$Stop, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        stop(params: Params$Resource$Projects$Locations$Runtimes$Stop, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        stop(params: Params$Resource$Projects$Locations$Runtimes$Stop, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        stop(params: Params$Resource$Projects$Locations$Runtimes$Stop, callback: BodyResponseCallback<Schema$Operation>): void;
        stop(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Switch a Managed Notebook Runtime.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        switch(params: Params$Resource$Projects$Locations$Runtimes$Switch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        switch(params?: Params$Resource$Projects$Locations$Runtimes$Switch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        switch(params: Params$Resource$Projects$Locations$Runtimes$Switch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        switch(params: Params$Resource$Projects$Locations$Runtimes$Switch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        switch(params: Params$Resource$Projects$Locations$Runtimes$Switch, callback: BodyResponseCallback<Schema$Operation>): void;
        switch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Runtimes$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Runtimes$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$TestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Runtimes$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Runtimes$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Runtimes$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        /**
         * Upgrades a Managed Notebook Runtime to the latest version.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        upgrade(params: Params$Resource$Projects$Locations$Runtimes$Upgrade, options: StreamMethodOptions): GaxiosPromise<Readable>;
        upgrade(params?: Params$Resource$Projects$Locations$Runtimes$Upgrade, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        upgrade(params: Params$Resource$Projects$Locations$Runtimes$Upgrade, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        upgrade(params: Params$Resource$Projects$Locations$Runtimes$Upgrade, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        upgrade(params: Params$Resource$Projects$Locations$Runtimes$Upgrade, callback: BodyResponseCallback<Schema$Operation>): void;
        upgrade(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Create extends StandardParameters {
        /**
         * Required. Format: `parent=projects/{project_id\}/locations/{location\}`
         */
        parent?: string;
        /**
         * Idempotent request UUID.
         */
        requestId?: string;
        /**
         * Required. User-defined unique ID of this Runtime.
         */
        runtimeId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Runtime;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Delete extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/runtimes/{runtime_id\}`
         */
        name?: string;
        /**
         * Idempotent request UUID.
         */
        requestId?: string;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Diagnose extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/runtimes/{runtimes_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DiagnoseRuntimeRequest;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Get extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/runtimes/{runtime_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$List extends StandardParameters {
        /**
         * Optional. List filter.
         */
        filter?: string;
        /**
         * Optional. Sort results. Supported values are "name", "name desc" or "" (unsorted).
         */
        orderBy?: string;
        /**
         * Maximum return size of the list call.
         */
        pageSize?: number;
        /**
         * A previous returned page token that can be used to continue listing from the last result.
         */
        pageToken?: string;
        /**
         * Required. Format: `parent=projects/{project_id\}/locations/{location\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Migrate extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/runtimes/{runtime_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$MigrateRuntimeRequest;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Patch extends StandardParameters {
        /**
         * Output only. The resource name of the runtime. Format: `projects/{project\}/locations/{location\}/runtimes/{runtimeId\}`
         */
        name?: string;
        /**
         * Idempotent request UUID.
         */
        requestId?: string;
        /**
         * Required. Specifies the path, relative to `Runtime`, of the field to update. For example, to change the software configuration kernels, the `update_mask` parameter would be specified as `software_config.kernels`, and the `PATCH` request body would specify the new value, as follows: { "software_config":{ "kernels": [{ 'repository': 'gcr.io/deeplearning-platform-release/pytorch-gpu', 'tag': 'latest' \}], \} \} Currently, only the following fields can be updated: - `software_config.kernels` - `software_config.post_startup_script` - `software_config.custom_gpu_driver_path` - `software_config.idle_shutdown` - `software_config.idle_shutdown_timeout` - `software_config.disable_terminal` - `labels`
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Runtime;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Refreshruntimetokeninternal extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/runtimes/{runtime_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RefreshRuntimeTokenInternalRequest;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Reportevent extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/runtimes/{runtime_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ReportRuntimeEventRequest;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Reset extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/runtimes/{runtime_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ResetRuntimeRequest;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Start extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/runtimes/{runtime_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$StartRuntimeRequest;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Stop extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/runtimes/{runtime_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$StopRuntimeRequest;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Switch extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/runtimes/{runtime_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SwitchRuntimeRequest;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export interface Params$Resource$Projects$Locations$Runtimes$Upgrade extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/runtimes/{runtime_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$UpgradeRuntimeRequest;
    }
    export class Resource$Projects$Locations$Schedules {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new Scheduled Notebook in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Schedules$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Schedules$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Schedules$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Schedules$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Schedules$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes schedule and all underlying jobs
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Schedules$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Schedules$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Schedules$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Schedules$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Schedules$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of schedule
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Schedules$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Schedules$Get, options?: MethodOptions): GaxiosPromise<Schema$Schedule>;
        get(params: Params$Resource$Projects$Locations$Schedules$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Schedules$Get, options: MethodOptions | BodyResponseCallback<Schema$Schedule>, callback: BodyResponseCallback<Schema$Schedule>): void;
        get(params: Params$Resource$Projects$Locations$Schedules$Get, callback: BodyResponseCallback<Schema$Schedule>): void;
        get(callback: BodyResponseCallback<Schema$Schedule>): void;
        /**
         * Lists schedules in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Schedules$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Schedules$List, options?: MethodOptions): GaxiosPromise<Schema$ListSchedulesResponse>;
        list(params: Params$Resource$Projects$Locations$Schedules$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Schedules$List, options: MethodOptions | BodyResponseCallback<Schema$ListSchedulesResponse>, callback: BodyResponseCallback<Schema$ListSchedulesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Schedules$List, callback: BodyResponseCallback<Schema$ListSchedulesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListSchedulesResponse>): void;
        /**
         * Triggers execution of an existing schedule.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        trigger(params: Params$Resource$Projects$Locations$Schedules$Trigger, options: StreamMethodOptions): GaxiosPromise<Readable>;
        trigger(params?: Params$Resource$Projects$Locations$Schedules$Trigger, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        trigger(params: Params$Resource$Projects$Locations$Schedules$Trigger, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        trigger(params: Params$Resource$Projects$Locations$Schedules$Trigger, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        trigger(params: Params$Resource$Projects$Locations$Schedules$Trigger, callback: BodyResponseCallback<Schema$Operation>): void;
        trigger(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Schedules$Create extends StandardParameters {
        /**
         * Required. Format: `parent=projects/{project_id\}/locations/{location\}`
         */
        parent?: string;
        /**
         * Required. User-defined unique ID of this schedule.
         */
        scheduleId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Schedule;
    }
    export interface Params$Resource$Projects$Locations$Schedules$Delete extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/schedules/{schedule_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Schedules$Get extends StandardParameters {
        /**
         * Required. Format: `projects/{project_id\}/locations/{location\}/schedules/{schedule_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Schedules$List extends StandardParameters {
        /**
         * Filter applied to resulting schedules.
         */
        filter?: string;
        /**
         * Field to order results by.
         */
        orderBy?: string;
        /**
         * Maximum return size of the list call.
         */
        pageSize?: number;
        /**
         * A previous returned page token that can be used to continue listing from the last result.
         */
        pageToken?: string;
        /**
         * Required. Format: `parent=projects/{project_id\}/locations/{location\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Schedules$Trigger extends StandardParameters {
        /**
         * Required. Format: `parent=projects/{project_id\}/locations/{location\}/schedules/{schedule_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TriggerScheduleRequest;
    }
    export {};
}
