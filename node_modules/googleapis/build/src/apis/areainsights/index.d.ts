/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { areainsights_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof areainsights_v1.Areainsights;
};
export declare function areainsights(version: 'v1'): areainsights_v1.Areainsights;
export declare function areainsights(options: areainsights_v1.Options): areainsights_v1.Areainsights;
declare const auth: AuthPlus;
export { auth };
export { areainsights_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
