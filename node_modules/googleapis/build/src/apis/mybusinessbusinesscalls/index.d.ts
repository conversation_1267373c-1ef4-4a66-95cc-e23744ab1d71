/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { mybusinessbusinesscalls_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof mybusinessbusinesscalls_v1.Mybusinessbusinesscalls;
};
export declare function mybusinessbusinesscalls(version: 'v1'): mybusinessbusinesscalls_v1.Mybusinessbusinesscalls;
export declare function mybusinessbusinesscalls(options: mybusinessbusinesscalls_v1.Options): mybusinessbusinesscalls_v1.Mybusinessbusinesscalls;
declare const auth: AuthPlus;
export { auth };
export { mybusinessbusinesscalls_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
