/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { digitalassetlinks_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof digitalassetlinks_v1.Digitalassetlinks;
};
export declare function digitalassetlinks(version: 'v1'): digitalassetlinks_v1.Digitalassetlinks;
export declare function digitalassetlinks(options: digitalassetlinks_v1.Options): digitalassetlinks_v1.Digitalassetlinks;
declare const auth: AuthPlus;
export { auth };
export { digitalassetlinks_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
