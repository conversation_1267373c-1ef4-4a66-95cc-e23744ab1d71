/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { androidmanagement_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof androidmanagement_v1.Androidmanagement;
};
export declare function androidmanagement(version: 'v1'): androidmanagement_v1.Androidmanagement;
export declare function androidmanagement(options: androidmanagement_v1.Options): androidmanagement_v1.Androidmanagement;
declare const auth: AuthPlus;
export { auth };
export { androidmanagement_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
