/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { testing_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof testing_v1.Testing;
};
export declare function testing(version: 'v1'): testing_v1.Testing;
export declare function testing(options: testing_v1.Options): testing_v1.Testing;
declare const auth: AuthPlus;
export { auth };
export { testing_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
