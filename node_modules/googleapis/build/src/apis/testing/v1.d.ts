/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace testing_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Cloud Testing API
     *
     * Allows developers to run automated tests for their mobile applications on Google infrastructure.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const testing = google.testing('v1');
     * ```
     */
    export class Testing {
        context: APIRequestContext;
        applicationDetailService: Resource$Applicationdetailservice;
        projects: Resource$Projects;
        testEnvironmentCatalog: Resource$Testenvironmentcatalog;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Identifies an account and how to log into it.
     */
    export interface Schema$Account {
        /**
         * An automatic google login account.
         */
        googleAuto?: Schema$GoogleAuto;
    }
    /**
     * A single Android device.
     */
    export interface Schema$AndroidDevice {
        /**
         * Required. The id of the Android device to be used. Use the TestEnvironmentDiscoveryService to get supported options.
         */
        androidModelId?: string | null;
        /**
         * Required. The id of the Android OS version to be used. Use the TestEnvironmentDiscoveryService to get supported options.
         */
        androidVersionId?: string | null;
        /**
         * Required. The locale the test device used for testing. Use the TestEnvironmentDiscoveryService to get supported options.
         */
        locale?: string | null;
        /**
         * Required. How the device is oriented during the test. Use the TestEnvironmentDiscoveryService to get supported options.
         */
        orientation?: string | null;
    }
    /**
     * The currently supported Android devices.
     */
    export interface Schema$AndroidDeviceCatalog {
        /**
         * The set of supported Android device models.
         */
        models?: Schema$AndroidModel[];
        /**
         * The set of supported runtime configurations.
         */
        runtimeConfiguration?: Schema$AndroidRuntimeConfiguration;
        /**
         * The set of supported Android OS versions.
         */
        versions?: Schema$AndroidVersion[];
    }
    /**
     * A list of Android device configurations in which the test is to be executed.
     */
    export interface Schema$AndroidDeviceList {
        /**
         * Required. A list of Android devices.
         */
        androidDevices?: Schema$AndroidDevice[];
    }
    /**
     * A test of an Android application that can control an Android component independently of its normal lifecycle. Android instrumentation tests run an application APK and test APK inside the same process on a virtual or physical AndroidDevice. They also specify a test runner class, such as com.google.GoogleTestRunner, which can vary on the specific instrumentation framework chosen. See for more information on types of Android tests.
     */
    export interface Schema$AndroidInstrumentationTest {
        /**
         * The APK for the application under test.
         */
        appApk?: Schema$FileReference;
        /**
         * A multi-apk app bundle for the application under test.
         */
        appBundle?: Schema$AppBundle;
        /**
         * The java package for the application under test. The default value is determined by examining the application's manifest.
         */
        appPackageId?: string | null;
        /**
         * The option of whether running each test within its own invocation of instrumentation with Android Test Orchestrator or not. ** Orchestrator is only compatible with AndroidJUnitRunner version 1.1 or higher! ** Orchestrator offers the following benefits: - No shared state - Crashes are isolated - Logs are scoped per test See for more information about Android Test Orchestrator. If not set, the test will be run without the orchestrator.
         */
        orchestratorOption?: string | null;
        /**
         * The option to run tests in multiple shards in parallel.
         */
        shardingOption?: Schema$ShardingOption;
        /**
         * Required. The APK containing the test code to be executed.
         */
        testApk?: Schema$FileReference;
        /**
         * The java package for the test to be executed. The default value is determined by examining the application's manifest.
         */
        testPackageId?: string | null;
        /**
         * The InstrumentationTestRunner class. The default value is determined by examining the application's manifest.
         */
        testRunnerClass?: string | null;
        /**
         * Each target must be fully qualified with the package name or class name, in one of these formats: - "package package_name" - "class package_name.class_name" - "class package_name.class_name#method_name" If empty, all targets in the module will be run.
         */
        testTargets?: string[] | null;
    }
    /**
     * A set of Android device configuration permutations is defined by the the cross-product of the given axes. Internally, the given AndroidMatrix will be expanded into a set of AndroidDevices. Only supported permutations will be instantiated. Invalid permutations (e.g., incompatible models/versions) are ignored.
     */
    export interface Schema$AndroidMatrix {
        /**
         * Required. The ids of the set of Android device to be used. Use the TestEnvironmentDiscoveryService to get supported options.
         */
        androidModelIds?: string[] | null;
        /**
         * Required. The ids of the set of Android OS version to be used. Use the TestEnvironmentDiscoveryService to get supported options.
         */
        androidVersionIds?: string[] | null;
        /**
         * Required. The set of locales the test device will enable for testing. Use the TestEnvironmentDiscoveryService to get supported options.
         */
        locales?: string[] | null;
        /**
         * Required. The set of orientations to test with. Use the TestEnvironmentDiscoveryService to get supported options.
         */
        orientations?: string[] | null;
    }
    /**
     * A description of an Android device tests may be run on.
     */
    export interface Schema$AndroidModel {
        /**
         * Reasons for access denial. This model is accessible if this list is empty, otherwise the model is viewable only.
         */
        accessDeniedReasons?: string[] | null;
        /**
         * The company that this device is branded with. Example: "Google", "Samsung".
         */
        brand?: string | null;
        /**
         * The name of the industrial design. This corresponds to android.os.Build.DEVICE.
         */
        codename?: string | null;
        /**
         * Whether this device is virtual or physical.
         */
        form?: string | null;
        /**
         * Whether this device is a phone, tablet, wearable, etc.
         */
        formFactor?: string | null;
        /**
         * The unique opaque id for this model. Use this for invoking the TestExecutionService.
         */
        id?: string | null;
        /**
         * Output only. Lab info of this device.
         */
        labInfo?: Schema$LabInfo;
        /**
         * True if and only if tests with this model are recorded by stitching together screenshots. See use_low_spec_video_recording in device config.
         */
        lowFpsVideoRecording?: boolean | null;
        /**
         * The manufacturer of this device.
         */
        manufacturer?: string | null;
        /**
         * The human-readable marketing name for this device model. Examples: "Nexus 5", "Galaxy S5".
         */
        name?: string | null;
        /**
         * Version-specific information of an Android model.
         */
        perVersionInfo?: Schema$PerAndroidVersionInfo[];
        /**
         * Screen density in DPI. This corresponds to ro.sf.lcd_density
         */
        screenDensity?: number | null;
        /**
         * Screen size in the horizontal (X) dimension measured in pixels.
         */
        screenX?: number | null;
        /**
         * Screen size in the vertical (Y) dimension measured in pixels.
         */
        screenY?: number | null;
        /**
         * The list of supported ABIs for this device. This corresponds to either android.os.Build.SUPPORTED_ABIS (for API level 21 and above) or android.os.Build.CPU_ABI/CPU_ABI2. The most preferred ABI is the first element in the list. Elements are optionally prefixed by "version_id:" (where version_id is the id of an AndroidVersion), denoting an ABI that is supported only on a particular version.
         */
        supportedAbis?: string[] | null;
        /**
         * The set of Android versions this device supports.
         */
        supportedVersionIds?: string[] | null;
        /**
         * Tags for this dimension. Examples: "default", "preview", "deprecated".
         */
        tags?: string[] | null;
        /**
         * URL of a thumbnail image (photo) of the device.
         */
        thumbnailUrl?: string | null;
    }
    /**
     * A test of an android application that explores the application on a virtual or physical Android Device, finding culprits and crashes as it goes.
     */
    export interface Schema$AndroidRoboTest {
        /**
         * The APK for the application under test.
         */
        appApk?: Schema$FileReference;
        /**
         * A multi-apk app bundle for the application under test.
         */
        appBundle?: Schema$AppBundle;
        /**
         * The initial activity that should be used to start the app.
         */
        appInitialActivity?: string | null;
        /**
         * The java package for the application under test. The default value is determined by examining the application's manifest.
         */
        appPackageId?: string | null;
        /**
         * The max depth of the traversal stack Robo can explore. Needs to be at least 2 to make Robo explore the app beyond the first activity. Default is 50.
         */
        maxDepth?: number | null;
        /**
         * The max number of steps Robo can execute. Default is no limit.
         */
        maxSteps?: number | null;
        /**
         * A set of directives Robo should apply during the crawl. This allows users to customize the crawl. For example, the username and password for a test account can be provided.
         */
        roboDirectives?: Schema$RoboDirective[];
        /**
         * The mode in which Robo should run. Most clients should allow the server to populate this field automatically.
         */
        roboMode?: string | null;
        /**
         * A JSON file with a sequence of actions Robo should perform as a prologue for the crawl.
         */
        roboScript?: Schema$FileReference;
        /**
         * The intents used to launch the app for the crawl. If none are provided, then the main launcher activity is launched. If some are provided, then only those provided are launched (the main launcher activity must be provided explicitly).
         */
        startingIntents?: Schema$RoboStartingIntent[];
    }
    /**
     * Android configuration that can be selected at the time a test is run.
     */
    export interface Schema$AndroidRuntimeConfiguration {
        /**
         * The set of available locales.
         */
        locales?: Schema$Locale[];
        /**
         * The set of available orientations.
         */
        orientations?: Schema$Orientation[];
    }
    /**
     * A test of an Android Application with a Test Loop. The intent \ will be implicitly added, since Games is the only user of this api, for the time being.
     */
    export interface Schema$AndroidTestLoop {
        /**
         * The APK for the application under test.
         */
        appApk?: Schema$FileReference;
        /**
         * A multi-apk app bundle for the application under test.
         */
        appBundle?: Schema$AppBundle;
        /**
         * The java package for the application under test. The default is determined by examining the application's manifest.
         */
        appPackageId?: string | null;
        /**
         * The list of scenario labels that should be run during the test. The scenario labels should map to labels defined in the application's manifest. For example, player_experience and com.google.test.loops.player_experience add all of the loops labeled in the manifest with the com.google.test.loops.player_experience name to the execution. Scenarios can also be specified in the scenarios field.
         */
        scenarioLabels?: string[] | null;
        /**
         * The list of scenarios that should be run during the test. The default is all test loops, derived from the application's manifest.
         */
        scenarios?: number[] | null;
    }
    /**
     * A version of the Android OS.
     */
    export interface Schema$AndroidVersion {
        /**
         * The API level for this Android version. Examples: 18, 19.
         */
        apiLevel?: number | null;
        /**
         * The code name for this Android version. Examples: "JellyBean", "KitKat".
         */
        codeName?: string | null;
        /**
         * Market share for this version.
         */
        distribution?: Schema$Distribution;
        /**
         * An opaque id for this Android version. Use this id to invoke the TestExecutionService.
         */
        id?: string | null;
        /**
         * The date this Android version became available in the market.
         */
        releaseDate?: Schema$Date;
        /**
         * Tags for this dimension. Examples: "default", "preview", "deprecated".
         */
        tags?: string[] | null;
        /**
         * A string representing this version of the Android OS. Examples: "4.3", "4.4".
         */
        versionString?: string | null;
    }
    /**
     * An Android package file to install.
     */
    export interface Schema$Apk {
        /**
         * The path to an APK to be installed on the device before the test begins.
         */
        location?: Schema$FileReference;
        /**
         * The java package for the APK to be installed. Value is determined by examining the application's manifest.
         */
        packageName?: string | null;
    }
    /**
     * Android application details based on application manifest and archive contents.
     */
    export interface Schema$ApkDetail {
        apkManifest?: Schema$ApkManifest;
    }
    /**
     * An Android app manifest. See http://developer.android.com/guide/topics/manifest/manifest-intro.html
     */
    export interface Schema$ApkManifest {
        /**
         * User-readable name for the application.
         */
        applicationLabel?: string | null;
        intentFilters?: Schema$IntentFilter[];
        /**
         * Maximum API level on which the application is designed to run.
         */
        maxSdkVersion?: number | null;
        /**
         * Meta-data tags defined in the manifest.
         */
        metadata?: Schema$Metadata[];
        /**
         * Minimum API level required for the application to run.
         */
        minSdkVersion?: number | null;
        /**
         * Full Java-style package name for this application, e.g. "com.example.foo".
         */
        packageName?: string | null;
        /**
         * Services contained in the tag.
         */
        services?: Schema$Service[];
        /**
         * Specifies the API Level on which the application is designed to run.
         */
        targetSdkVersion?: number | null;
        /**
         * Feature usage tags defined in the manifest.
         */
        usesFeature?: Schema$UsesFeature[];
        usesPermission?: string[] | null;
        /**
         * Permissions declared to be used by the application
         */
        usesPermissionTags?: Schema$UsesPermissionTag[];
        /**
         * Version number used internally by the app.
         */
        versionCode?: string | null;
        /**
         * Version number shown to users.
         */
        versionName?: string | null;
    }
    /**
     * An Android App Bundle file format, containing a BundleConfig.pb file, a base module directory, zero or more dynamic feature module directories. See https://developer.android.com/guide/app-bundle/build for guidance on building App Bundles.
     */
    export interface Schema$AppBundle {
        /**
         * .aab file representing the app bundle under test.
         */
        bundleLocation?: Schema$FileReference;
    }
    /**
     * The request object for cancelling a Device Session.
     */
    export interface Schema$CancelDeviceSessionRequest {
    }
    /**
     * Response containing the current state of the specified test matrix.
     */
    export interface Schema$CancelTestMatrixResponse {
        /**
         * The current rolled-up state of the test matrix. If this state is already final, then the cancelation request will have no effect.
         */
        testState?: string | null;
    }
    /**
     * Information about the client which invoked the test.
     */
    export interface Schema$ClientInfo {
        /**
         * The list of detailed information about client.
         */
        clientInfoDetails?: Schema$ClientInfoDetail[];
        /**
         * Required. Client name, such as gcloud.
         */
        name?: string | null;
    }
    /**
     * Key-value pair of detailed information about the client which invoked the test. Examples: {'Version', '1.0'\}, {'Release Track', 'BETA'\}.
     */
    export interface Schema$ClientInfoDetail {
        /**
         * Required. The key of detailed client information.
         */
        key?: string | null;
        /**
         * Required. The value of detailed client information.
         */
        value?: string | null;
    }
    /**
     * Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp
     */
    export interface Schema$Date {
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        day?: number | null;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        month?: number | null;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        year?: number | null;
    }
    /**
     * A single device file description.
     */
    export interface Schema$DeviceFile {
        /**
         * A reference to an opaque binary blob file.
         */
        obbFile?: Schema$ObbFile;
        /**
         * A reference to a regular file.
         */
        regularFile?: Schema$RegularFile;
    }
    /**
     * A single device IP block
     */
    export interface Schema$DeviceIpBlock {
        /**
         * The date this block was added to Firebase Test Lab
         */
        addedDate?: Schema$Date;
        /**
         * An IP address block in CIDR notation eg: ************/29
         */
        block?: string | null;
        /**
         * Whether this block is used by physical or virtual devices
         */
        form?: string | null;
    }
    /**
     * List of IP blocks used by the Firebase Test Lab
     */
    export interface Schema$DeviceIpBlockCatalog {
        /**
         * The device IP blocks used by Firebase Test Lab
         */
        ipBlocks?: Schema$DeviceIpBlock[];
    }
    /**
     * Protobuf message describing the device message, used from several RPCs.
     */
    export interface Schema$DeviceSession {
        /**
         * Output only. The timestamp that the session first became ACTIVE.
         */
        activeStartTime?: string | null;
        /**
         * Required. The requested device
         */
        androidDevice?: Schema$AndroidDevice;
        /**
         * Output only. The time that the Session was created.
         */
        createTime?: string | null;
        /**
         * Output only. The title of the DeviceSession to be presented in the UI.
         */
        displayName?: string | null;
        /**
         * Optional. If the device is still in use at this time, any connections will be ended and the SessionState will transition from ACTIVE to FINISHED.
         */
        expireTime?: string | null;
        /**
         * Output only. The interval of time that this device must be interacted with before it transitions from ACTIVE to TIMEOUT_INACTIVITY.
         */
        inactivityTimeout?: string | null;
        /**
         * Optional. Name of the DeviceSession, e.g. "projects/{project_id\}/deviceSessions/{session_id\}"
         */
        name?: string | null;
        /**
         * Output only. Current state of the DeviceSession.
         */
        state?: string | null;
        /**
         * Output only. The historical state transitions of the session_state message including the current session state.
         */
        stateHistories?: Schema$SessionStateEvent[];
        /**
         * Optional. The amount of time that a device will be initially allocated for. This can eventually be extended with the UpdateDeviceSession RPC. Default: 15 minutes.
         */
        ttl?: string | null;
    }
    /**
     * Denotes whether Direct Access is supported, and by which client versions. DirectAccessService is currently available as a preview to select developers. You can register today on behalf of you and your team at https://developer.android.com/studio/preview/android-device-streaming
     */
    export interface Schema$DirectAccessVersionInfo {
        /**
         * Whether direct access is supported at all. Clients are expected to filter down the device list to only android models and versions which support Direct Access when that is the user intent.
         */
        directAccessSupported?: boolean | null;
        /**
         * Output only. Indicates client-device compatibility, where a device is known to work only with certain workarounds implemented in the Android Studio client. Expected format "major.minor.micro.patch", e.g. "5921.22.2211.8881706".
         */
        minimumAndroidStudioVersion?: string | null;
    }
    /**
     * Data about the relative number of devices running a given configuration of the Android platform.
     */
    export interface Schema$Distribution {
        /**
         * Output only. The estimated fraction (0-1) of the total market with this configuration.
         */
        marketShare?: number | null;
        /**
         * Output only. The time this distribution was measured.
         */
        measurementTime?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * The environment in which the test is run.
     */
    export interface Schema$Environment {
        /**
         * An Android device which must be used with an Android test.
         */
        androidDevice?: Schema$AndroidDevice;
        /**
         * An iOS device which must be used with an iOS test.
         */
        iosDevice?: Schema$IosDevice;
    }
    /**
     * The matrix of environments in which the test is to be executed.
     */
    export interface Schema$EnvironmentMatrix {
        /**
         * A list of Android devices; the test will be run only on the specified devices.
         */
        androidDeviceList?: Schema$AndroidDeviceList;
        /**
         * A matrix of Android devices.
         */
        androidMatrix?: Schema$AndroidMatrix;
        /**
         * A list of iOS devices.
         */
        iosDeviceList?: Schema$IosDeviceList;
    }
    /**
     * A key-value pair passed as an environment variable to the test.
     */
    export interface Schema$EnvironmentVariable {
        /**
         * Key for the environment variable.
         */
        key?: string | null;
        /**
         * Value for the environment variable.
         */
        value?: string | null;
    }
    /**
     * A reference to a file, used for user inputs.
     */
    export interface Schema$FileReference {
        /**
         * A path to a file in Google Cloud Storage. Example: gs://build-app-*************/app%40debug-unaligned.apk These paths are expected to be url encoded (percent encoding)
         */
        gcsPath?: string | null;
    }
    /**
     * Response containing the details of the specified Android application.
     */
    export interface Schema$GetApkDetailsResponse {
        /**
         * Details of the Android App.
         */
        apkDetail?: Schema$ApkDetail;
    }
    /**
     * Enables automatic Google account login. If set, the service automatically generates a Google test account and adds it to the device, before executing the test. Note that test accounts might be reused. Many applications show their full set of functionalities when an account is present on the device. Logging into the device with these generated accounts allows testing more functionalities.
     */
    export interface Schema$GoogleAuto {
    }
    /**
     * A storage location within Google cloud storage (GCS).
     */
    export interface Schema$GoogleCloudStorage {
        /**
         * Required. The path to a directory in GCS that will eventually contain the results for this test. The requesting user must have write access on the bucket in the supplied path.
         */
        gcsPath?: string | null;
    }
    /**
     * The section of an tag. https://developer.android.com/guide/topics/manifest/intent-filter-element.html
     */
    export interface Schema$IntentFilter {
        /**
         * The android:name value of the tag.
         */
        actionNames?: string[] | null;
        /**
         * The android:name value of the tag.
         */
        categoryNames?: string[] | null;
        /**
         * The android:mimeType value of the tag.
         */
        mimeType?: string | null;
    }
    /**
     * A single iOS device.
     */
    export interface Schema$IosDevice {
        /**
         * Required. The id of the iOS device to be used. Use the TestEnvironmentDiscoveryService to get supported options.
         */
        iosModelId?: string | null;
        /**
         * Required. The id of the iOS major software version to be used. Use the TestEnvironmentDiscoveryService to get supported options.
         */
        iosVersionId?: string | null;
        /**
         * Required. The locale the test device used for testing. Use the TestEnvironmentDiscoveryService to get supported options.
         */
        locale?: string | null;
        /**
         * Required. How the device is oriented during the test. Use the TestEnvironmentDiscoveryService to get supported options.
         */
        orientation?: string | null;
    }
    /**
     * The currently supported iOS devices.
     */
    export interface Schema$IosDeviceCatalog {
        /**
         * The set of supported iOS device models.
         */
        models?: Schema$IosModel[];
        /**
         * The set of supported runtime configurations.
         */
        runtimeConfiguration?: Schema$IosRuntimeConfiguration;
        /**
         * The set of supported iOS software versions.
         */
        versions?: Schema$IosVersion[];
        /**
         * The set of supported Xcode versions.
         */
        xcodeVersions?: Schema$XcodeVersion[];
    }
    /**
     * A file or directory to install on the device before the test starts.
     */
    export interface Schema$IosDeviceFile {
        /**
         * The bundle id of the app where this file lives. iOS apps sandbox their own filesystem, so app files must specify which app installed on the device.
         */
        bundleId?: string | null;
        /**
         * The source file
         */
        content?: Schema$FileReference;
        /**
         * Location of the file on the device, inside the app's sandboxed filesystem
         */
        devicePath?: string | null;
    }
    /**
     * A list of iOS device configurations in which the test is to be executed.
     */
    export interface Schema$IosDeviceList {
        /**
         * Required. A list of iOS devices.
         */
        iosDevices?: Schema$IosDevice[];
    }
    /**
     * A description of an iOS device tests may be run on.
     */
    export interface Schema$IosModel {
        /**
         * Device capabilities. Copied from https://developer.apple.com/library/archive/documentation/DeviceInformation/Reference/iOSDeviceCompatibility/DeviceCompatibilityMatrix/DeviceCompatibilityMatrix.html
         */
        deviceCapabilities?: string[] | null;
        /**
         * Whether this device is a phone, tablet, wearable, etc.
         */
        formFactor?: string | null;
        /**
         * The unique opaque id for this model. Use this for invoking the TestExecutionService.
         */
        id?: string | null;
        /**
         * The human-readable name for this device model. Examples: "iPhone 4s", "iPad Mini 2".
         */
        name?: string | null;
        /**
         * Version-specific information of an iOS model.
         */
        perVersionInfo?: Schema$PerIosVersionInfo[];
        /**
         * Screen density in DPI.
         */
        screenDensity?: number | null;
        /**
         * Screen size in the horizontal (X) dimension measured in pixels.
         */
        screenX?: number | null;
        /**
         * Screen size in the vertical (Y) dimension measured in pixels.
         */
        screenY?: number | null;
        /**
         * The set of iOS major software versions this device supports.
         */
        supportedVersionIds?: string[] | null;
        /**
         * Tags for this dimension. Examples: "default", "preview", "deprecated".
         */
        tags?: string[] | null;
    }
    /**
     * A test that explores an iOS application on an iOS device.
     */
    export interface Schema$IosRoboTest {
        /**
         * The bundle ID for the app-under-test. This is determined by examining the application's "Info.plist" file.
         */
        appBundleId?: string | null;
        /**
         * Required. The ipa stored at this file should be used to run the test.
         */
        appIpa?: Schema$FileReference;
        /**
         * An optional Roboscript to customize the crawl. See https://firebase.google.com/docs/test-lab/android/robo-scripts-reference for more information about Roboscripts. The maximum allowed file size of the roboscript is 10MiB.
         */
        roboScript?: Schema$FileReference;
    }
    /**
     * iOS configuration that can be selected at the time a test is run.
     */
    export interface Schema$IosRuntimeConfiguration {
        /**
         * The set of available locales.
         */
        locales?: Schema$Locale[];
        /**
         * The set of available orientations.
         */
        orientations?: Schema$Orientation[];
    }
    /**
     * A test of an iOS application that implements one or more game loop scenarios. This test type accepts an archived application (.ipa file) and a list of integer scenarios that will be executed on the app sequentially.
     */
    export interface Schema$IosTestLoop {
        /**
         * Output only. The bundle id for the application under test.
         */
        appBundleId?: string | null;
        /**
         * Required. The .ipa of the application to test.
         */
        appIpa?: Schema$FileReference;
        /**
         * The list of scenarios that should be run during the test. Defaults to the single scenario 0 if unspecified.
         */
        scenarios?: number[] | null;
    }
    /**
     * A description of how to set up an iOS device prior to running the test.
     */
    export interface Schema$IosTestSetup {
        /**
         * iOS apps to install in addition to those being directly tested.
         */
        additionalIpas?: Schema$FileReference[];
        /**
         * The network traffic profile used for running the test. Available network profiles can be queried by using the NETWORK_CONFIGURATION environment type when calling TestEnvironmentDiscoveryService.GetTestEnvironmentCatalog.
         */
        networkProfile?: string | null;
        /**
         * List of directories on the device to upload to Cloud Storage at the end of the test. Directories should either be in a shared directory (such as /private/var/mobile/Media) or within an accessible directory inside the app's filesystem (such as /Documents) by specifying the bundle ID.
         */
        pullDirectories?: Schema$IosDeviceFile[];
        /**
         * List of files to push to the device before starting the test.
         */
        pushFiles?: Schema$IosDeviceFile[];
    }
    /**
     * An iOS version.
     */
    export interface Schema$IosVersion {
        /**
         * An opaque id for this iOS version. Use this id to invoke the TestExecutionService.
         */
        id?: string | null;
        /**
         * An integer representing the major iOS version. Examples: "8", "9".
         */
        majorVersion?: number | null;
        /**
         * An integer representing the minor iOS version. Examples: "1", "2".
         */
        minorVersion?: number | null;
        /**
         * The available Xcode versions for this version.
         */
        supportedXcodeVersionIds?: string[] | null;
        /**
         * Tags for this dimension. Examples: "default", "preview", "deprecated".
         */
        tags?: string[] | null;
    }
    /**
     * A test of an iOS application that uses the XCTest framework. Xcode supports the option to "build for testing", which generates an .xctestrun file that contains a test specification (arguments, test methods, etc). This test type accepts a zip file containing the .xctestrun file and the corresponding contents of the Build/Products directory that contains all the binaries needed to run the tests.
     */
    export interface Schema$IosXcTest {
        /**
         * Output only. The bundle id for the application under test.
         */
        appBundleId?: string | null;
        /**
         * The option to test special app entitlements. Setting this would re-sign the app having special entitlements with an explicit application-identifier. Currently supports testing aps-environment entitlement.
         */
        testSpecialEntitlements?: boolean | null;
        /**
         * Required. The .zip containing the .xctestrun file and the contents of the DerivedData/Build/Products directory. The .xctestrun file in this zip is ignored if the xctestrun field is specified.
         */
        testsZip?: Schema$FileReference;
        /**
         * The Xcode version that should be used for the test. Use the TestEnvironmentDiscoveryService to get supported options. Defaults to the latest Xcode version Firebase Test Lab supports.
         */
        xcodeVersion?: string | null;
        /**
         * An .xctestrun file that will override the .xctestrun file in the tests zip. Because the .xctestrun file contains environment variables along with test methods to run and/or ignore, this can be useful for sharding tests. Default is taken from the tests zip.
         */
        xctestrun?: Schema$FileReference;
    }
    /**
     * Lab specific information for a device.
     */
    export interface Schema$LabInfo {
        /**
         * Lab name where the device is hosted. If empty, the device is hosted in a Google owned lab.
         */
        name?: string | null;
        /**
         * The Unicode country/region code (CLDR) of the lab where the device is hosted. E.g. "US" for United States, "CH" for Switzerland.
         */
        regionCode?: string | null;
    }
    /**
     * Specifies an intent that starts the main launcher activity.
     */
    export interface Schema$LauncherActivityIntent {
    }
    /**
     * A list of device sessions.
     */
    export interface Schema$ListDeviceSessionsResponse {
        /**
         * The sessions matching the specified filter in the given cloud project.
         */
        deviceSessions?: Schema$DeviceSession[];
        /**
         * A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
    }
    /**
     * A location/region designation for language.
     */
    export interface Schema$Locale {
        /**
         * The id for this locale. Example: "en_US".
         */
        id?: string | null;
        /**
         * A human-friendly name for this language/locale. Example: "English".
         */
        name?: string | null;
        /**
         * A human-friendly string representing the region for this locale. Example: "United States". Not present for every locale.
         */
        region?: string | null;
        /**
         * Tags for this dimension. Example: "default".
         */
        tags?: string[] | null;
    }
    /**
     * Shards test cases into the specified groups of packages, classes, and/or methods. With manual sharding enabled, specifying test targets via environment_variables or in InstrumentationTest is invalid.
     */
    export interface Schema$ManualSharding {
        /**
         * Required. Group of packages, classes, and/or test methods to be run for each manually-created shard. You must specify at least one shard if this field is present. When you select one or more physical devices, the number of repeated test_targets_for_shard must be <= 50. When you select one or more ARM virtual devices, it must be <= 200. When you select only x86 virtual devices, it must be <= 500.
         */
        testTargetsForShard?: Schema$TestTargetsForShard[];
    }
    /**
     * Describes a single error or issue with a matrix.
     */
    export interface Schema$MatrixErrorDetail {
        /**
         * Output only. A human-readable message about how the error in the TestMatrix. Expands on the `reason` field with additional details and possible options to fix the issue.
         */
        message?: string | null;
        /**
         * Output only. The reason for the error. This is a constant value in UPPER_SNAKE_CASE that identifies the cause of the error.
         */
        reason?: string | null;
    }
    /**
     * A tag within a manifest. https://developer.android.com/guide/topics/manifest/meta-data-element.html
     */
    export interface Schema$Metadata {
        /**
         * The android:name value
         */
        name?: string | null;
        /**
         * The android:value value
         */
        value?: string | null;
    }
    export interface Schema$NetworkConfiguration {
        /**
         * The emulation rule applying to the download traffic.
         */
        downRule?: Schema$TrafficRule;
        /**
         * The unique opaque id for this network traffic configuration.
         */
        id?: string | null;
        /**
         * The emulation rule applying to the upload traffic.
         */
        upRule?: Schema$TrafficRule;
    }
    export interface Schema$NetworkConfigurationCatalog {
        configurations?: Schema$NetworkConfiguration[];
    }
    /**
     * Skips the starting activity
     */
    export interface Schema$NoActivityIntent {
    }
    /**
     * An opaque binary blob file to install on the device before the test starts.
     */
    export interface Schema$ObbFile {
        /**
         * Required. Opaque Binary Blob (OBB) file(s) to install on the device.
         */
        obb?: Schema$FileReference;
        /**
         * Required. OBB file name which must conform to the format as specified by Android e.g. [main|patch].0300110.com.example.android.obb which will be installed into \/Android/obb/\/ on the device.
         */
        obbFileName?: string | null;
    }
    /**
     * Screen orientation of the device.
     */
    export interface Schema$Orientation {
        /**
         * The id for this orientation. Example: "portrait".
         */
        id?: string | null;
        /**
         * A human-friendly name for this orientation. Example: "portrait".
         */
        name?: string | null;
        /**
         * Tags for this dimension. Example: "default".
         */
        tags?: string[] | null;
    }
    /**
     * A version-specific information of an Android model.
     */
    export interface Schema$PerAndroidVersionInfo {
        /**
         * The number of online devices for an Android version.
         */
        deviceCapacity?: string | null;
        /**
         * Output only. Identifies supported clients for DirectAccess for this Android version.
         */
        directAccessVersionInfo?: Schema$DirectAccessVersionInfo;
        /**
         * Output only. The estimated wait time for a single interactive device session using Direct Access.
         */
        interactiveDeviceAvailabilityEstimate?: string | null;
        /**
         * An Android version.
         */
        versionId?: string | null;
    }
    /**
     * A version-specific information of an iOS model.
     */
    export interface Schema$PerIosVersionInfo {
        /**
         * The number of online devices for an iOS version.
         */
        deviceCapacity?: string | null;
        /**
         * An iOS version.
         */
        versionId?: string | null;
    }
    /**
     * The currently provided software environment on the devices under test.
     */
    export interface Schema$ProvidedSoftwareCatalog {
        /**
         * A string representing the current version of AndroidX Test Orchestrator that is used in the environment. The package is available at https://maven.google.com/web/index.html#androidx.test:orchestrator.
         */
        androidxOrchestratorVersion?: string | null;
        /**
         * Deprecated: Use AndroidX Test Orchestrator going forward. A string representing the current version of Android Test Orchestrator that is used in the environment. The package is available at https://maven.google.com/web/index.html#com.android.support.test:orchestrator.
         */
        orchestratorVersion?: string | null;
    }
    /**
     * A file or directory to install on the device before the test starts.
     */
    export interface Schema$RegularFile {
        /**
         * Required. The source file.
         */
        content?: Schema$FileReference;
        /**
         * Required. Where to put the content on the device. Must be an absolute, allowlisted path. If the file exists, it will be replaced. The following device-side directories and any of their subdirectories are allowlisted: ${EXTERNAL_STORAGE\}, /sdcard, or /storage ${ANDROID_DATA\}/local/tmp, or /data/local/tmp Specifying a path outside of these directory trees is invalid. The paths /sdcard and /data will be made available and treated as implicit path substitutions. E.g. if /sdcard on a particular device does not map to external storage, the system will replace it with the external storage path prefix for that device and copy the file there. It is strongly advised to use the Environment API in app and test code to access files on the device in a portable way.
         */
        devicePath?: string | null;
    }
    /**
     * Locations where the results of running the test are stored.
     */
    export interface Schema$ResultStorage {
        /**
         * Required.
         */
        googleCloudStorage?: Schema$GoogleCloudStorage;
        /**
         * Output only. URL to the results in the Firebase Web Console.
         */
        resultsUrl?: string | null;
        /**
         * Output only. The tool results execution that results are written to.
         */
        toolResultsExecution?: Schema$ToolResultsExecution;
        /**
         * The tool results history that contains the tool results execution that results are written to. If not provided, the service will choose an appropriate value.
         */
        toolResultsHistory?: Schema$ToolResultsHistory;
    }
    /**
     * Directs Robo to interact with a specific UI element if it is encountered during the crawl. Currently, Robo can perform text entry or element click.
     */
    export interface Schema$RoboDirective {
        /**
         * Required. The type of action that Robo should perform on the specified element.
         */
        actionType?: string | null;
        /**
         * The text that Robo is directed to set. If left empty, the directive will be treated as a CLICK on the element matching the resource_name.
         */
        inputText?: string | null;
        /**
         * Required. The android resource name of the target UI element. For example, in Java: R.string.foo in xml: @string/foo Only the "foo" part is needed. Reference doc: https://developer.android.com/guide/topics/resources/accessing-resources.html
         */
        resourceName?: string | null;
    }
    /**
     * Message for specifying the start activities to crawl.
     */
    export interface Schema$RoboStartingIntent {
        /**
         * An intent that starts the main launcher activity.
         */
        launcherActivity?: Schema$LauncherActivityIntent;
        /**
         * Skips the starting activity
         */
        noActivity?: Schema$NoActivityIntent;
        /**
         * An intent that starts an activity with specific details.
         */
        startActivity?: Schema$StartActivityIntent;
        /**
         * Timeout in seconds for each intent.
         */
        timeout?: string | null;
    }
    /**
     * The section of an tag. https://developer.android.com/guide/topics/manifest/service-element
     */
    export interface Schema$Service {
        /**
         * Intent filters in the service
         */
        intentFilter?: Schema$IntentFilter[];
        /**
         * The android:name value
         */
        name?: string | null;
    }
    /**
     * A message encapsulating a series of Session states and the time that the DeviceSession first entered those states.
     */
    export interface Schema$SessionStateEvent {
        /**
         * Output only. The time that the session_state first encountered that state.
         */
        eventTime?: string | null;
        /**
         * Output only. The session_state tracked by this event
         */
        sessionState?: string | null;
        /**
         * Output only. A human-readable message to explain the state.
         */
        stateMessage?: string | null;
    }
    /**
     * Output only. Details about the shard.
     */
    export interface Schema$Shard {
        /**
         * Output only. The estimated shard duration based on previous test case timing records, if available.
         */
        estimatedShardDuration?: string | null;
        /**
         * Output only. The total number of shards.
         */
        numShards?: number | null;
        /**
         * Output only. The index of the shard among all the shards.
         */
        shardIndex?: number | null;
        /**
         * Output only. Test targets for each shard. Only set for manual sharding.
         */
        testTargetsForShard?: Schema$TestTargetsForShard;
    }
    /**
     * Options for enabling sharding.
     */
    export interface Schema$ShardingOption {
        /**
         * Shards test cases into the specified groups of packages, classes, and/or methods.
         */
        manualSharding?: Schema$ManualSharding;
        /**
         * Shards test based on previous test case timing records.
         */
        smartSharding?: Schema$SmartSharding;
        /**
         * Uniformly shards test cases given a total number of shards.
         */
        uniformSharding?: Schema$UniformSharding;
    }
    /**
     * Shards test based on previous test case timing records.
     */
    export interface Schema$SmartSharding {
        /**
         * The amount of time tests within a shard should take. Default: 300 seconds (5 minutes). The minimum allowed: 120 seconds (2 minutes). The shard count is dynamically set based on time, up to the maximum shard limit (described below). To guarantee at least one test case for each shard, the number of shards will not exceed the number of test cases. Shard duration will be exceeded if: - The maximum shard limit is reached and there is more calculated test time remaining to allocate into shards. - Any individual test is estimated to be longer than the targeted shard duration. Shard duration is not guaranteed because smart sharding uses test case history and default durations which may not be accurate. The rules for finding the test case timing records are: - If the service has processed a test case in the last 30 days, the record of the latest successful test case will be used. - For new test cases, the average duration of other known test cases will be used. - If there are no previous test case timing records available, the default test case duration is 15 seconds. Because the actual shard duration can exceed the targeted shard duration, we recommend that you set the targeted value at least 5 minutes less than the maximum allowed test timeout (45 minutes for physical devices and 60 minutes for virtual), or that you use the custom test timeout value that you set. This approach avoids cancelling the shard before all tests can finish. Note that there is a limit for maximum number of shards. When you select one or more physical devices, the number of shards must be <= 50. When you select one or more ARM virtual devices, it must be <= 200. When you select only x86 virtual devices, it must be <= 500. To guarantee at least one test case for per shard, the number of shards will not exceed the number of test cases. Each shard created counts toward daily test quota.
         */
        targetedShardDuration?: string | null;
    }
    /**
     * A starting intent specified by an action, uri, and categories.
     */
    export interface Schema$StartActivityIntent {
        /**
         * Action name. Required for START_ACTIVITY.
         */
        action?: string | null;
        /**
         * Intent categories to set on the intent.
         */
        categories?: string[] | null;
        /**
         * URI for the action.
         */
        uri?: string | null;
    }
    export interface Schema$SystraceSetup {
        /**
         * Systrace duration in seconds. Should be between 1 and 30 seconds. 0 disables systrace.
         */
        durationSeconds?: number | null;
    }
    /**
     * Additional details about the progress of the running test.
     */
    export interface Schema$TestDetails {
        /**
         * Output only. If the TestState is ERROR, then this string will contain human-readable details about the error.
         */
        errorMessage?: string | null;
        /**
         * Output only. Human-readable, detailed descriptions of the test's progress. For example: "Provisioning a device", "Starting Test". During the course of execution new data may be appended to the end of progress_messages.
         */
        progressMessages?: string[] | null;
    }
    /**
     * A description of a test environment.
     */
    export interface Schema$TestEnvironmentCatalog {
        /**
         * Supported Android devices.
         */
        androidDeviceCatalog?: Schema$AndroidDeviceCatalog;
        /**
         * The IP blocks used by devices in the test environment.
         */
        deviceIpBlockCatalog?: Schema$DeviceIpBlockCatalog;
        /**
         * Supported iOS devices.
         */
        iosDeviceCatalog?: Schema$IosDeviceCatalog;
        /**
         * Supported network configurations.
         */
        networkConfigurationCatalog?: Schema$NetworkConfigurationCatalog;
        /**
         * The software test environment provided by TestExecutionService.
         */
        softwareCatalog?: Schema$ProvidedSoftwareCatalog;
    }
    /**
     * A single test executed in a single environment.
     */
    export interface Schema$TestExecution {
        /**
         * Output only. How the host machine(s) are configured.
         */
        environment?: Schema$Environment;
        /**
         * Output only. Unique id set by the service.
         */
        id?: string | null;
        /**
         * Output only. Id of the containing TestMatrix.
         */
        matrixId?: string | null;
        /**
         * Output only. The cloud project that owns the test execution.
         */
        projectId?: string | null;
        /**
         * Output only. Details about the shard.
         */
        shard?: Schema$Shard;
        /**
         * Output only. Indicates the current progress of the test execution (e.g., FINISHED).
         */
        state?: string | null;
        /**
         * Output only. Additional details about the running test.
         */
        testDetails?: Schema$TestDetails;
        /**
         * Output only. How to run the test.
         */
        testSpecification?: Schema$TestSpecification;
        /**
         * Output only. The time this test execution was initially created.
         */
        timestamp?: string | null;
        /**
         * Output only. Where the results for this execution are written.
         */
        toolResultsStep?: Schema$ToolResultsStep;
    }
    /**
     * TestMatrix captures all details about a test. It contains the environment configuration, test specification, test executions and overall state and outcome.
     */
    export interface Schema$TestMatrix {
        /**
         * Information about the client which invoked the test.
         */
        clientInfo?: Schema$ClientInfo;
        /**
         * Required. The devices the tests are being executed on.
         */
        environmentMatrix?: Schema$EnvironmentMatrix;
        /**
         * Output only. Details about why a matrix was deemed invalid. If multiple checks can be safely performed, they will be reported but no assumptions should be made about the length of this list.
         */
        extendedInvalidMatrixDetails?: Schema$MatrixErrorDetail[];
        /**
         * If true, only a single attempt at most will be made to run each execution/shard in the matrix. Flaky test attempts are not affected. Normally, 2 or more attempts are made if a potential infrastructure issue is detected. This feature is for latency sensitive workloads. The incidence of execution failures may be significantly greater for fail-fast matrices and support is more limited because of that expectation.
         */
        failFast?: boolean | null;
        /**
         * The number of times a TestExecution should be re-attempted if one or more of its test cases fail for any reason. The maximum number of reruns allowed is 10. Default is 0, which implies no reruns.
         */
        flakyTestAttempts?: number | null;
        /**
         * Output only. Describes why the matrix is considered invalid. Only useful for matrices in the INVALID state.
         */
        invalidMatrixDetails?: string | null;
        /**
         * Output Only. The overall outcome of the test. Only set when the test matrix state is FINISHED.
         */
        outcomeSummary?: string | null;
        /**
         * The cloud project that owns the test matrix.
         */
        projectId?: string | null;
        /**
         * Required. Where the results for the matrix are written.
         */
        resultStorage?: Schema$ResultStorage;
        /**
         * Output only. Indicates the current progress of the test matrix.
         */
        state?: string | null;
        /**
         * Output only. The list of test executions that the service creates for this matrix.
         */
        testExecutions?: Schema$TestExecution[];
        /**
         * Output only. Unique id set by the service.
         */
        testMatrixId?: string | null;
        /**
         * Required. How to run the test.
         */
        testSpecification?: Schema$TestSpecification;
        /**
         * Output only. The time this test matrix was initially created.
         */
        timestamp?: string | null;
    }
    /**
     * A description of how to set up the Android device prior to running the test.
     */
    export interface Schema$TestSetup {
        /**
         * The device will be logged in on this account for the duration of the test.
         */
        account?: Schema$Account;
        /**
         * APKs to install in addition to those being directly tested. These will be installed after the app under test. Currently capped at 100.
         */
        additionalApks?: Schema$Apk[];
        /**
         * List of directories on the device to upload to GCS at the end of the test; they must be absolute paths under /sdcard, /storage or /data/local/tmp. Path names are restricted to characters a-z A-Z 0-9 _ - . + and / Note: The paths /sdcard and /data will be made available and treated as implicit path substitutions. E.g. if /sdcard on a particular device does not map to external storage, the system will replace it with the external storage path prefix for that device.
         */
        directoriesToPull?: string[] | null;
        /**
         * Whether to prevent all runtime permissions to be granted at app install
         */
        dontAutograntPermissions?: boolean | null;
        /**
         * Environment variables to set for the test (only applicable for instrumentation tests).
         */
        environmentVariables?: Schema$EnvironmentVariable[];
        /**
         * List of files to push to the device before starting the test.
         */
        filesToPush?: Schema$DeviceFile[];
        /**
         * Optional. Initial setup APKs to install before the app under test is installed. Currently capped at 100.
         */
        initialSetupApks?: Schema$Apk[];
        /**
         * The network traffic profile used for running the test. Available network profiles can be queried by using the NETWORK_CONFIGURATION environment type when calling TestEnvironmentDiscoveryService.GetTestEnvironmentCatalog.
         */
        networkProfile?: string | null;
        /**
         * Systrace configuration for the run. Deprecated: Systrace used Python 2 which was sunsetted on 2020-01-01. Systrace is no longer supported in the Cloud Testing API, and no Systrace file will be provided in the results.
         */
        systrace?: Schema$SystraceSetup;
    }
    /**
     * A description of how to run the test.
     */
    export interface Schema$TestSpecification {
        /**
         * An Android instrumentation test.
         */
        androidInstrumentationTest?: Schema$AndroidInstrumentationTest;
        /**
         * An Android robo test.
         */
        androidRoboTest?: Schema$AndroidRoboTest;
        /**
         * An Android Application with a Test Loop.
         */
        androidTestLoop?: Schema$AndroidTestLoop;
        /**
         * Disables performance metrics recording. May reduce test latency.
         */
        disablePerformanceMetrics?: boolean | null;
        /**
         * Disables video recording. May reduce test latency.
         */
        disableVideoRecording?: boolean | null;
        /**
         * An iOS Robo test.
         */
        iosRoboTest?: Schema$IosRoboTest;
        /**
         * An iOS application with a test loop.
         */
        iosTestLoop?: Schema$IosTestLoop;
        /**
         * Test setup requirements for iOS.
         */
        iosTestSetup?: Schema$IosTestSetup;
        /**
         * An iOS XCTest, via an .xctestrun file.
         */
        iosXcTest?: Schema$IosXcTest;
        /**
         * Test setup requirements for Android e.g. files to install, bootstrap scripts.
         */
        testSetup?: Schema$TestSetup;
        /**
         * Max time a test execution is allowed to run before it is automatically cancelled. The default value is 5 min.
         */
        testTimeout?: string | null;
    }
    /**
     * Test targets for a shard.
     */
    export interface Schema$TestTargetsForShard {
        /**
         * Group of packages, classes, and/or test methods to be run for each shard. The targets need to be specified in AndroidJUnitRunner argument format. For example, "package com.my.packages" "class com.my.package.MyClass". The number of test_targets must be greater than 0.
         */
        testTargets?: string[] | null;
    }
    /**
     * Represents a tool results execution resource. This has the results of a TestMatrix.
     */
    export interface Schema$ToolResultsExecution {
        /**
         * Output only. A tool results execution ID.
         */
        executionId?: string | null;
        /**
         * Output only. A tool results history ID.
         */
        historyId?: string | null;
        /**
         * Output only. The cloud project that owns the tool results execution.
         */
        projectId?: string | null;
    }
    /**
     * Represents a tool results history resource.
     */
    export interface Schema$ToolResultsHistory {
        /**
         * Required. A tool results history ID.
         */
        historyId?: string | null;
        /**
         * Required. The cloud project that owns the tool results history.
         */
        projectId?: string | null;
    }
    /**
     * Represents a tool results step resource. This has the results of a TestExecution.
     */
    export interface Schema$ToolResultsStep {
        /**
         * Output only. A tool results execution ID.
         */
        executionId?: string | null;
        /**
         * Output only. A tool results history ID.
         */
        historyId?: string | null;
        /**
         * Output only. The cloud project that owns the tool results step.
         */
        projectId?: string | null;
        /**
         * Output only. A tool results step ID.
         */
        stepId?: string | null;
    }
    /**
     * Network emulation parameters.
     */
    export interface Schema$TrafficRule {
        /**
         * Bandwidth in kbits/second.
         */
        bandwidth?: number | null;
        /**
         * Burst size in kbits.
         */
        burst?: number | null;
        /**
         * Packet delay, must be \>= 0.
         */
        delay?: string | null;
        /**
         * Packet duplication ratio (0.0 - 1.0).
         */
        packetDuplicationRatio?: number | null;
        /**
         * Packet loss ratio (0.0 - 1.0).
         */
        packetLossRatio?: number | null;
    }
    /**
     * Uniformly shards test cases given a total number of shards. For instrumentation tests, it will be translated to "-e numShard" and "-e shardIndex" AndroidJUnitRunner arguments. With uniform sharding enabled, specifying either of these sharding arguments via `environment_variables` is invalid. Based on the sharding mechanism AndroidJUnitRunner uses, there is no guarantee that test cases will be distributed uniformly across all shards.
     */
    export interface Schema$UniformSharding {
        /**
         * Required. The total number of shards to create. This must always be a positive number that is no greater than the total number of test cases. When you select one or more physical devices, the number of shards must be <= 50. When you select one or more ARM virtual devices, it must be <= 200. When you select only x86 virtual devices, it must be <= 500.
         */
        numShards?: number | null;
    }
    /**
     * A tag within a manifest. https://developer.android.com/guide/topics/manifest/uses-feature-element.html
     */
    export interface Schema$UsesFeature {
        /**
         * The android:required value
         */
        isRequired?: boolean | null;
        /**
         * The android:name value
         */
        name?: string | null;
    }
    /**
     * The tag within a manifest. https://developer.android.com/guide/topics/manifest/uses-permission-element.html
     */
    export interface Schema$UsesPermissionTag {
        /**
         * The android:name value
         */
        maxSdkVersion?: number | null;
        /**
         * The android:name value
         */
        name?: string | null;
    }
    /**
     * An Xcode version that an iOS version is compatible with.
     */
    export interface Schema$XcodeVersion {
        /**
         * Tags for this Xcode version. Example: "default".
         */
        tags?: string[] | null;
        /**
         * The id for this version. Example: "9.2".
         */
        version?: string | null;
    }
    export class Resource$Applicationdetailservice {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the details of an Android application APK.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getApkDetails(params: Params$Resource$Applicationdetailservice$Getapkdetails, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getApkDetails(params?: Params$Resource$Applicationdetailservice$Getapkdetails, options?: MethodOptions): GaxiosPromise<Schema$GetApkDetailsResponse>;
        getApkDetails(params: Params$Resource$Applicationdetailservice$Getapkdetails, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getApkDetails(params: Params$Resource$Applicationdetailservice$Getapkdetails, options: MethodOptions | BodyResponseCallback<Schema$GetApkDetailsResponse>, callback: BodyResponseCallback<Schema$GetApkDetailsResponse>): void;
        getApkDetails(params: Params$Resource$Applicationdetailservice$Getapkdetails, callback: BodyResponseCallback<Schema$GetApkDetailsResponse>): void;
        getApkDetails(callback: BodyResponseCallback<Schema$GetApkDetailsResponse>): void;
    }
    export interface Params$Resource$Applicationdetailservice$Getapkdetails extends StandardParameters {
        /**
         * A path to a file in Google Cloud Storage. Example: gs://build-app-*************/app%40debug-unaligned.apk These paths are expected to be url encoded (percent encoding)
         */
        'bundleLocation.gcsPath'?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$FileReference;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        deviceSessions: Resource$Projects$Devicesessions;
        testMatrices: Resource$Projects$Testmatrices;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Devicesessions {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * POST /v1/projects/{project_id\}/deviceSessions/{device_session_id\}:cancel Changes the DeviceSession to state FINISHED and terminates all connections. Canceled sessions are not deleted and can be retrieved or listed by the user until they expire based on the 28 day deletion policy.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Devicesessions$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Projects$Devicesessions$Cancel, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        cancel(params: Params$Resource$Projects$Devicesessions$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Devicesessions$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Devicesessions$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * POST /v1/projects/{project_id\}/deviceSessions
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Devicesessions$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Devicesessions$Create, options?: MethodOptions): GaxiosPromise<Schema$DeviceSession>;
        create(params: Params$Resource$Projects$Devicesessions$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Devicesessions$Create, options: MethodOptions | BodyResponseCallback<Schema$DeviceSession>, callback: BodyResponseCallback<Schema$DeviceSession>): void;
        create(params: Params$Resource$Projects$Devicesessions$Create, callback: BodyResponseCallback<Schema$DeviceSession>): void;
        create(callback: BodyResponseCallback<Schema$DeviceSession>): void;
        /**
         * GET /v1/projects/{project_id\}/deviceSessions/{device_session_id\} Return a DeviceSession, which documents the allocation status and whether the device is allocated. Clients making requests from this API must poll GetDeviceSession.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Devicesessions$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Devicesessions$Get, options?: MethodOptions): GaxiosPromise<Schema$DeviceSession>;
        get(params: Params$Resource$Projects$Devicesessions$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Devicesessions$Get, options: MethodOptions | BodyResponseCallback<Schema$DeviceSession>, callback: BodyResponseCallback<Schema$DeviceSession>): void;
        get(params: Params$Resource$Projects$Devicesessions$Get, callback: BodyResponseCallback<Schema$DeviceSession>): void;
        get(callback: BodyResponseCallback<Schema$DeviceSession>): void;
        /**
         * GET /v1/projects/{project_id\}/deviceSessions Lists device Sessions owned by the project user.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Devicesessions$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Devicesessions$List, options?: MethodOptions): GaxiosPromise<Schema$ListDeviceSessionsResponse>;
        list(params: Params$Resource$Projects$Devicesessions$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Devicesessions$List, options: MethodOptions | BodyResponseCallback<Schema$ListDeviceSessionsResponse>, callback: BodyResponseCallback<Schema$ListDeviceSessionsResponse>): void;
        list(params: Params$Resource$Projects$Devicesessions$List, callback: BodyResponseCallback<Schema$ListDeviceSessionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListDeviceSessionsResponse>): void;
        /**
         * PATCH /v1/projects/{projectId\}/deviceSessions/deviceSessionId\}:updateDeviceSession Updates the current device session to the fields described by the update_mask.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Devicesessions$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Devicesessions$Patch, options?: MethodOptions): GaxiosPromise<Schema$DeviceSession>;
        patch(params: Params$Resource$Projects$Devicesessions$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Devicesessions$Patch, options: MethodOptions | BodyResponseCallback<Schema$DeviceSession>, callback: BodyResponseCallback<Schema$DeviceSession>): void;
        patch(params: Params$Resource$Projects$Devicesessions$Patch, callback: BodyResponseCallback<Schema$DeviceSession>): void;
        patch(callback: BodyResponseCallback<Schema$DeviceSession>): void;
    }
    export interface Params$Resource$Projects$Devicesessions$Cancel extends StandardParameters {
        /**
         * Required. Name of the DeviceSession, e.g. "projects/{project_id\}/deviceSessions/{session_id\}"
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelDeviceSessionRequest;
    }
    export interface Params$Resource$Projects$Devicesessions$Create extends StandardParameters {
        /**
         * Required. The Compute Engine project under which this device will be allocated. "projects/{project_id\}"
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DeviceSession;
    }
    export interface Params$Resource$Projects$Devicesessions$Get extends StandardParameters {
        /**
         * Required. Name of the DeviceSession, e.g. "projects/{project_id\}/deviceSessions/{session_id\}"
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Devicesessions$List extends StandardParameters {
        /**
         * Optional. If specified, responses will be filtered by the given filter. Allowed fields are: session_state.
         */
        filter?: string;
        /**
         * Optional. The maximum number of DeviceSessions to return.
         */
        pageSize?: number;
        /**
         * Optional. A continuation token for paging.
         */
        pageToken?: string;
        /**
         * Required. The name of the parent to request, e.g. "projects/{project_id\}"
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Devicesessions$Patch extends StandardParameters {
        /**
         * Optional. Name of the DeviceSession, e.g. "projects/{project_id\}/deviceSessions/{session_id\}"
         */
        name?: string;
        /**
         * Required. The list of fields to update.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DeviceSession;
    }
    export class Resource$Projects$Testmatrices {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Cancels unfinished test executions in a test matrix. This call returns immediately and cancellation proceeds asynchronously. If the matrix is already final, this operation will have no effect. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to read project - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the Test Matrix does not exist
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Testmatrices$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Projects$Testmatrices$Cancel, options?: MethodOptions): GaxiosPromise<Schema$CancelTestMatrixResponse>;
        cancel(params: Params$Resource$Projects$Testmatrices$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Testmatrices$Cancel, options: MethodOptions | BodyResponseCallback<Schema$CancelTestMatrixResponse>, callback: BodyResponseCallback<Schema$CancelTestMatrixResponse>): void;
        cancel(params: Params$Resource$Projects$Testmatrices$Cancel, callback: BodyResponseCallback<Schema$CancelTestMatrixResponse>): void;
        cancel(callback: BodyResponseCallback<Schema$CancelTestMatrixResponse>): void;
        /**
         * Creates and runs a matrix of tests according to the given specifications. Unsupported environments will be returned in the state UNSUPPORTED. A test matrix is limited to use at most 2000 devices in parallel. The returned matrix will not yet contain the executions that will be created for this matrix. Execution creation happens later on and will require a call to GetTestMatrix. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to write to project - INVALID_ARGUMENT - if the request is malformed or if the matrix tries to use too many simultaneous devices.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Testmatrices$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Testmatrices$Create, options?: MethodOptions): GaxiosPromise<Schema$TestMatrix>;
        create(params: Params$Resource$Projects$Testmatrices$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Testmatrices$Create, options: MethodOptions | BodyResponseCallback<Schema$TestMatrix>, callback: BodyResponseCallback<Schema$TestMatrix>): void;
        create(params: Params$Resource$Projects$Testmatrices$Create, callback: BodyResponseCallback<Schema$TestMatrix>): void;
        create(callback: BodyResponseCallback<Schema$TestMatrix>): void;
        /**
         * Checks the status of a test matrix and the executions once they are created. The test matrix will contain the list of test executions to run if and only if the resultStorage.toolResultsExecution fields have been populated. Note: Flaky test executions may be added to the matrix at a later stage. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to read project - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the Test Matrix does not exist
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Testmatrices$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Testmatrices$Get, options?: MethodOptions): GaxiosPromise<Schema$TestMatrix>;
        get(params: Params$Resource$Projects$Testmatrices$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Testmatrices$Get, options: MethodOptions | BodyResponseCallback<Schema$TestMatrix>, callback: BodyResponseCallback<Schema$TestMatrix>): void;
        get(params: Params$Resource$Projects$Testmatrices$Get, callback: BodyResponseCallback<Schema$TestMatrix>): void;
        get(callback: BodyResponseCallback<Schema$TestMatrix>): void;
    }
    export interface Params$Resource$Projects$Testmatrices$Cancel extends StandardParameters {
        /**
         * Cloud project that owns the test.
         */
        projectId?: string;
        /**
         * Test matrix that will be canceled.
         */
        testMatrixId?: string;
    }
    export interface Params$Resource$Projects$Testmatrices$Create extends StandardParameters {
        /**
         * The GCE project under which this job will run.
         */
        projectId?: string;
        /**
         * A string id used to detect duplicated requests. Ids are automatically scoped to a project, so users should ensure the ID is unique per-project. A UUID is recommended. Optional, but strongly recommended.
         */
        requestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestMatrix;
    }
    export interface Params$Resource$Projects$Testmatrices$Get extends StandardParameters {
        /**
         * Cloud project that owns the test matrix.
         */
        projectId?: string;
        /**
         * Unique test matrix id which was assigned by the service.
         */
        testMatrixId?: string;
    }
    export class Resource$Testenvironmentcatalog {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the catalog of supported test environments. May return any of the following canonical error codes: - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the environment type does not exist - INTERNAL - if an internal error occurred
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Testenvironmentcatalog$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Testenvironmentcatalog$Get, options?: MethodOptions): GaxiosPromise<Schema$TestEnvironmentCatalog>;
        get(params: Params$Resource$Testenvironmentcatalog$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Testenvironmentcatalog$Get, options: MethodOptions | BodyResponseCallback<Schema$TestEnvironmentCatalog>, callback: BodyResponseCallback<Schema$TestEnvironmentCatalog>): void;
        get(params: Params$Resource$Testenvironmentcatalog$Get, callback: BodyResponseCallback<Schema$TestEnvironmentCatalog>): void;
        get(callback: BodyResponseCallback<Schema$TestEnvironmentCatalog>): void;
    }
    export interface Params$Resource$Testenvironmentcatalog$Get extends StandardParameters {
        /**
         * Required. The type of environment that should be listed.
         */
        environmentType?: string;
        /**
         * Optional. Whether to include viewable only models in the response. This is only applicable for Android models.
         */
        includeViewableModels?: boolean;
        /**
         * For authorization, the cloud project requesting the TestEnvironmentCatalog.
         */
        projectId?: string;
    }
    export {};
}
