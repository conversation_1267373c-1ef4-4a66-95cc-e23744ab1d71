/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { oracledatabase_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof oracledatabase_v1.Oracledatabase;
};
export declare function oracledatabase(version: 'v1'): oracledatabase_v1.Oracledatabase;
export declare function oracledatabase(options: oracledatabase_v1.Options): oracledatabase_v1.Oracledatabase;
declare const auth: AuthPlus;
export { auth };
export { oracledatabase_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
